"""
Knowledge Graph Repository Implementation

This module provides an implementation of the Knowledge Graph Repository interface.
"""
import logging
import uuid
from py2neo import Node, Relationship, NodeMatcher

from linars.com.warmer.kgmaker.dal.kg_repository import KGRepository
from linars.com.warmer.kgmaker.entity.qa_entity_item import QAEntityItem
from linars.com.warmer.kgmaker.utils.graph_page_record import GraphPageRecord

class KGRepositoryImpl(KGRepository):
    """Knowledge Graph Repository implementation."""

    def __init__(self, graph, neo4j_util):
        """
        Initialize the Knowledge Graph Repository.

        Args:
            graph: The Neo4j Graph instance
            neo4j_util: The Neo4j Utility instance
        """
        self.graph = graph
        self.neo4j_util = neo4j_util
        self.logger = logging.getLogger(__name__)
        self.node_matcher = NodeMatcher(graph)

    def get_page_domain(self, query_item):
        """
        Get a page of domains.

        Args:
            query_item: The query parameters

        Returns:
            A GraphPageRecord containing the domains
        """
        result_record = GraphPageRecord()

        try:
            # 获取所有标签
            print(f"获取领域列表: query_item={query_item.__dict__}")
            labels = self.neo4j_util.get_all_labels()

            # 过滤标签
            domain = query_item.domain if hasattr(query_item, 'domain') else None
            filtered_labels = []
            if domain:
                for label in labels:
                    if domain.lower() in label.lower():
                        filtered_labels.append({"name": label})
            else:
                for label in labels:
                    filtered_labels.append({"name": label})

            print(f"过滤后的标签列表: {filtered_labels}")

            # 分页
            page_index = query_item.page_index if hasattr(query_item, 'page_index') else 1
            page_size = query_item.page_size if hasattr(query_item, 'page_size') else 10
            start = (page_index - 1) * page_size
            end = start + page_size
            print(f"分页信息: page_index={page_index}, page_size={page_size}")

            # 计算总数
            total_count = len(filtered_labels)

            # 获取当前页的数据
            page_data = filtered_labels[start:end]
            print(f"当前页的数据: {page_data}")

            # 设置结果
            result_record.set_page_index(page_index)
            result_record.set_page_size(page_size)
            result_record.set_total_count(total_count)
            result_record.set_node_list(page_data)
        except Exception as e:
            self.logger.error(f"Error getting page domain: {e}")

        return result_record

    def delete_kg_domain(self, domain):
        """
        Delete a domain.

        Args:
            domain: The domain to delete
        """
        try:
            print(f"删除领域: {domain}")
            # 删除所有关系
            cypher = f"MATCH (n:{domain})-[r]-() DELETE r"
            self.graph.run(cypher)

            # 删除所有节点
            cypher = f"MATCH (n:{domain}) DELETE n"
            self.graph.run(cypher)
            print(f"领域 {domain} 删除成功")
        except Exception as e:
            self.logger.error(f"Error deleting KG domain: {e}")
            print(f"删除领域错误: {e}")

    def get_domain_graph(self, query):
        """
        Get the graph for a domain.

        Args:
            query: The query parameters

        Returns:
            A dictionary containing the graph data
        """
        result = {}

        try:
            domain = query.domain if hasattr(query, 'domain') else None
            limit = query.limit or 100

            print(f"获取领域图谱: domain={domain}, limit={limit}")

            # 检查领域是否为空
            if not domain:
                print("领域为空，返回空结果")
                return {"nodes": [], "relationships": []}

            # 构建Cypher查询
            cypher = f"MATCH (n:{domain}) "
            if hasattr(query, 'node_name') and query.node_name:
                cypher += f"WHERE n.name CONTAINS '{query.node_name}' "
            cypher += f"WITH n LIMIT {limit} "
            cypher += f"MATCH (n)-[r]->(m) RETURN n,r,m"

            print(f"执行查询: {cypher}")

            # 执行查询
            graph_data = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphNodeAndShip")[0]

            # 设置结果
            result = graph_data
        except Exception as e:
            self.logger.error(f"Error getting domain graph: {e}")

        return result

    def get_domain_nodes(self, domain, page_index, page_size):
        """
        Get the nodes for a domain.

        Args:
            domain: The domain
            page_index: The page index
            page_size: The page size

        Returns:
            A dictionary containing the nodes
        """
        result = {}

        try:
            # 计算跳过的记录数
            skip = (page_index - 1) * page_size

            # 构建Cypher查询
            cypher = f"MATCH (n:{domain}) RETURN n SKIP {skip} LIMIT {page_size}"

            # 执行查询
            nodes = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphNode")

            # 获取总数
            cypher = f"MATCH (n:{domain}) RETURN count(n) as count"
            count_result = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphItem")
            total_count = count_result[0].get("count", 0) if count_result else 0

            # 设置结果
            result = {
                "nodes": nodes,
                "totalCount": total_count
            }
        except Exception as e:
            self.logger.error(f"Error getting domain nodes: {e}")

        return result

    def get_relation_node_count(self, domain, node_id):
        """
        Get the count of related nodes.

        Args:
            domain: The domain
            node_id: The node ID

        Returns:
            The count of related nodes
        """
        count = 0

        try:
            # 构建Cypher查询
            cypher = f"MATCH (n:{domain})-[]-() WHERE id(n)={node_id} RETURN count(*) as count"

            # 执行查询
            result = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphItem")

            # 获取结果
            count = result[0].get("count", 0) if result else 0
        except Exception as e:
            self.logger.error(f"Error getting relation node count: {e}")

        return count

    def create_domain(self, domain):
        """
        Create a domain.

        Args:
            domain: The domain to create
        """
        try:
            print(f"创建领域: {domain}")
            # 创建一个节点作为领域的根节点
            node = Node(domain, name=domain, entitytype=0)
            self.graph.create(node)
            print(f"领域 {domain} 创建成功")
        except Exception as e:
            self.logger.error(f"Error creating domain: {e}")
            print(f"创建领域错误: {e}")

    def get_more_relation_node(self, domain, node_id):
        """
        Get more related nodes.

        Args:
            domain: The domain
            node_id: The node ID

        Returns:
            A dictionary containing the related nodes
        """
        result = {}

        try:
            # 构建Cypher查询
            cypher = f"MATCH (n:{domain})-[r]->(m) WHERE id(n)={node_id} RETURN n,r,m"

            # 执行查询
            graph_data = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphNodeAndShip")[0]

            # 设置结果
            result = graph_data
        except Exception as e:
            self.logger.error(f"Error getting more relation node: {e}")

        return result

    def update_node_name(self, domain, node_id, node_name):
        """
        Update a node's name.

        Args:
            domain: The domain
            node_id: The node ID
            node_name: The new node name

        Returns:
            A dictionary containing the updated node
        """
        result = {}

        try:
            # 构建Cypher查询
            cypher = f"MATCH (n:{domain}) WHERE id(n)={node_id} SET n.name='{node_name}' RETURN n"

            # 执行查询
            nodes = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphNode")

            # 设置结果
            if nodes:
                result = nodes[0]
        except Exception as e:
            self.logger.error(f"Error updating node name: {e}")

        return result

    def create_node(self, domain, entity):
        """
        Create a node.

        Args:
            domain: The domain
            entity: The entity to create

        Returns:
            A dictionary containing the created node
        """
        result = {}

        try:
            # 创建节点
            node = Node(domain, name=entity.name)

            # 设置属性
            if entity.entity_type:
                node["entitytype"] = entity.entity_type
            if entity.color:
                node["color"] = entity.color
            if entity.r:
                node["r"] = entity.r

            # 保存节点
            self.graph.create(node)

            # 设置结果
            result = {
                "id": node.identity,
                "name": node["name"],
                "entitytype": node.get("entitytype", 0),
                "color": node.get("color", ""),
                "r": node.get("r", 30)
            }
        except Exception as e:
            self.logger.error(f"Error creating node: {e}")

        return result

    def batch_create_node(self, domain, source_name, relation, target_names):
        """
        Batch create nodes.

        Args:
            domain: The domain
            source_name: The source node name
            relation: The relation
            target_names: The target node names

        Returns:
            A dictionary containing the created nodes
        """
        result = {}

        try:
            # 查找或创建源节点
            source_node = self.node_matcher.match(domain, name=source_name).first()
            if not source_node:
                source_node = Node(domain, name=source_name)
                self.graph.create(source_node)

            # 创建目标节点并建立关系
            for target_name in target_names:
                target_node = self.node_matcher.match(domain, name=target_name).first()
                if not target_node:
                    target_node = Node(domain, name=target_name)
                    self.graph.create(target_node)

                # 创建关系
                rel = Relationship(source_node, relation, target_node)
                self.graph.create(rel)

            # 设置结果
            result = {
                "sourceId": source_node.identity,
                "sourceName": source_node["name"],
                "targetNames": target_names,
                "relation": relation
            }
        except Exception as e:
            self.logger.error(f"Error batch creating nodes: {e}")

        return result

    def batch_create_child_node(self, domain, source_id, entity_type, target_names, relation):
        """
        Batch create child nodes.

        Args:
            domain: The domain
            source_id: The source node ID
            entity_type: The entity type
            target_names: The target node names
            relation: The relation

        Returns:
            A dictionary containing the created nodes
        """
        result = {}

        try:
            # 查找源节点
            cypher = f"MATCH (n:{domain}) WHERE id(n)={source_id} RETURN n"
            source_nodes = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphNode")

            if not source_nodes:
                return result

            source_node = self.graph.nodes.get(int(source_id))

            # 创建目标节点并建立关系
            for target_name in target_names:
                target_node = self.node_matcher.match(domain, name=target_name).first()
                if not target_node:
                    target_node = Node(domain, name=target_name, entitytype=entity_type)
                    self.graph.create(target_node)

                # 创建关系
                rel = Relationship(source_node, relation, target_node)
                self.graph.create(rel)

            # 设置结果
            result = {
                "sourceId": source_node.identity,
                "sourceName": source_node["name"],
                "targetNames": target_names,
                "relation": relation
            }
        except Exception as e:
            self.logger.error(f"Error batch creating child nodes: {e}")

        return result

    def batch_create_same_node(self, domain, entity_type, source_names):
        """
        Batch create same nodes.

        Args:
            domain: The domain
            entity_type: The entity type
            source_names: The source node names

        Returns:
            A list of dictionaries containing the created nodes
        """
        results = []

        try:
            # 创建节点
            for source_name in source_names:
                node = self.node_matcher.match(domain, name=source_name).first()
                if not node:
                    node = Node(domain, name=source_name, entitytype=entity_type)
                    self.graph.create(node)

                # 添加结果
                results.append({
                    "id": node.identity,
                    "name": node["name"],
                    "entitytype": node.get("entitytype", 0)
                })
        except Exception as e:
            self.logger.error(f"Error batch creating same nodes: {e}")

        return results

    def create_link(self, domain, source_id, target_id, ship):
        """
        Create a link.

        Args:
            domain: The domain
            source_id: The source node ID
            target_id: The target node ID
            ship: The relationship

        Returns:
            A dictionary containing the created link
        """
        result = {}

        try:
            # 查找源节点和目标节点
            source_node = self.graph.nodes.get(int(source_id))
            target_node = self.graph.nodes.get(int(target_id))

            if not source_node or not target_node:
                return result

            # 创建关系
            rel = Relationship(source_node, ship, target_node)
            self.graph.create(rel)

            # 设置结果
            result = {
                "id": rel.identity,
                "source": source_id,
                "target": target_id,
                "type": ship
            }
        except Exception as e:
            self.logger.error(f"Error creating link: {e}")

        return result

    def update_link(self, ship_id, attrs):
        """
        Update a link.

        Args:
            ship_id: The link ID
            attrs: The attributes to update

        Returns:
            A dictionary containing the updated link
        """
        result = {}

        try:
            # 查找关系
            cypher = f"MATCH ()-[r]-() WHERE id(r)={ship_id} RETURN r"
            rels = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphRelationShip")

            if not rels:
                return result

            # 更新关系属性
            rel = self.graph.relationships.get(int(ship_id))

            for key, value in attrs.items():
                if key != "id" and key != "type" and key != "startNode" and key != "endNode":
                    rel[key] = value[0] if isinstance(value, list) else value

            # 保存关系
            self.graph.push(rel)

            # 设置结果
            result = {
                "id": rel.identity,
                "type": rel.type,
                "startNode": rel.start_node.identity,
                "endNode": rel.end_node.identity
            }

            # 添加其他属性
            for key, value in rel.items():
                result[key] = value
        except Exception as e:
            self.logger.error(f"Error updating link: {e}")

        return result

    def update_node(self, node_id, attrs):
        """
        Update a node.

        Args:
            node_id: The node ID
            attrs: The attributes to update

        Returns:
            A dictionary containing the updated node
        """
        result = {}

        try:
            # 查找节点
            node = self.graph.nodes.get(int(node_id))

            if not node:
                return result

            # 更新节点属性
            for key, value in attrs.items():
                if key != "id" and key != "labels":
                    node[key] = value[0] if isinstance(value, list) else value

            # 保存节点
            self.graph.push(node)

            # 设置结果
            result = {
                "id": node.identity,
                "labels": list(node.labels)
            }

            # 添加其他属性
            for key, value in node.items():
                result[key] = value
        except Exception as e:
            self.logger.error(f"Error updating node: {e}")

        return result

    def change_link(self, domain, ship_id):
        """
        Change a link.

        Args:
            domain: The domain
            ship_id: The link ID

        Returns:
            A dictionary containing the changed link
        """
        result = {}

        try:
            # 查找关系
            cypher = f"MATCH ()-[r]-() WHERE id(r)={ship_id} RETURN r"
            rels = self.neo4j_util.execute_cypher_sql(cypher, "GetGraphRelationShip")

            if not rels:
                return result

            # 更新关系类型
            rel = self.graph.relationships.get(int(ship_id))

            # 设置结果
            result = {
                "id": rel.identity,
                "type": rel.type,
                "startNode": rel.start_node.identity,
                "endNode": rel.end_node.identity
            }
        except Exception as e:
            self.logger.error(f"Error changing link: {e}")

        return result

    def delete_node(self, domain, node_id):
        """
        Delete a node.

        Args:
            domain: The domain
            node_id: The node ID
        """
        try:
            # 删除节点的所有关系
            cypher = f"MATCH (n:{domain})-[r]-() WHERE id(n)={node_id} DELETE r"
            self.graph.run(cypher)

            # 删除节点
            cypher = f"MATCH (n:{domain}) WHERE id(n)={node_id} DELETE n"
            self.graph.run(cypher)
        except Exception as e:
            self.logger.error(f"Error deleting node: {e}")

    def delete_link(self, domain, ship_id):
        """
        Delete a link.

        Args:
            domain: The domain
            ship_id: The link ID
        """
        try:
            # 删除关系
            cypher = f"MATCH ()-[r]-() WHERE id(r)={ship_id} DELETE r"
            self.graph.run(cypher)
        except Exception as e:
            self.logger.error(f"Error deleting link: {e}")

    def batch_insert_by_csv(self, domain, csv_url, skip_count):
        """
        Batch insert by CSV.

        Args:
            domain: The domain
            csv_url: The CSV URL
            skip_count: The number of rows to skip
        """
        try:
            # 构建Cypher查询
            cypher = f"""
            LOAD CSV FROM '{csv_url}' AS line
            SKIP {skip_count}
            MERGE (source:{domain} {{name: line[0]}})
            MERGE (target:{domain} {{name: line[1]}})
            MERGE (source)-[:{line[2]}]->(target)
            """

            # 执行查询
            self.graph.run(cypher)
        except Exception as e:
            self.logger.error(f"Error batch inserting by CSV: {e}")

    def update_node_file_status(self, domain, node_id, file_status):
        """
        Update a node's file status.

        Args:
            domain: The domain
            node_id: The node ID
            file_status: The file status
        """
        try:
            # 构建Cypher查询
            cypher = f"MATCH (n:{domain}) WHERE id(n)={node_id} SET n.hasfile={file_status} RETURN n"

            # 执行查询
            self.neo4j_util.execute_cypher_sql(cypher, "GetGraphNode")
        except Exception as e:
            self.logger.error(f"Error updating node file status: {e}")

    def update_corrd_of_node(self, domain, node_id, fx, fy):
        """
        Update a node's coordinates.

        Args:
            domain: The domain
            node_id: The node ID
            fx: The x coordinate
            fy: The y coordinate
        """
        try:
            # 检查参数
            if not domain:
                self.logger.error("Domain is empty, cannot update node coordinates")
                return

            if not node_id:
                self.logger.error("Node ID is empty, cannot update node coordinates")
                return

            # 构建Cypher查询
            cypher = ""

            # 处理特殊情况
            if fx is None and fy is None:
                cypher = f"MATCH (n:{domain}) WHERE id(n)={node_id} SET n.fx=null, n.fy=null RETURN n"
            elif fx == 0.0 and fy == 0.0:
                cypher = f"MATCH (n:{domain}) WHERE id(n)={node_id} SET n.fx=null, n.fy=null RETURN n"
            else:
                cypher = f"MATCH (n:{domain}) WHERE id(n)={node_id} SET n.fx={fx}, n.fy={fy} RETURN n"

            self.logger.info(f"Executing query: {cypher}")

            # 执行查询
            self.neo4j_util.execute_cypher_sql(cypher, "GetGraphNode")
        except Exception as e:
            self.logger.error(f"Error updating node coordinates: {e}")
