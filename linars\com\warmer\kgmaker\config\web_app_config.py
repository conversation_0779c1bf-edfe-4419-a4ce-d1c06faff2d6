"""
Web Application Configuration

This module provides configuration settings for the Knowledge Graph Maker application.
"""
import os

class WebAppConfig:
    """Web Application Configuration class"""

    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_key_for_kgmaker')
    DEBUG = os.environ.get('DEBUG', 'True') == 'True'

    # Neo4j配置
    NEO4J_URI = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    NEO4J_USER = os.environ.get('NEO4J_USER', 'neo4j')
    NEO4J_PASSWORD = os.environ.get('NEO4J_PASSWORD', '12345678')

    # 文件上传配置
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = {'csv', 'xls', 'xlsx'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # 获取上传文件夹路径
    @staticmethod
    def get_location():
        """获取上传文件夹路径"""
        upload_folder = WebAppConfig.UPLOAD_FOLDER
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder)
        return upload_folder

    # 检查文件扩展名是否允许
    @staticmethod
    def allowed_file(filename):
        """检查文件扩展名是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in WebAppConfig.ALLOWED_EXTENSIONS
