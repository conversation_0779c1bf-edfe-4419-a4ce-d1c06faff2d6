"""
A Hyperassociative Map is a new type of algorithm that organizes an arbitrary
graph of interconnected nodes according to its associations to other nodes.
Once a new Hyperassociative Map has been associated and aligned, nodes that
are most closely associated will be closest to each other.
"""
from typing import Dict, List, Optional, Set, Tuple, Union, Any, TypeVar, Generic, Callable
import random
import math
import threading
import numpy as np
from concurrent.futures import ExecutorService, Future

N = TypeVar('N')
E = TypeVar('E')

class HyperassociativeMap(Generic[N, E]):
    """
    A Hyperassociative Map organizes a graph of nodes according to associations.
    """
    
    # Constants
    REPULSIVE_WEAKNESS = 2.0
    ATTRACTION_STRENGTH = 4.0
    EQUILIBRIUM_ALIGNMENT_FACTOR = 0.005
    LEARNING_RATE_INCREASE_FACTOR = 0.95
    LEARNING_RATE_PROCESSING_ADJUSTMENT = 1.01
    
    DEFAULT_LEARNING_RATE = 0.4
    DEFAULT_MAX_MOVEMENT = 0.0
    DEFAULT_TOTAL_MOVEMENT = 0.0
    DEFAULT_ACCEPTABLE_DISTANCE_FACTOR = 0.85
    DEFAULT_EQUILIBRIUM_DISTANCE = 1.0
    
    class EdgeWeightToDistanceFunction:
        """Edge weight to distance function enum"""
        Min = 0
        Max = 1
        Sum = 2
        SumOneDiv = 3
        OneDivSum = 4
        OneDivSumOneDiv = 5
    
    class DistanceMetric:
        """Distance metric interface"""
        
        def get_distance(self, a: np.ndarray, b: np.ndarray, max_distance: float = float('inf')) -> float:
            """
            Calculate distance between two points
            
            Args:
                a: First point
                b: Second point
                max_distance: Maximum distance
                
            Returns:
                float: The distance
            """
            pass
        
        def subtract_if_less_than(self, a: np.ndarray, b: np.ndarray, result: np.ndarray, max_distance: float) -> float:
            """
            Subtract vectors if distance is less than max
            
            Args:
                a: First vector
                b: Second vector
                result: Result vector
                max_distance: Maximum distance
                
            Returns:
                float: The distance or POSITIVE_INFINITY
            """
            pass
    
    class Euclidean(DistanceMetric):
        """Euclidean distance metric"""
        
        def get_distance(self, a: np.ndarray, b: np.ndarray, max_distance: float = float('inf')) -> float:
            """
            Calculate Euclidean distance
            
            Args:
                a: First point
                b: Second point
                max_distance: Maximum distance
                
            Returns:
                float: The distance
            """
            max_square = max_distance * max_distance
            d = np.sum((a - b) ** 2)
            
            if d > max_square:
                return float('inf')
            
            return math.sqrt(d)
        
        def subtract_if_less_than(self, a: np.ndarray, b: np.ndarray, result: np.ndarray, max_distance: float) -> float:
            """
            Subtract vectors if distance is less than max
            
            Args:
                a: First vector
                b: Second vector
                result: Result vector
                max_distance: Maximum distance
                
            Returns:
                float: The distance or POSITIVE_INFINITY
            """
            max_distance_sq = max_distance * max_distance
            diff = a - b
            d = np.sum(diff ** 2)
            
            if d > max_distance_sq:
                return float('inf')
            
            np.copyto(result, diff)
            return math.sqrt(d)
    
    class Manhattan(DistanceMetric):
        """Manhattan distance metric"""
        
        def get_distance(self, a: np.ndarray, b: np.ndarray, max_distance: float = float('inf')) -> float:
            """
            Calculate Manhattan distance
            
            Args:
                a: First point
                b: Second point
                max_distance: Maximum distance
                
            Returns:
                float: The distance
            """
            d = np.sum(np.abs(a - b))
            
            if d > max_distance:
                return float('inf')
            
            return d
        
        def subtract_if_less_than(self, a: np.ndarray, b: np.ndarray, result: np.ndarray, max_distance: float) -> float:
            """
            Subtract vectors if distance is less than max
            
            Args:
                a: First vector
                b: Second vector
                result: Result vector
                max_distance: Maximum distance
                
            Returns:
                float: The distance or POSITIVE_INFINITY
            """
            diff = a - b
            d = np.sum(np.abs(diff))
            
            if d > max_distance:
                return float('inf')
            
            np.copyto(result, diff)
            return math.sqrt(d)
    
    # Static distance metrics
    Euclidean = Euclidean()
    Manhattan = Manhattan()
    
    def __init__(self, graph, dimensions: int, equilibrium_distance: float = DEFAULT_EQUILIBRIUM_DISTANCE, 
                 distance: DistanceMetric = None, thread_executor: ExecutorService = None):
        """
        Constructor
        
        Args:
            graph: The graph
            dimensions: Number of dimensions
            equilibrium_distance: Equilibrium distance
            distance: Distance metric
            thread_executor: Thread executor
        """
        if graph is None:
            raise ValueError("Graph cannot be null")
        
        if dimensions <= 0:
            raise ValueError("Dimensions must be 1 or more")
        
        self.graph = graph
        self.dimensions = dimensions
        self.thread_executor = thread_executor
        self.equilibrium_distance = abs(equilibrium_distance)
        self.distance_function = distance if distance else self.Euclidean
        self.zero = np.zeros(dimensions)
        
        # When distance between nodes exceeds this factor times target distance, repulsion is not applied
        self.max_repulsion_distance = 12.0
        
        # Initialize other fields
        self.learning_rate = self.DEFAULT_LEARNING_RATE
        self.max_movement = self.DEFAULT_MAX_MOVEMENT
        self.total_movement = self.DEFAULT_TOTAL_MOVEMENT
        self.acceptable_max_distance_factor = self.DEFAULT_ACCEPTABLE_DISTANCE_FACTOR
        self.edge_weight_to_distance = self.EdgeWeightToDistanceFunction.OneDivSum
        
        # Initialize coordinates
        if thread_executor:
            self.coordinates = {}  # Would be synchronized in Java
        else:
            self.coordinates = {}
        
        # Refresh all nodes
        for node in self.graph.vertex_set():
            self.coordinates[node] = self.random_coordinates(self.dimensions)
    
    def keys(self) -> Set[N]:
        """
        Get the keys
        
        Returns:
            Set: The keys
        """
        return set(self.coordinates.keys())
    
    def new_node_coordinates(self, node: N) -> np.ndarray:
        """
        Create new coordinates for a node
        
        Args:
            node: The node
            
        Returns:
            np.ndarray: The coordinates
        """
        location = self.random_coordinates(self.dimensions)
        self.coordinates[node] = location
        return location
    
    def get_position(self, node: N) -> np.ndarray:
        """
        Get the position of a node
        
        Args:
            node: The node
            
        Returns:
            np.ndarray: The position
        """
        location = self.coordinates.get(node)
        if location is None:
            location = self.new_node_coordinates(node)
        return location
    
    def run(self, iterations: int):
        """
        Run the algorithm for a number of iterations
        
        Args:
            iterations: Number of iterations
        """
        for _ in range(iterations):
            self.align()
    
    class Align:
        """Align callable for parallel execution"""
        
        def __init__(self, node: N, parent):
            """
            Constructor
            
            Args:
                node: The node to align
                parent: The parent map
            """
            self.node = node
            self.parent = parent
        
        def __call__(self) -> np.ndarray:
            """
            Call the align function
            
            Returns:
                np.ndarray: The aligned position
            """
            return self.parent.align(self.node, None)
    
    def align(self):
        """Align the map"""
        self.total_movement = self.DEFAULT_TOTAL_MOVEMENT
        self.max_movement = self.DEFAULT_MAX_MOVEMENT
        
        if self.thread_executor is None:
            center = self.process_locally()
        else:
            # Align all nodes in parallel
            futures = self.submit_future_aligns()
            
            # Wait for all nodes to finish aligning
            try:
                center = self.wait_and_process_futures(futures)
            except InterruptedException:
                raise RuntimeError("Unexpected interruption")
        
        # Calculate the center of all points
        num_vertices = len(self.graph.vertex_set())
        center = center / num_vertices
        
        self.recenter_nodes(center)
    
    def get_dimensions(self) -> int:
        """
        Get the number of dimensions
        
        Returns:
            int: The number of dimensions
        """
        return self.dimensions
    
    @staticmethod
    def add(target: np.ndarray, add_vector: np.ndarray):
        """
        Add vectors
        
        Args:
            target: Target vector
            add_vector: Vector to add
        """
        target += add_vector
    
    @staticmethod
    def add(target: np.ndarray, add_vector: np.ndarray, factor: float):
        """
        Add vectors with a factor
        
        Args:
            target: Target vector
            add_vector: Vector to add
            factor: Factor to multiply by
        """
        if factor == 0:
            return
        
        target += add_vector * factor
    
    @staticmethod
    def sub(target: np.ndarray, subtract_vector: np.ndarray):
        """
        Subtract vectors
        
        Args:
            target: Target vector
            subtract_vector: Vector to subtract
        """
        target -= subtract_vector
    
    def recenter_nodes(self, center: np.ndarray):
        """
        Recenter all nodes
        
        Args:
            center: The center point
        """
        for node in self.graph.vertex_set():
            v = self.coordinates.get(node)
            if v is not None:
                self.sub(v, center)
    
    def get_radius(self, n: N) -> float:
        """
        Get the radius of a node
        
        Args:
            n: The node
            
        Returns:
            float: The radius
        """
        return 0.0
    
    def get_speed_factor(self, n: N) -> float:
        """
        Get the speed factor of a node
        
        Args:
            n: The node
            
        Returns:
            float: The speed factor
        """
        return 1.0
    
    def get_edge_weight(self, e: E) -> float:
        """
        Get the edge weight
        
        Args:
            e: The edge
            
        Returns:
            float: The edge weight
        """
        return 1.0
    
    def get_neighbors(self, node_to_query: N, neighbors: Dict[N, float] = None) -> Dict[N, float]:
        """
        Get the neighbors of a node
        
        Args:
            node_to_query: The node to query
            neighbors: Dictionary to store neighbors
            
        Returns:
            Dict: The neighbors
        """
        if neighbors is None:
            neighbors = {}
        else:
            neighbors.clear()
        
        for neighbor_edge in self.graph.edges_of(node_to_query):
            s = self.graph.get_edge_source(neighbor_edge)
            t = self.graph.get_edge_target(neighbor_edge)
            neighbor = t if s == node_to_query else s
            
            existing_weight = neighbors.get(neighbor)
            current_weight = self.get_edge_weight(neighbor_edge)
            
            if existing_weight is not None:
                if self.edge_weight_to_distance == self.EdgeWeightToDistanceFunction.Min:
                    current_weight = min(existing_weight, current_weight)
                elif self.edge_weight_to_distance == self.EdgeWeightToDistanceFunction.Max:
                    current_weight = max(existing_weight, current_weight)
                elif self.edge_weight_to_distance in [self.EdgeWeightToDistanceFunction.SumOneDiv, 
                                                     self.EdgeWeightToDistanceFunction.OneDivSumOneDiv]:
                    current_weight = 1/current_weight + existing_weight
                elif self.edge_weight_to_distance in [self.EdgeWeightToDistanceFunction.Sum, 
                                                     self.EdgeWeightToDistanceFunction.OneDivSum]:
                    current_weight += existing_weight
            
            neighbors[neighbor] = current_weight
        
        # Apply final transformations
        if self.edge_weight_to_distance in [self.EdgeWeightToDistanceFunction.OneDivSumOneDiv, 
                                           self.EdgeWeightToDistanceFunction.OneDivSum]:
            for neighbor, weight in neighbors.items():
                neighbors[neighbor] = 1.0 / weight
        
        return neighbors
    
    def magnitude(self, x: np.ndarray) -> float:
        """
        Calculate the magnitude of a vector
        
        Args:
            x: The vector
            
        Returns:
            float: The magnitude
        """
        return self.distance_function.get_distance(self.zero, x)
    
    def align(self, node_to_align: N, neighbors: Dict[N, float] = None) -> np.ndarray:
        """
        Align a node
        
        Args:
            node_to_align: The node to align
            neighbors: The neighbors
            
        Returns:
            np.ndarray: The aligned position
        """
        position = self.get_position(node_to_align)
        node_speed = self.get_speed_factor(node_to_align)
        
        if node_speed == 0:
            return position
        
        neighbors = self.get_neighbors(node_to_align, neighbors)
        delta = np.zeros(self.dimensions)
        target_distance = self.get_radius(node_to_align) + self.equilibrium_distance
        
        # Align with neighbors
        for neighbor, dist_to_neighbor in neighbors.items():
            attract_vector = self.get_position(neighbor) - position
            old_distance = self.magnitude(attract_vector)
            
            new_distance = 0.0
            factor = 0.0
            
            if old_distance > dist_to_neighbor:
                new_distance = math.pow(old_distance - dist_to_neighbor, self.ATTRACTION_STRENGTH)
            else:
                new_distance = -target_distance * self.atanh((dist_to_neighbor - old_distance) / dist_to_neighbor)
                
                if abs(new_distance) > abs(dist_to_neighbor - old_distance):
                    new_distance = -target_distance * (dist_to_neighbor - old_distance)
            
            new_distance *= self.learning_rate
            if old_distance != 0:
                factor = new_distance / old_distance
            
            self.add(delta, attract_vector, factor)
        
        # Calculate repulsion with non-neighbors
        repel_vector = np.zeros(self.dimensions)
        max_effective_distance = target_distance * self.max_repulsion_distance
        
        for node in self.graph.vertex_set():
            if node == node_to_align or node in neighbors:
                continue
            
            old_distance = self.distance_function.subtract_if_less_than(
                self.get_position(node), position, repel_vector, max_effective_distance)
            
            if old_distance == float('inf'):
                continue
            
            new_distance = -target_distance / math.pow(old_distance, self.REPULSIVE_WEAKNESS)
            
            if abs(new_distance) > target_distance:
                new_distance = math.copysign(target_distance, new_distance)
            
            new_distance *= self.learning_rate
            
            self.add(delta, repel_vector, new_distance / old_distance)
        
        # Apply speed factor
        if node_speed != 1.0:
            delta *= node_speed
        
        # Check movement distance
        move_distance = self.magnitude(delta)
        
        if move_distance > target_distance * self.acceptable_max_distance_factor:
            new_learning_rate = (target_distance * self.acceptable_max_distance_factor) / move_distance
            
            if new_learning_rate < self.learning_rate:
                self.learning_rate = new_learning_rate
            else:
                self.learning_rate *= self.LEARNING_RATE_INCREASE_FACTOR
            
            move_distance = self.DEFAULT_TOTAL_MOVEMENT
        else:
            self.add(position, delta)
        
        if move_distance > self.max_movement:
            self.max_movement = move_distance
        
        self.total_movement += move_distance
        
        return position
    
    @staticmethod
    def random_coordinates(dimensions: int) -> np.ndarray:
        """
        Generate random coordinates
        
        Args:
            dimensions: Number of dimensions
            
        Returns:
            np.ndarray: Random coordinates
        """
        return np.random.uniform(-1.0, 1.0, dimensions)
    
    @staticmethod
    def atanh(value: float) -> float:
        """
        Calculate the inverse hyperbolic tangent
        
        Args:
            value: The input value
            
        Returns:
            float: The inverse hyperbolic tangent
        """
        return math.log(abs((value + 1.0) / (1.0 - value))) / 2
    
    def submit_future_aligns(self) -> List[Future]:
        """
        Submit align tasks to the executor
        
        Returns:
            List: The futures
        """
        futures = []
        for node in self.graph.vertex_set():
            futures.append(self.thread_executor.submit(self.Align(node, self)))
        return futures
    
    def process_locally(self) -> np.ndarray:
        """
        Process alignment locally
        
        Returns:
            np.ndarray: The point sum
        """
        point_sum = np.zeros(self.dimensions)
        reusable_neighbor_data = {}
        
        for node in self.graph.vertex_set():
            new_position = self.align(node, reusable_neighbor_data)
            self.add(point_sum, new_position)
        
        # Adjust learning rate
        if (self.learning_rate * self.LEARNING_RATE_PROCESSING_ADJUSTMENT) < self.DEFAULT_LEARNING_RATE:
            acceptable_distance_adjustment = 0.1
            if self.get_average_movement() < (self.equilibrium_distance * self.acceptable_max_distance_factor * 
                                             acceptable_distance_adjustment):
                self.acceptable_max_distance_factor *= self.LEARNING_RATE_INCREASE_FACTOR
            
            self.learning_rate *= self.LEARNING_RATE_PROCESSING_ADJUSTMENT
        
        return point_sum
    
    def wait_and_process_futures(self, futures: List[Future]) -> np.ndarray:
        """
        Wait for futures and process results
        
        Args:
            futures: The futures
            
        Returns:
            np.ndarray: The point sum
        """
        point_sum = np.zeros(self.dimensions)
        
        try:
            for future in futures:
                new_point = future.result()
                point_sum += new_point
        except Exception as e:
            raise RuntimeError("Unexpected execution exception") from e
        
        # Adjust learning rate
        if self.learning_rate * self.LEARNING_RATE_PROCESSING_ADJUSTMENT < self.DEFAULT_LEARNING_RATE:
            acceptable_distance_adjustment = 0.1
            if self.get_average_movement() < (self.equilibrium_distance * self.acceptable_max_distance_factor * 
                                             acceptable_distance_adjustment):
                self.acceptable_max_distance_factor = self.max_movement * 2.0
            
            self.learning_rate *= self.LEARNING_RATE_PROCESSING_ADJUSTMENT
        
        return point_sum
    
    def get_average_movement(self) -> float:
        """
        Get the average movement
        
        Returns:
            float: The average movement
        """
        return self.total_movement / len(self.graph.vertex_set())
    
    def __str__(self) -> str:
        """
        String representation
        
        Returns:
            str: String representation
        """
        return str(self.coordinates)
