############################################################
#  	Default Logging Configuration File
#
# In the LIDA framework the logging configuration file is specified by the 
# 'lida.logging.configuration' property in the 'lidaConfig.propoerties' file.
# By default it is this file: 'configs/logging.properties'.
# After an agent is created but before the framework's GUI is created, this file
# configures the LogManager (thus all loggers created in the agent) by calling
# 'LogManager#readConfiguration(InputStream ins)'.
#
# Note that the LIDA framework will always configure the LogManager this way,
# which overrides the LogManager's default configuration specified by the 
# properties file "lib/logging.properties" in the JRE directory.
#
# @see java.util.logging.LogManager
# @see java.util.logging.ConsoleHandler
# @see java.util.logging.FileHandler
############################################################

############################################################
#  	Global properties
############################################################

# Default global logging level.
# This specifies which kinds of events are logged across
# all loggers.  For any given facility this global level
# can be overridden by a facility specific level
# Note that the ConsoleHandler also has a separate level
# setting to limit messages printed to the console.
.level=INFO

# "handlers" specifies a comma separated list of log Handler 
# classes.  These handlers will be installed during VM startup.
# Note that these classes must be on the system classpath.
# By default we only configure a ConsoleHandler, which will only
# show messages at the INFO and above levels.
handlers= java.util.logging.ConsoleHandler
#handlers=java.util.logging.FileHandler,java.util.logging.ConsoleHandler

############################################################
# Handler specific properties.
# Describes specific configuration info for Handlers.
############################################################

###### ConsoleHandler ######
#specifies the Level of messages printed to the console.
java.util.logging.ConsoleHandler.level= info
#specifies the name of a Formatter class to use (defaults to java.util.logging.XMLFormatter)
java.util.logging.ConsoleHandler.formatter=edu.memphis.ccrg.lida.Framework.initialization.FrameworkFormatter

###### FileHandler ######
#specifies the default level for the Handler (defaults to Level.ALL).
java.util.logging.FileHandler.level=FINEST
#specifies the name of a Filter class to use (defaults to no Filter)
java.util.logging.FileHandler.filter= 
#specifies the name of a Formatter class to use (defaults to java.util.logging.XMLFormatter)
java.util.logging.FileHandler.formatter=edu.memphis.ccrg.lida.Framework.initialization.FrameworkFormatter
#the name of the character set encoding to use (defaults to the default platform encoding).
java.util.logging.FileHandler.encoding= 
#specifies an approximate maximum amount to write (in bytes) to any one file. If this is zero, then there is no limit. (Defaults to no limit).
java.util.logging.FileHandler.limit=0
#specifies how many output files to cycle through (defaults to 1).
java.util.logging.FileHandler.count=1
#specifies a pattern for generating the output file name. (Defaults to "%h/java%u.log").
java.util.logging.FileHandler.pattern=%h/../../claLogs%u.log
#specifies whether the FileHandler should append onto any existing files (defaults to false).
java.util.logging.FileHandler.append=false 

# The 'config' property is intended to allow arbitrary configuration code to be run. 
# The property defines a whitespace or comma separated list of class names. 
# A new instance will be created for each named class. 
# The default constructor of each class may execute arbitrary code to update the logging configuration, 
# such as setting logger levels, adding handlers, adding filters, etc. 
#config=your custom configuration Class
