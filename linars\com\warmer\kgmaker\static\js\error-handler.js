/**
 * 全局错误处理器
 * 用于捕获和处理未捕获的Promise错误
 */
window.addEventListener('unhandledrejection', function(event) {
    // 检查错误是否来自翻译API
    if (event.reason && 
        (event.reason.cmd === 'background-clear-trans-state' || 
         event.reason.message === 'The message port closed before a response was received.')) {
        // 阻止默认处理（例如将错误打印到控制台）
        event.preventDefault();
        console.log('忽略翻译API错误:', event.reason);
    }
});

// 捕获常规的JavaScript错误
window.onerror = function(message, source, lineno, colno, error) {
    // 检查错误是否来自翻译API
    if (source && source.includes('translate-api')) {
        // 返回true表示我们已经处理了这个错误
        console.log('忽略翻译API错误:', message);
        return true;
    }
    // 返回false表示我们没有处理这个错误，让浏览器继续处理
    return false;
};
