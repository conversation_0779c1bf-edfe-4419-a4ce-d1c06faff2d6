 
 
<!DOCTYPE html>
<html class="site-demo-overflow">
<head>
<meta charset=utf-8"utf-8">
<title>layDate - JS日期和时间选择器组件/插件 - 在线演示 - layui</title>
<meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <meta name="apple-mobile-web-app-status-bar-style" content="black"> 
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="format-detection" content="telephone=no">
  
  <link rel="stylesheet" href="../../res.layui.com/layui/dist/css/layui.css-t=1515376178738.css" tppabs="http://res.layui.com/layui/dist/css/layui.css?t=1515376178738"  media="all">
  <link rel="stylesheet" href="../../res.layui.com/css/global.css-t=1515376178738-1.css" tppabs="http://res.layui.com/css/global.css?t=1515376178738-1" media="all">
<style>
.site-demo-body .layui-form-label{width: 100px;}
.site-demo-laydate .layui-inline{margin: 0 20px 20px 0;}
@media screen and (max-width: 450px){
  .layui-form-item .layui-input-inline{margin-left: 130px;}
}
</style>
</head>
<body>
<div class="layui-layout layui-layout-admin">
  <div class="layui-header header header-demo">
  <div class="layui-main">
    <a class="logo" href="../index.htm" tppabs="http://www.layui.com/">
      <img src="../../res.layui.com/images/layui/logo.png" tppabs="http://res.layui.com/images/layui/logo.png" alt="layui">
    </a>
    <div class="layui-form component">
      <select lay-search lay-filter="component">
        <option value="">搜索组件或模块</option>
        <option value="element/layout.html">grid 栅格布局</option>
        <option value="element/layout.html#admin">admin 后台布局</option>
        <option value="element/color.html">color 颜色</option>
        <option value="element/icon.html">iconfont 字体图标</option>
        <option value="element/anim.html">animation 动画</option>
        <option value="element/button.html">button 按钮</option>
        <option value="element/form.html">form 表单组</option>
        <option value="element/form.html#input">input 输入框</option>
        <option value="element/form.html#select">select 下拉选择框</option>
        <option value="element/form.html#checkbox">checkbox 复选框</option>
        <option value="element/form.html#switch">switch 开关</option>
        <option value="element/form.html#radio">radio 单选框</option>
        <option value="element/form.html#textarea">textarea 文本域</option>
        <option value="element/nav.html">nav 导航菜单</option>
        <option value="element/nav.html#breadcrumb">breadcrumb 面包屑</option>
        <option value="element/tab.html">tabs 选项卡</option>
        <option value="element/progress.html">progress 进度条</option>
        <option value="element/collapse.html">collapse 折叠面板/手风琴</option>
        <option value="element/table.html">table 表格元素</option>
        <option value="element/badge.html">badge 徽章</option>
        <option value="element/timeline.html">timeline 时间线</option>
        <option value="element/auxiliar.html#blockquote">blockquote 引用块</option>
        <option value="element/auxiliar.html#fieldset">fieldset 字段集</option>
        <option value="element/auxiliar.html#hr">hr 分割线</option>
        
        <option value="modules/layer.html">layer 弹出层/弹窗综合</option>
        <option value="modules/laydate.html">laydate 日期时间选择器</option>
        <option value="modules/layim.html">layim 即时通讯/聊天</option>
        <option value="modules/laypage.html">laypage 分页</option>
        <option value="modules/laytpl.html">laytpl 模板引擎</option>
        <option value="modules/form.html">form 表单模块</option>
        <option value="modules/table.html">table 数据表格</option>
        <option value="modules/upload.html">upload 文件/图片上传</option>
        <option value="modules/element.html">element 常用元素操作</option>
        <option value="modules/carousel.html">carousel 轮播/跑马灯</option>
        <option value="modules/layedit.html">layedit 富文本编辑器</option>
        <option value="modules/tree.html">tree 树形菜单</option>
        <option value="modules/flow.html">flow 信息流/图片懒加载</option>
        <option value="modules/util.html">util 工具集</option>
        <option value="modules/code.html">code 代码修饰</option>
      </select>
    </div>
    <ul class="layui-nav">
      <li class="layui-nav-item ">
        <a href="../doc/index.htm" tppabs="http://www.layui.com/doc/">文档<!-- <span class="layui-badge-dot"></span> --></a> 
      </li>
      <li class="layui-nav-item layui-this">
        <a href="index.htm" tppabs="http://www.layui.com/demo/">示例<!--  --></a>
      </li> 
      
      <li class="layui-nav-item layui-hide-xs">
        <a href="javascript:if(confirm('http://fly.layui.com/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='http://fly.layui.com/'" tppabs="http://fly.layui.com/" target="_blank">社区</a>
      </li>
      
      
      <li class="layui-nav-item">
        <!--<span class="layui-badge-dot" style="margin: -4px 3px 0;"></span>-->
        <a href="javascript:;">周边</a>
        <dl class="layui-nav-child">
          <dd class="layui-hide-sm layui-show-xs"><a href="javascript:if(confirm('http://fly.layui.com/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='http://fly.layui.com/'" tppabs="http://fly.layui.com/" target="_blank">社区交流</a><hr></dd>
          <dd><a href="javascript:if(confirm('http://layim.layui.com/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='http://layim.layui.com/'" tppabs="http://layim.layui.com/" target="_blank">即时聊天</a></dd>
          <dd><a href="../template/fly/index.htm" tppabs="http://www.layui.com/template/fly/" target="_blank">社区模板<span class="layui-badge-dot"></span></a></dd>
          <hr>
          <dd><a href="../alone.html" tppabs="http://www.layui.com/alone.html" target="_blank">独立组件</a></dd>
          <dd><a href="javascript:if(confirm('http://fly.layui.com/jie/9842/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='http://fly.layui.com/jie/9842/'" tppabs="http://fly.layui.com/jie/9842/" target="_blank">Axure组件</a></dd>
        </dl>
      </li>
      
      
      <li class="layui-nav-item layui-hide-xs">
        <a href="../admin/index.htm" tppabs="http://www.layui.com/admin/" target="_blank">后台<span class="layui-badge-dot"></span></a>
      </li>
      
    </ul>
  </div>
</div>
<!-- 让IE8/9支持媒体查询，从而兼容栅格 -->
<!--[if lt IE 9]>
  <script src="../../cdn.staticfile.org/html5shiv/r29/html5.min.js" tppabs="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
  <script src="../../cdn.staticfile.org/respond.js/1.4.2/respond.min.js" tppabs="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
<![endif]--> 
  <div class="layui-side layui-bg-black">
    <div class="layui-side-scroll">
      
<ul class="layui-nav layui-nav-tree site-demo-nav">
  
  <li class="layui-nav-item layui-nav-itemed">
    <a class="javascript:;" href="javascript:;">开发工具</a>
    <dl class="layui-nav-child">
      <dd>
        <a href="index.htm" tppabs="http://www.layui.com/demo/">调试预览</a>
      </dd>
    </dl>
  </li>
  
  <li class="layui-nav-item layui-nav-itemed">
    <a class="javascript:;" href="javascript:;">布局</a>
    <dl class="layui-nav-child">
      <dd class="">
        <a href="grid.html" tppabs="http://www.layui.com/demo/grid.html">栅格</a>
      </dd>
      <dd class="">
        <a href="admin.html" tppabs="http://www.layui.com/demo/admin.html">后台布局</a>
      </dd>
    </dl>
  </li>
  
  <li class="layui-nav-item layui-nav-itemed">
    <a class="javascript:;" href="javascript:;">基本元素</a>
    <dl class="layui-nav-child">
      <dd class="">
        <a href="button.html" tppabs="http://www.layui.com/demo/button.html">按钮</a>
      </dd>
      <dd class="">
        <a href="form.html" tppabs="http://www.layui.com/demo/form.html">表单</a>
      </dd>
      <dd class="">
        <a href="nav.html" tppabs="http://www.layui.com/demo/nav.html">导航/面包屑</a>
      </dd>
      <dd class="">
        <a href="tab.html" tppabs="http://www.layui.com/demo/tab.html">选项卡</a>
      </dd>
      <dd class="">
        <a href="progress.html" tppabs="http://www.layui.com/demo/progress.html">进度条</a>
      </dd>
      <dd class="">
        <a href="panel.html" tppabs="http://www.layui.com/demo/panel.html">面板</a>
      </dd>
      <dd class="">
        <a href="badge.html" tppabs="http://www.layui.com/demo/badge.html">徽章</a>
      </dd>
      <dd class="">
        <a href="timeline.html" tppabs="http://www.layui.com/demo/timeline.html">时间线</a>
      </dd>
      <dd class="">
        <a href="table-element.html" tppabs="http://www.layui.com/demo/table-element.html">静态表格</a>
      </dd>
      <dd class="">
        <a href="anim.html" tppabs="http://www.layui.com/demo/anim.html">动画</a>
      </dd>
      <dd class="">
        <a href="auxiliar.html" tppabs="http://www.layui.com/demo/auxiliar.html">辅助元素</a>
      </dd>
    </dl>
  </li>
  
  <li class="layui-nav-item layui-nav-itemed">
    <a class="javascript:;" href="javascript:;">组件示例</a>
    <dl class="layui-nav-child">
      <dd class="">
        <a href="layer.html" tppabs="http://www.layui.com/demo/layer.html">
          弹出层
        </a>
      </dd>
      <dd class="layui-this">
        <a href="laydate.html" tppabs="http://www.layui.com/demo/laydate.html">
          日期与时间选择
        </a>
      </dd>
      <dd class="">
        <a href="layim.html" tppabs="http://www.layui.com/demo/layim.html">
          即时通讯
        </a>
      </dd>
      <dd class="">
        <a href="table.html" tppabs="http://www.layui.com/demo/table.html">
          数据表格
        </a>
      </dd>
       <dd class="">
        <a href="javascript:if(confirm('http://www.layui.com/demo/laypage.html  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ������������ʵ��ļ������صġ�  \n\n�����ڷ������ϴ���?'))window.location='http://www.layui.com/demo/laypage.html'" tppabs="http://www.layui.com/demo/laypage.html">
          分页
        </a>
      </dd>
      <dd class="">
        <a href="upload.html" tppabs="http://www.layui.com/demo/upload.html">
          文件上传
        </a>
      </dd>
      <dd class="">
        <a href="javascript:if(confirm('http://www.layui.com/demo/carousel.html  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ������������ʵ��ļ������صġ�  \n\n�����ڷ������ϴ���?'))window.location='http://www.layui.com/demo/carousel.html'" tppabs="http://www.layui.com/demo/carousel.html">
          轮播
        </a>
      </dd>
      <dd class="">
        <a href="javascript:if(confirm('http://www.layui.com/demo/laytpl.html  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ������������ʵ��ļ������صġ�  \n\n�����ڷ������ϴ���?'))window.location='http://www.layui.com/demo/laytpl.html'" tppabs="http://www.layui.com/demo/laytpl.html">
          模板引擎
        </a>
      </dd>
      
      <dd class="">
        <a href="javascript:if(confirm('http://www.layui.com/demo/flow.html  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ������������ʵ��ļ������صġ�  \n\n�����ڷ������ϴ���?'))window.location='http://www.layui.com/demo/flow.html'" tppabs="http://www.layui.com/demo/flow.html">
          流加载
        </a>
      </dd>
      <dd class="">
        <a href="javascript:if(confirm('http://www.layui.com/demo/util.html  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ������������ʵ��ļ������صġ�  \n\n�����ڷ������ϴ���?'))window.location='http://www.layui.com/demo/util.html'" tppabs="http://www.layui.com/demo/util.html">
          工具集
        </a>
      </dd>
      <dd class="">
        <a href="code.html" tppabs="http://www.layui.com/demo/code.html">
          代码修饰器
        </a>
      </dd>
    </dl>
  </li>
  
  <li class="layui-nav-item" style="height: 30px; text-align: center"></li>
</ul>

    </div>
  </div>
  <div class="layui-tab layui-tab-brief" lay-filter="demoTitle">
    <ul class="layui-tab-title site-demo-title">
      <li class="layui-this">预览</li>
      <li>查看代码</li>
      <li>帮助</li>
    </ul>
    <div class="layui-body layui-tab-content site-demo site-demo-body">
    
    
      <div class="layui-tab-item layui-show">
        <div class="layui-main">
          <div id="LAY_preview">
            
<blockquote class="layui-elem-quote">
  layDate 是目前 layui 独立维护的三大组件（即：layer、layim、layDate）之一。在 layui 2.0 的版本中，layDate 完成了一次巨大的逆袭。
  <a class="layui-btn layui-btn-normal" href="javascript:if(confirm('http://www.layui.com/laydate/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ������������ʵ��ļ������صġ�  \n\n�����ڷ������ϴ���?'))window.location='http://www.layui.com/laydate/'" tppabs="http://www.layui.com/laydate/" target="_blank">layDate官网</a>
</blockquote>
      
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>常规用法</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">中文版</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test1" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">国际版</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test1-1" placeholder="yyyy-MM-dd">
      </div>
    </div>
  </div>
</div>
          
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>其它选择器</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">年选择器</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test2" placeholder="yyyy">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">年月选择器</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test3" placeholder="yyyy-MM">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">时间选择器</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test4" placeholder="HH:mm:ss">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">日期时间选择器</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test5" placeholder="yyyy-MM-dd HH:mm:ss">
      </div>
    </div>
  </div>
</div>
 
<!-- 示例-970 -->
<ins class="adsbygoogle"
style="display:inline-block;width:970px;height:90px"
data-ad-client="ca-pub-6111334333458862"
data-ad-slot="3820120620"></ins>
  
          
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>范围选择</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">日期范围</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test6" placeholder=" - ">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">年范围</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test7" placeholder=" - ">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">年月范围</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test8" placeholder=" - ">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">时间范围</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test9" placeholder=" - ">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">日期时间范围</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test10" placeholder=" - ">
      </div>
    </div>
  </div>
</div>
 
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>自定义格式</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">请选择日期</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test11" placeholder="yyyy年MM月dd日">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">请选择日期</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test12" placeholder="dd/MM/yyyy">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">请选择月份</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test13" placeholder="yyyyMMdd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">请选择时间</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test14" placeholder="H点m分">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">请选择范围</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test15" placeholder=" ~ ">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">请选择范围</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test16" placeholder="开始 到 结束">
      </div>
    </div>
  </div>
</div>
 
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>公历节日和自定义重要日子</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">开启公历节日</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test17" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">自定义重要日</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test18" placeholder="yyyy-MM-dd">
      </div>
    </div>
  </div>
</div>
 
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>控制可选的日期与时间</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">限定可选日期</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test-limit1" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">前后若干天可选</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test-limit2" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">限定可选时间</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test-limit3" placeholder="HH:mm:ss">
      </div>
      <div class="layui-form-mid layui-cont-aux">
        这里以控制在9:30-17:30为例
      </div>
    </div>
  </div>
</div>
 
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>同时绑定多个</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <input type="text" class="layui-input test-item" placeholder="yyyy-MM-dd">
    </div>
    <div class="layui-inline">
      <input type="text" class="layui-input test-item" placeholder="yyyy-MM-dd">
    </div>
    <div class="layui-inline">
      <input type="text" class="layui-input test-item" placeholder="yyyy-MM-dd">
    </div>
  </div>
</div>
 
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>其它功能示例</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">初始赋值</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test19" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">选中后的回调</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test20" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">日期切换的回调</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test21" placeholder="yyyy-MM-dd">
      </div>
    </div>
    
    <div class="layui-inline">
      <label class="layui-form-label">不出现底部栏</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test22" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">只出现确定按钮</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test23" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">自定义事件</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test24" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label" id="test25-1">点我触发</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test25" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label" id="test26-1">双击我触发</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test26" placeholder="yyyy-MM-dd">
      </div>
    </div>
    
    <div class="layui-inline">
      <label class="layui-form-label">日期只读</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test27" readonly placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">非input元素</label>
      <div class="layui-input-inline">
        <div id="test28" style="height: 38px; line-height: 38px; cursor: pointer; border-bottom: 1px solid #e2e2e2;"></div>
      </div>
    </div>
  </div>
</div>
 
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>其它主题</legend>
</fieldset>
 
<div class="layui-form">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">墨绿主题</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test29" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">自定义颜色主题</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test30" placeholder="yyyy-MM-dd">
      </div>
    </div>
    <div class="layui-inline">
      <label class="layui-form-label">格子主题</label>
      <div class="layui-input-inline">
        <input type="text" class="layui-input" id="test31" placeholder="yyyy-MM-dd">
      </div>
    </div>
  </div>
</div> 
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
  <legend>直接嵌套显示</legend>
</fieldset>
 
<div class="site-demo-laydate">
  <div class="layui-inline" id="test-n1"></div>
  <div class="layui-inline" id="test-n2"></div>
  <div class="layui-inline" id="test-n3"></div>
  <div class="layui-inline" id="test-n4"></div>
</div>
          
          </div>
          
        </div>
      </div>
      
      
      <div class="layui-tab-item">
<textarea class="layui-border-box site-demo-text site-demo-code" id="LAY_democode" spellcheck="false" readonly>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>layui</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="../../res.layui.com/layui/dist/css/layui.css" tppabs="http://res.layui.com/layui/dist/css/layui.css"  media="all">
  <!-- 注意：如果你直接复制所有代码到本地，上述css路径需要改成你本地的 -->
</head>
</textarea>
      </div>
      
      
      <div class="layui-tab-item">
        <div class="layui-main">
          <p>与独立组件layDate最大的不同在于，你现在需要自己绑定事件。</p>
          
          

<div style="margin: 15px 0;">
  <ins class="adsbygoogle"
  style="display:inline-block;width:970px;height:90px"
  data-ad-client="ca-pub-6111334333458862"
  data-ad-slot="6835627838"></ins>
</div>


          
          <fieldset class="layui-elem-field layui-field-title" style="margin-top: 50px;">
            <legend>相关</legend>
          </fieldset>
          <a class="layui-btn layui-btn-normal" href="../doc/modules/laydate.html" tppabs="http://www.layui.com/doc/modules/laydate.html" target="_blank">layDate文档</a>
        </div>
      </div>
      
        
    </div>
  </div>
  <div class="layui-footer footer footer-demo">
  <div class="layui-main">
    <p>&copy; 2018 <a href="../index.htm" tppabs="http://www.layui.com/">layui.com</a> MIT license</p>
    <p>
      <a href="javascript:if(confirm('http://fly.layui.com/case/2018/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='http://fly.layui.com/case/2018/'" tppabs="http://fly.layui.com/case/2018/" target="_blank">案例</a>
      <a href="javascript:if(confirm('http://fly.layui.com/jie/3147/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='http://fly.layui.com/jie/3147/'" tppabs="http://fly.layui.com/jie/3147/" target="_blank">众筹</a>
      <a href="mailto:<EMAIL>">联系</a>
      <a href="javascript:if(confirm('https://github.com/sentsin/layui/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='https://github.com/sentsin/layui/'" tppabs="https://github.com/sentsin/layui/" target="_blank" rel="nofollow">GitHub</a>
      <a href="javascript:if(confirm('https://gitee.com/sentsin/layui  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='https://gitee.com/sentsin/layui'" tppabs="https://gitee.com/sentsin/layui" target="_blank" rel="nofollow">码云</a>
      <a href="javascript:if(confirm('http://fly.layui.com/jie/2461/  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='http://fly.layui.com/jie/2461/'" tppabs="http://fly.layui.com/jie/2461/" target="_blank">微信公众号</a>
    </p>
    <p class="site-union">
      <a href="javascript:if(confirm('https://www.upyun.com/?from=layui  \n\n���ļ��޷��� Teleport Ultra ����, ��Ϊ ����һ�����·���ⲿ������Ϊ������ʼ��ַ�ĵ�ַ��  \n\n�����ڷ������ϴ���?'))window.location='https://www.upyun.com/?from=layui'" tppabs="https://www.upyun.com/?from=layui" target="_blank" rel="nofollow" upyun><img src="../../res.layui.com/images/other/upyun.png-t=1.png" tppabs="http://res.layui.com//images/other/upyun.png?t=1"></a>
      <span>提供 CDN 赞助</span>
    </p>
  </div>
</div>
<script async src="../../pagead2.googlesyndication.com/pagead/js/adsbygoogle.js" tppabs="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<div class="site-tree-mobile layui-hide">
  <i class="layui-icon">&#xe602;</i>
</div>
<div class="site-mobile-shade"></div>
<script src="../../res.layui.com/layui/dist/layui.js-t=1515376178738" tppabs="http://res.layui.com/layui/dist/layui.js?t=1515376178738" charset="utf-8"></script>
<script>
window.global = {
  pageType: 'demo'
  ,preview: function(){
    var preview = document.getElementById('LAY_preview');
    return preview ? preview.innerHTML : '';
  }()
};
layui.config({
  base: '//res.layui.com/lay/modules/layui/'
  ,version: '1515376178738'
}).use('global');
</script>
<script type="text/javascript">var cnzz_protocol = (("https:" == document.location.protocol) ? " https://" : " http://");document.write(unescape("%3Cspan id='cnzz_stat_icon_30088308'%3E%3C/span%3E%3Cscript src='" + cnzz_protocol + "w.cnzz.com/c.php%3Fid%3D30088308' type='text/javascript'%3E%3C/script%3E"));</script>
</div>
<div id="LAY_democodejs">
<script>
layui.use('laydate', function(){
  var laydate = layui.laydate;
  
  //常规用法
  laydate.render({
    elem: '#test1'
  });
  
  //国际版
  laydate.render({
    elem: '#test1-1'
    ,lang: 'en'
  });
  
  //年选择器
  laydate.render({
    elem: '#test2'
    ,type: 'year'
  });
  
  //年月选择器
  laydate.render({
    elem: '#test3'
    ,type: 'month'
  });
  
  //时间选择器
  laydate.render({
    elem: '#test4'
    ,type: 'time'
  });
  
  //时间选择器
  laydate.render({
    elem: '#test5'
    ,type: 'datetime'
  });
  
  //日期范围
  laydate.render({
    elem: '#test6'
    ,range: true
  });
  
  //年范围
  laydate.render({
    elem: '#test7'
    ,type: 'year'
    ,range: true
  });
  
  //年月范围
  laydate.render({
    elem: '#test8'
    ,type: 'month'
    ,range: true
  });
  
  //时间范围
  laydate.render({
    elem: '#test9'
    ,type: 'time'
    ,range: true
  });
  
  //日期时间范围
  laydate.render({
    elem: '#test10'
    ,type: 'datetime'
    ,range: true
  });
  
  //自定义格式
  laydate.render({
    elem: '#test11'
    ,format: 'yyyy年MM月dd日'
  });
  laydate.render({
    elem: '#test12'
    ,format: 'dd/MM/yyyy'
  });
  laydate.render({
    elem: '#test13'
    ,format: 'yyyyMMdd'
  });
  laydate.render({
    elem: '#test14'
    ,type: 'time'
    ,format: 'H点m分'
  });
  laydate.render({
    elem: '#test15'
    ,type: 'month'
    ,range: '~'
    ,format: 'yyyy-MM'
  });
  laydate.render({
    elem: '#test16'
    ,type: 'datetime'
    ,range: '到'
    ,format: 'yyyy年M月d日H时m分s秒'
  });
  
  //开启公历节日
  laydate.render({
    elem: '#test17'
    ,calendar: true
  });
  
  //自定义重要日
  laydate.render({
    elem: '#test18'
    ,mark: {
      '0-10-14': '生日'
      ,'0-12-31': '跨年' //每年的日期
      ,'0-0-10': '工资' //每月某天
      ,'0-0-15': '月中'
      ,'2017-8-15': '' //如果为空字符，则默认显示数字+徽章
      ,'2099-10-14': '呵呵'
    }
    ,done: function(value, date){
      if(date.year === 2017 && date.month === 8 && date.date === 15){ //点击2017年8月15日，弹出提示语
        layer.msg('这一天是：中国人民抗日战争胜利72周年');
      }
    }
  });
  
  //限定可选日期
  var ins22 = laydate.render({
    elem: '#test-limit1'
    ,min: '2016-10-14'
    ,max: '2080-10-14'
    ,ready: function(){
      ins22.hint('日期可选值设定在 <br> 2016-10-14 到 2080-10-14');
    }
  });
  
  //前后若干天可选，这里以7天为例
  laydate.render({
    elem: '#test-limit2'
    ,min: -7
    ,max: 7
  });
  
  //限定可选时间
  laydate.render({
    elem: '#test-limit3'
    ,type: 'time'
    ,min: '09:30:00'
    ,max: '17:30:00'
    ,btns: ['clear', 'confirm']
  });
  
  //同时绑定多个
  lay('.test-item').each(function(){
    laydate.render({
      elem: this
      ,trigger: 'click'
    });
  });
  
  //初始赋值
  laydate.render({
    elem: '#test19'
    ,value: '1989-10-14'
  });
  
  //选中后的回调
  laydate.render({
    elem: '#test20'
    ,done: function(value, date){
      layer.alert('你选择的日期是：' + value + '<br>获得的对象是' + JSON.stringify(date));
    }
  });
  
  //日期切换的回调
  laydate.render({
    elem: '#test21'
    ,change: function(value, date){
      layer.msg('你选择的日期是：' + value + '<br><br>获得的对象是' + JSON.stringify(date));
    }
  });
  //不出现底部栏
  laydate.render({
    elem: '#test22'
    ,showBottom: false
  });
  
  //只出现确定按钮
  laydate.render({
    elem: '#test23'
    ,btns: ['confirm']
  });
  
  //自定义事件
  laydate.render({
    elem: '#test24'
    ,trigger: 'mousedown'
  });
  
  //点我触发
  laydate.render({
    elem: '#test25'
    ,eventElem: '#test25-1'
    ,trigger: 'click'
  });
  
  //双击我触发
  lay('#test26-1').on('dblclick', function(){
    laydate.render({
      elem: '#test26'
      ,show: true
      ,closeStop: '#test26-1'
    });
  });
  
  //日期只读
  laydate.render({
    elem: '#test27'
    ,trigger: 'click'
  });
  
  //非input元素
  laydate.render({
    elem: '#test28'
  });
  
  //墨绿主题
  laydate.render({
    elem: '#test29'
    ,theme: 'molv'
  });
  
  //自定义颜色
  laydate.render({
    elem: '#test30'
    ,theme: '#393D49'
  });
  
  //格子主题
  laydate.render({
    elem: '#test31'
    ,theme: 'grid'
  });
  
  
  //直接嵌套显示
  laydate.render({
    elem: '#test-n1'
    ,position: 'static'
  });
  laydate.render({
    elem: '#test-n2'
    ,position: 'static'
    ,lang: 'en'
  });
  laydate.render({
    elem: '#test-n3'
    ,type: 'month'
    ,position: 'static'
  });
  laydate.render({
    elem: '#test-n4'
    ,type: 'time'
    ,position: 'static'
  });
});
</script>
</div>
</body>
</html>