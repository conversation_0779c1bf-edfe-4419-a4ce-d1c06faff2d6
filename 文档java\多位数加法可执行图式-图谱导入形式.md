# 多位数加法可执行图式 - 图谱导入形式

## 一、概述

本文档描述了多位数加法可执行图式的图谱导入形式，基于"多位数加法可执行图式-点边结构表示.md"中的点边结构，进一步细化为可直接导入图谱的点边形式。这种形式更加符合图数据库的存储和查询特性，同时保留了原有点边结构的语义表达能力。

## 二、点边形式设计原则

### 1. 点的设计

在图谱导入形式中，点分为以下几类，并包含详细属性：

1. **数据点**：表示数据或变量，如"和"、"进位"、"结果"等
   - 例如：操作数1(id=1, name="操作数1", type="数据", data_type="string", value="123")

2. **单点操作**：表示基本操作，不包含参数结构
   - 例如：检查(id=101, name="检查", type="操作", operation_type="validation")
   - 例如：计算(id=108, name="计算", type="操作", operation_type="computation")

3. **单点控制**：表示基本控制操作，不包含参数结构
   - 例如：循环条件(id=111, name="循环条件", type="控制", control_type="loop")
   - 例如：判断条件(id=112, name="判断条件", type="控制", control_type="condition")

4. **点组结构操作点**：表示操作与参数的组合，采用点组形式
   - 例如：(*, 获取, 总和) 而非 (获取总和)
   - 属性表示：(id=20, name="获取当前位", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前位"])

5. **点组结构控制点**：表示控制操作与参数的组合，采用点组形式
   - 例如：(*, 检查, 循环条件) 而非 (检查循环条件)
   - 属性表示：(id=19, name="检查循环条件", type="控制：点组结构", control_type="condition_check", predicate="检查", args=["循环条件"])

6. **API点**：表示底层API，如"validation/basic/check_operands_validity"
   - 例如：(id=31, name="validation/basic/check_operands_validity", type="API", category="validation/basic")

### 2. 边的设计

在图谱导入形式中，边分为以下几类，并包含详细属性：

1. **数据流边**：表示数据的流动和传递
   - 属性：(id=1001, type="数据流", source_id=101, target_id=102, data_type="int")
   - 例如：(*, 和) -[数据流]-> (*, 获取, 总和)

2. **顺承边**：表示执行顺序和控制流
   - 属性：(id=2001, type="顺承", source_id=11, target_id=12, priority=1)
   - 例如：(*, 初始化) -[顺承]-> (*, 处理, 位循环)

3. **时序边**：表示包含关系和结构层次
   - 属性：(id=3001, type="时序", source_id=10, target_id=11, is_first=true)
   - 例如：(*, 计算, 加法) -[时序]-> (*, 初始化)
   - 特殊属性：时序首边用于标记第一个执行的子步骤，属性is_first=true

4. **判断边**：表示条件分支和决策点
   - 属性：(id=4001, type="判断", source_id=26, target_id=27, condition="true", is_first=true)
   - 例如：(*, 检查, 进位是否大于零) -[判断首]-> (*, 添加, 进位)
   - 特殊属性：判断首边用于标记条件为true时执行的分支，属性is_first=true

5. **循环边**：表示循环结构和重复执行
   - 属性：(id=5001, type="循环", source_id=23, target_id=19, condition="index>=0")
   - 例如：(*, 移动, 到, 下一位) -[循环]-> (*, 检查, 循环条件)

6. **组件边**：表示点组结构与其组成部分的关系
   - 属性：(id=6001, type="组件", source_id=26, target_id=130, arg_position=1, arg_name="arg1")
   - 例如：(*, 检查, 进位是否大于零) -[arg1]-> (检查)
   - 例如：(*, 检查, 进位是否大于零) -[arg2]-> (进位是否大于零)

7. **API引用边**：表示操作节点引用的API
   - 属性：(id=7001, type="API引用", source_id=15, target_id=31, api_version="1.0")
   - 例如：(*, 检查, 操作数有效性) -[API引用]-> (validation/basic/check_operands_validity)

## 三、点边形式示例

以下是将原有点边结构细化为图谱导入形式的示例：

### 示例1：数据流关系细化

点的详细属性：
```
// 数据点
和(id=101, name="和", type="数据", data_type="int", value=0)

// 点组结构操作点
(id=102, name="获取总和", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["总和"])

// 单点操作
获取(id=103, name="获取", type="操作", operation_type="retrieval")
总和(id=104, name="总和", type="数据", data_type="int")
```

边的详细属性：
```
// 数据流边
(id=1001, type="数据流", source_id=101, target_id=102, data_type="int")

// 组件边
(id=6001, type="组件", source_id=102, target_id=103, arg_position=1, arg_name="arg1")
(id=6002, type="组件", source_id=102, target_id=104, arg_position=2, arg_name="arg2")
```

图谱导入形式：
```
// 数据流关系
(*, 和) -[数据流]-> (*, 获取, 总和)

// 组件关系
(*, 获取, 总和) -[arg1]-> (获取)
(*, 获取, 总和) -[arg2]-> (总和)
```

在这个例子中：
- (*, 和) 是数据点，完整属性在点的详细属性中定义
- (*, 获取, 总和) 是点组结构操作点，表示"获取总和"这个操作
- (获取) 和 (总和) 是单点操作和数据点，是点组结构的组成部分
- [数据流] 表示数据从"和"流向"获取总和"操作
- [arg1] 和 [arg2] 表示点组结构与其组成部分的关系

## 四、多位数加法可执行图式的图谱导入形式

接下来，我将按照上述原则，将多位数加法可执行图式的点边结构细化为图谱导入形式。

### API节点列表

首先，我们定义所有需要的API节点：

```
// API点
(id=31, name="validation/basic/check_operands_validity", type="API", category="validation/basic")
(id=32, name="string/basic/create_empty", type="API", category="string/basic")
(id=33, name="variable/basic/set_zero", type="API", category="variable/basic")
(id=34, name="math/basic/min_length_minus_one", type="API", category="math/basic")
(id=35, name="comparison/basic/greater_or_equal_than_zero", type="API", category="comparison/basic")
(id=36, name="variable/basic/get_value", type="API", category="variable/basic")
(id=37, name="comparison/basic/greater_than_zero", type="API", category="comparison/basic")
(id=38, name="string/basic/prepend_int_to_string", type="API", category="string/basic")
(id=39, name="control/basic/no_operation", type="API", category="control/basic")

// 字符串操作
API字符串检查非空(id=311, name="validation/string/not_empty", type="API", category="validation/string")
API字符串检查仅数字(id=312, name="validation/string/only_digits", type="API", category="validation/string")
API字符串创建空字符串(id=321, name="string/basic/create_empty_string", type="API", category="string/basic")
API字符串获取长度(id=341, name="string/basic/get_length", type="API", category="string/basic")
API字符串检查索引有效(id=361, name="string/basic/check_index_valid", type="API", category="string/basic")
API字符串反向获取字符(id=362, name="string/basic/get_char_at_reverse_index", type="API", category="string/basic")
API字符串连接(id=383, name="string/basic/concatenate", type="API", category="string/basic")
API字符串前置字符(id=413, name="string/basic/prepend_char", type="API", category="string/basic")

// 变量操作
API变量初始化(id=331, name="variable/basic/initialize_variable", type="API", category="variable/basic")
API变量设置值(id=332, name="variable/basic/set_value", type="API", category="variable/basic")
API变量获取值(id=351, name="variable/basic/get_value", type="API", category="variable/basic")
API变量设置返回值(id=384, name="variable/basic/set_value", type="API", category="variable/basic")
API变量获取进位值(id=401, name="variable/basic/get_value", type="API", category="variable/basic")

// 数学运算
API数学加法(id=371, name="math/basic/add", type="API", category="math/basic")
API数学取模10(id=372, name="math/basic/mod_10", type="API", category="math/basic")
API数学除以10(id=373, name="math/basic/divide_10", type="API", category="math/basic")
API数学比较值(id=342, name="comparison/basic/compare_values", type="API", category="comparison/basic")
API数学最小值(id=343, name="math/basic/min", type="API", category="math/basic")
API数学减法(id=344, name="math/basic/subtract", type="API", category="math/basic")
API数学减1(id=392, name="math/basic/subtract", type="API", category="math/basic")

// 比较操作
API比较大于等于零(id=352, name="comparison/basic/greater_or_equal_than_zero", type="API", category="comparison/basic")
API比较大于零(id=402, name="comparison/basic/greater_than", type="API", category="comparison/basic")

// 控制操作
API设置循环结果(id=353, name="control/basic/set_loop_result", type="API", category="control/basic")
API设置判断结果(id=403, name="control/basic/set_condition_result", type="API", category="control/basic")

// 类型转换
API字符转数字(id=363, name="conversion/basic/char_to_digit", type="API", category="conversion/basic")
API数字转字符(id=382, name="conversion/basic/int_to_char", type="API", category="conversion/basic")
API数字转字符串(id=412, name="conversion/basic/int_to_string", type="API", category="conversion/basic")
```

### 1. 上下文节点与主要操作节点

```
#### 1.1 主要节点定义
```

首先，我们定义加法计算的上下文节点及其主要操作节点：

```
// 点的详细属性

// 点组结构操作点
(id=10, name="加法计算", type="操作：点组结构", operation_type="computation", predicate="计算", args=["加法"])
(id=11, name="初始化", type="操作：点组结构", operation_type="initialization", predicate="初始化", args=[])
(id=12, name="处理位循环", type="操作：点组结构", operation_type="process", predicate="处理", args=["位循环"])
(id=13, name="处理最终进位", type="操作：点组结构", operation_type="process", predicate="处理", args=["最终进位"])
(id=14, name="返回结果", type="操作：点组结构", operation_type="return", predicate="返回", args=["结果"])

// 单点操作
计算(id=201, name="计算", type="操作", operation_type="computation")
加法(id=202, name="加法", type="数据", data_type="operation")
初始化(id=203, name="初始化", type="操作", operation_type="initialization")
处理(id=204, name="处理", type="操作", operation_type="process")
位循环(id=205, name="位循环", type="数据", data_type="process_target")
最终进位(id=206, name="最终进位", type="数据", data_type="process_target")
返回(id=207, name="返回", type="操作", operation_type="return")
结果(id=208, name="结果", type="数据", data_type="string")

// 边的详细属性

// 时序边
(id=3001, type="时序", source_id=10, target_id=11, is_first=true)
(id=3002, type="时序", source_id=10, target_id=12, is_first=false)
(id=3003, type="时序", source_id=10, target_id=13, is_first=false)
(id=3004, type="时序", source_id=10, target_id=14, is_first=false)

// 顺承边
(id=2001, type="顺承", source_id=11, target_id=12, priority=1)
(id=2002, type="顺承", source_id=12, target_id=13, priority=1)
(id=2003, type="顺承", source_id=13, target_id=14, priority=1)

// 组件边
(id=6001, type="组件", source_id=10, target_id=201, arg_position=1, arg_name="arg1")
(id=6002, type="组件", source_id=10, target_id=202, arg_position=2, arg_name="arg2")
(id=6003, type="组件", source_id=12, target_id=204, arg_position=1, arg_name="arg1")
(id=6004, type="组件", source_id=12, target_id=205, arg_position=2, arg_name="arg2")
(id=6005, type="组件", source_id=13, target_id=204, arg_position=1, arg_name="arg1")
(id=6006, type="组件", source_id=13, target_id=206, arg_position=2, arg_name="arg2")
(id=6007, type="组件", source_id=14, target_id=207, arg_position=1, arg_name="arg1")
(id=6008, type="组件", source_id=14, target_id=208, arg_position=2, arg_name="arg2")

// 图谱导入形式

// 创建上下文与主要操作的时序关系
(*, 计算, 加法) -[时序首]-> (*, 初始化)
(*, 计算, 加法) -[时序]-> (*, 处理, 位循环)
(*, 计算, 加法) -[时序]-> (*, 处理, 最终进位)
(*, 计算, 加法) -[时序]-> (*, 返回, 结果)

// 创建主要操作之间的顺承关系
(*, 初始化) -[顺承]-> (*, 处理, 位循环)
(*, 处理, 位循环) -[顺承]-> (*, 处理, 最终进位)
(*, 处理, 最终进位) -[顺承]-> (*, 返回, 结果)

// 创建点组结构与单点的组件关系
(*, 计算, 加法) -[arg1]-> (计算)
(*, 计算, 加法) -[arg2]-> (加法)
(*, 处理, 位循环) -[arg1]-> (处理)
(*, 处理, 位循环) -[arg2]-> (位循环)
(*, 处理, 最终进位) -[arg1]-> (处理)
(*, 处理, 最终进位) -[arg2]-> (最终进位)
(*, 返回, 结果) -[arg1]-> (返回)
(*, 返回, 结果) -[arg2]-> (结果)
```

### 2. 初始化操作的图谱导入形式

接下来，我们细化初始化操作的图谱导入形式：

```
// 点的详细属性
操作数1(id=1, name="操作数1", type="数据", data_type="string", value="123")
操作数2(id=2, name="操作数2", type="数据", data_type="string", value="456")
结果(id=3, name="结果", type="数据", data_type="string", value="")
进位(id=4, name="进位", type="数据", data_type="int", value=0)
当前位索引(id=5, name="当前位索引", type="数据", data_type="int", value=0)

// 点组结构操作点
(id=15, name="检查操作数有效性", type="操作：点组结构", operation_type="validation", predicate="检查", args=["操作数有效性"])
(id=16, name="创建空结果字符串", type="操作：点组结构", operation_type="creation", predicate="创建", args=["空结果字符串"])
(id=17, name="设置进位为0", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["进位", "为", "0"])
(id=18, name="计算初始位索引", type="操作：点组结构", operation_type="computation", predicate="计算", args=["初始位索引"])

// 单点操作
检查(id=101, name="检查", type="操作", operation_type="validation")
创建(id=103, name="创建", type="操作", operation_type="creation")
设置(id=105, name="设置", type="操作", operation_type="assignment")
计算(id=108, name="计算", type="操作", operation_type="computation")

// 单点控制
操作数有效性(id=102, name="操作数有效性", type="控制", control_type="validity")

// 数据点
空结果字符串(id=104, name="空结果字符串", type="数据", data_type="string")
为(id=106, name="为", type="连接词")
零(id=107, name="0", type="数据", data_type="int", value=0)
初始位索引(id=109, name="初始位索引", type="数据", data_type="int")

// API点
(id=31, name="validation/basic/check_operands_validity", type="API", category="validation/basic")
(id=32, name="string/basic/create_empty", type="API", category="string/basic")
(id=33, name="variable/basic/set_zero", type="API", category="variable/basic")
(id=34, name="math/basic/min_length_minus_one", type="API", category="math/basic")

// 创建初始化与子操作的时序关系
(*, 初始化) -[时序]-> (*, 检查, 操作数有效性)
(*, 初始化) -[时序]-> (*, 创建, 空结果字符串)
(*, 初始化) -[时序]-> (*, 设置, 进位, 为, 0)
(*, 初始化) -[时序]-> (*, 计算, 初始位索引)

// 创建点组结构操作点与单点的组件关系
(*, 检查, 操作数有效性) -[arg1]-> (检查)
(*, 检查, 操作数有效性) -[arg2]-> (操作数有效性)

(*, 创建, 空结果字符串) -[arg1]-> (创建)
(*, 创建, 空结果字符串) -[arg2]-> (空结果字符串)

(*, 设置, 进位, 为, 0) -[arg1]-> (设置)
(*, 设置, 进位, 为, 0) -[arg2]-> (进位)
(*, 设置, 进位, 为, 0) -[arg3]-> (为)
(*, 设置, 进位, 为, 0) -[arg4]-> (零)

(*, 计算, 初始位索引) -[arg1]-> (计算)
(*, 计算, 初始位索引) -[arg2]-> (初始位索引)

// 创建初始化子操作之间的顺承关系
(*, 检查, 操作数有效性) -[顺承]-> (*, 创建, 空结果字符串)
(*, 创建, 空结果字符串) -[顺承]-> (*, 设置, 进位, 为, 0)
(*, 设置, 进位, 为, 0) -[顺承]-> (*, 计算, 初始位索引)

```
#### 2.1 检查操作数有效性
```

// 点组结构操作点
(id=151, name="检查操作数1不为空", type="操作：点组结构", operation_type="validation", predicate="检查", args=["操作数1", "不为空"])
(id=152, name="检查操作数2不为空", type="操作：点组结构", operation_type="validation", predicate="检查", args=["操作数2", "不为空"])
(id=153, name="检查操作数1仅包含数字", type="操作：点组结构", operation_type="validation", predicate="检查", args=["操作数1", "仅包含数字"])
(id=154, name="检查操作数2仅包含数字", type="操作：点组结构", operation_type="validation", predicate="检查", args=["操作数2", "仅包含数字"])

// 单点操作
不为空(id=155, name="不为空", type="操作", operation_type="validation")
仅包含数字(id=156, name="仅包含数字", type="操作", operation_type="validation")

// 创建“检查操作数有效性”内部时序关系
(*, 检查, 操作数有效性) -[时序]-> (*, 检查, 操作数1, 不为空)
(*, 检查, 操作数有效性) -[时序]-> (*, 检查, 操作数2, 不为空)
(*, 检查, 操作数有效性) -[时序]-> (*, 检查, 操作数1, 仅包含数字)
(*, 检查, 操作数有效性) -[时序]-> (*, 检查, 操作数2, 仅包含数字)

// 创建“检查操作数有效性”内部顺承关系
(*, 检查, 操作数1, 不为空) -[顺承]-> (*, 检查, 操作数2, 不为空)
(*, 检查, 操作数2, 不为空) -[顺承]-> (*, 检查, 操作数1, 仅包含数字)
(*, 检查, 操作数1, 仅包含数字) -[顺承]-> (*, 检查, 操作数2, 仅包含数字)

// 创建“检查操作数有效性”内部组件关系
(*, 检查, 操作数1, 不为空) -[arg1]-> (检查)
(*, 检查, 操作数1, 不为空) -[arg2]-> (操作数1)
(*, 检查, 操作数1, 不为空) -[arg3]-> (不为空)

(*, 检查, 操作数2, 不为空) -[arg1]-> (检查)
(*, 检查, 操作数2, 不为空) -[arg2]-> (操作数2)
(*, 检查, 操作数2, 不为空) -[arg3]-> (不为空)

(*, 检查, 操作数1, 仅包含数字) -[arg1]-> (检查)
(*, 检查, 操作数1, 仅包含数字) -[arg2]-> (操作数1)
(*, 检查, 操作数1, 仅包含数字) -[arg3]-> (仅包含数字)

(*, 检查, 操作数2, 仅包含数字) -[arg1]-> (检查)
(*, 检查, 操作数2, 仅包含数字) -[arg2]-> (操作数2)
(*, 检查, 操作数2, 仅包含数字) -[arg3]-> (仅包含数字)

// 创建“检查操作数有效性”内部数据流
(*, 操作数1) -[数据流]-> (*, 检查, 操作数1, 不为空)
(*, 操作数2) -[数据流]-> (*, 检查, 操作数2, 不为空)
(*, 操作数1) -[数据流]-> (*, 检查, 操作数1, 仅包含数字)
(*, 操作数2) -[数据流]-> (*, 检查, 操作数2, 仅包含数字)

// 创建子操作与API的关联
(*, 检查, 操作数1, 不为空) -[API引用]-> (id=311, name="validation/string/not_empty", type="API", category="validation/string")
(*, 检查, 操作数2, 不为空) -[API引用]-> (id=311, name="validation/string/not_empty", type="API", category="validation/string")
(*, 检查, 操作数1, 仅包含数字) -[API引用]-> (id=312, name="validation/string/only_digits", type="API", category="validation/string")
(*, 检查, 操作数2, 仅包含数字) -[API引用]-> (id=312, name="validation/string/only_digits", type="API", category="validation/string")

```
#### 2.2 创建空结果字符串
```

// 点组结构操作点
(id=161, name="创建新字符串", type="操作：点组结构", operation_type="creation", predicate="创建", args=["新字符串"])
(id=162, name="设置结果变量", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["结果变量"])

// 单点操作
新字符串(id=163, name="新字符串", type="数据", data_type="string", value="")

// 创建“创建空结果字符串”内部时序关系
(*, 创建, 空结果字符串) -[时序]-> (*, 创建, 新字符串)
(*, 创建, 空结果字符串) -[时序]-> (*, 设置, 结果变量)

// 创建“创建空结果字符串”内部顺承关系
(*, 创建, 新字符串) -[顺承]-> (*, 设置, 结果变量)

// 创建“创建空结果字符串”内部组件关系
(*, 创建, 新字符串) -[arg1]-> (创建)
(*, 创建, 新字符串) -[arg2]-> (新字符串)

(*, 设置, 结果变量) -[arg1]-> (设置)
(*, 设置, 结果变量) -[arg2]-> (结果)

// 创建“创建空结果字符串”内部数据流
(*, 创建, 新字符串) -[数据流]-> (*, 新字符串)
(*, 新字符串) -[数据流]-> (*, 设置, 结果变量)
(*, 设置, 结果变量) -[数据流]-> (*, 结果)

// 创建子操作与API的关联
(*, 创建, 新字符串) -[API引用]-> (id=321, name="string/basic/create_empty_string", type="API", category="string/basic")
(*, 设置, 结果变量) -[API引用]-> (id=322, name="variable/basic/set_value", type="API", category="variable/basic")

```
#### 2.3 设置进位为0
```

// 点组结构操作点
(id=171, name="初始化进位变量", type="操作：点组结构", operation_type="initialization", predicate="初始化", args=["进位变量"])
(id=172, name="设置变量值", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["变量值"])

// 创建“设置进位为0”内部时序关系
(*, 设置, 进位, 为, 0) -[时序]-> (*, 初始化, 进位变量)
(*, 设置, 进位, 为, 0) -[时序]-> (*, 设置, 变量值)

// 创建“设置进位为0”内部顺承关系
(*, 初始化, 进位变量) -[顺承]-> (*, 设置, 变量值)

// 创建“设置进位为0”内部组件关系
(*, 初始化, 进位变量) -[arg1]-> (初始化)
(*, 初始化, 进位变量) -[arg2]-> (进位)

(*, 设置, 变量值) -[arg1]-> (设置)
(*, 设置, 变量值) -[arg2]-> (进位)
(*, 设置, 变量值) -[arg3]-> (为)
(*, 设置, 变量值) -[arg4]-> (零)

// 创建“设置进位为0”内部数据流
(*, 初始化, 进位变量) -[数据流]-> (*, 进位)
(*, 零) -[数据流]-> (*, 设置, 变量值)
(*, 设置, 变量值) -[数据流]-> (*, 进位)

// 创建子操作与API的关联
(*, 初始化, 进位变量) -[API引用]-> (id=331, name="variable/basic/initialize_variable", type="API", category="variable/basic")
(*, 设置, 变量值) -[API引用]-> (id=332, name="variable/basic/set_value", type="API", category="variable/basic")

```
#### 2.4 计算初始位索引
```

// 点组结构操作点
(id=181, name="获取操作数1长度", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["操作数1长度"])
(id=182, name="获取操作数2长度", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["操作数2长度"])
(id=183, name="比较两个长度", type="操作：点组结构", operation_type="comparison", predicate="比较", args=["两个长度"])
(id=184, name="选择较小值", type="操作：点组结构", operation_type="selection", predicate="选择", args=["较小值"])
(id=185, name="减去1", type="操作：点组结构", operation_type="subtraction", predicate="减去", args=["1"])
(id=186, name="设置当前位索引", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["当前位索引"])

// 单点操作
长度(id=187, name="长度", type="操作", operation_type="measurement")
比较(id=188, name="比较", type="操作", operation_type="comparison")
选择(id=189, name="选择", type="操作", operation_type="selection")
减去(id=190, name="减去", type="操作", operation_type="subtraction")

// 数据点
操作数1长度(id=191, name="操作数1长度", type="数据", data_type="int")
操作数2长度(id=192, name="操作数2长度", type="数据", data_type="int")
较小长度(id=193, name="较小长度", type="数据", data_type="int")
一(id=194, name="1", type="数据", data_type="int", value=1)
索引值(id=195, name="索引值", type="数据", data_type="int")

// 创建“计算初始位索引”内部时序关系
(*, 计算, 初始位索引) -[时序]-> (*, 获取, 操作数1长度)
(*, 计算, 初始位索引) -[时序]-> (*, 获取, 操作数2长度)
(*, 计算, 初始位索引) -[时序]-> (*, 比较, 两个长度)
(*, 计算, 初始位索引) -[时序]-> (*, 选择, 较小值)
(*, 计算, 初始位索引) -[时序]-> (*, 减去, 1)
(*, 计算, 初始位索引) -[时序]-> (*, 设置, 当前位索引)

// 创建“计算初始位索引”内部顺承关系
(*, 获取, 操作数1长度) -[顺承]-> (*, 获取, 操作数2长度)
(*, 获取, 操作数2长度) -[顺承]-> (*, 比较, 两个长度)
(*, 比较, 两个长度) -[顺承]-> (*, 选择, 较小值)
(*, 选择, 较小值) -[顺承]-> (*, 减去, 1)
(*, 减去, 1) -[顺承]-> (*, 设置, 当前位索引)

// 创建“计算初始位索引”内部组件关系
(*, 获取, 操作数1长度) -[arg1]-> (获取)
(*, 获取, 操作数1长度) -[arg2]-> (操作数1)
(*, 获取, 操作数1长度) -[arg3]-> (长度)

(*, 获取, 操作数2长度) -[arg1]-> (获取)
(*, 获取, 操作数2长度) -[arg2]-> (操作数2)
(*, 获取, 操作数2长度) -[arg3]-> (长度)

(*, 比较, 两个长度) -[arg1]-> (比较)
(*, 比较, 两个长度) -[arg2]-> (操作数1长度)
(*, 比较, 两个长度) -[arg3]-> (操作数2长度)

(*, 选择, 较小值) -[arg1]-> (选择)
(*, 选择, 较小值) -[arg2]-> (操作数1长度)
(*, 选择, 较小值) -[arg3]-> (操作数2长度)

(*, 减去, 1) -[arg1]-> (减去)
(*, 减去, 1) -[arg2]-> (较小长度)
(*, 减去, 1) -[arg3]-> (一)

(*, 设置, 当前位索引) -[arg1]-> (设置)
(*, 设置, 当前位索引) -[arg2]-> (当前位索引)

// 创建“计算初始位索引”内部数据流
(*, 操作数1) -[数据流]-> (*, 获取, 操作数1长度)
(*, 获取, 操作数1长度) -[数据流]-> (*, 操作数1长度)

(*, 操作数2) -[数据流]-> (*, 获取, 操作数2长度)
(*, 获取, 操作数2长度) -[数据流]-> (*, 操作数2长度)

(*, 操作数1长度) -[数据流]-> (*, 比较, 两个长度)
(*, 操作数2长度) -[数据流]-> (*, 比较, 两个长度)

(*, 操作数1长度) -[数据流]-> (*, 选择, 较小值)
(*, 操作数2长度) -[数据流]-> (*, 选择, 较小值)
(*, 比较, 两个长度) -[数据流]-> (*, 选择, 较小值)
(*, 选择, 较小值) -[数据流]-> (*, 较小长度)

(*, 较小长度) -[数据流]-> (*, 减去, 1)
(*, 一) -[数据流]-> (*, 减去, 1)
(*, 减去, 1) -[数据流]-> (*, 索引值)

(*, 索引值) -[数据流]-> (*, 设置, 当前位索引)
(*, 设置, 当前位索引) -[数据流]-> (*, 当前位索引)

// 创建子操作与API的关联
(*, 获取, 操作数1长度) -[API引用]-> (id=341, name="string/basic/get_length", type="API", category="string/basic")
(*, 获取, 操作数2长度) -[API引用]-> (id=341, name="string/basic/get_length", type="API", category="string/basic")
(*, 比较, 两个长度) -[API引用]-> (id=342, name="comparison/basic/compare_values", type="API", category="comparison/basic")
(*, 选择, 较小值) -[API引用]-> (id=343, name="math/basic/min", type="API", category="math/basic")
(*, 减去, 1) -[API引用]-> (id=344, name="math/basic/subtract", type="API", category="math/basic")
(*, 设置, 当前位索引) -[API引用]-> (id=345, name="variable/basic/set_value", type="API", category="variable/basic")
```

### 3. 处理位循环的图谱导入形式

接下来，我们细化处理位循环的图谱导入形式：

```
// 点的详细属性
位1(id=6, name="位1", type="数据", data_type="int", value=0)
位2(id=7, name="位2", type="数据", data_type="int", value=0)
当前位结果(id=8, name="当前位结果", type="数据", data_type="int", value=0)
和(id=9, name="和", type="数据", data_type="int", value=0)

// 点组结构控制点
(id=19, name="检查循环条件", type="控制：点组结构", control_type="condition_check", predicate="检查", args=["循环条件"])

// 点组结构操作点
(id=20, name="获取当前位", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前位"])
(id=21, name="计算当前位和进位", type="操作：点组结构", operation_type="computation", predicate="计算", args=["当前位", "和", "进位"])
(id=22, name="更新结果", type="操作：点组结构", operation_type="update", predicate="更新", args=["结果"])
(id=23, name="移动到下一位", type="操作：点组结构", operation_type="movement", predicate="移动", args=["到", "下一位"])
(id=24, name="获取当前位索引", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前位索引"])
(id=25, name="比较与零", type="操作：点组结构", operation_type="comparison", predicate="比较", args=["与零"])

// 单点操作
检查(id=110, name="检查", type="操作", operation_type="validation")
获取(id=112, name="获取", type="操作", operation_type="retrieval")
计算(id=114, name="计算", type="操作", operation_type="computation")
和(id=115, name="和", type="操作", operation_type="addition")
更新(id=116, name="更新", type="操作", operation_type="update")
移动(id=117, name="移动", type="操作", operation_type="movement")
比较(id=120, name="比较", type="操作", operation_type="comparison")

// 单点控制
循环条件(id=111, name="循环条件", type="控制", control_type="loop")
与零(id=121, name="与零", type="控制", control_type="zero_check")

// 数据点
当前位(id=113, name="当前位", type="数据", data_type="int")
到(id=118, name="到", type="连接词")
下一位(id=119, name="下一位", type="数据", data_type="int")

// API点
(id=35, name="comparison/basic/greater_or_equal_than_zero", type="API", category="comparison/basic")
(id=36, name="variable/basic/get_value", type="API", category="variable/basic")

// 创建处理位循环与子操作的时序关系
(*, 处理, 位循环) -[时序]-> (*, 检查, 循环条件)
(*, 处理, 位循环) -[时序]-> (*, 获取, 当前位)
(*, 处理, 位循环) -[时序]-> (*, 计算, 当前位, 和, 进位)
(*, 处理, 位循环) -[时序]-> (*, 更新, 结果)
(*, 处理, 位循环) -[时序]-> (*, 移动, 到, 下一位)
// 创建点组结构与单点的组件关系
(*, 检查, 循环条件) -[arg1]-> (检查)
(*, 检查, 循环条件) -[arg2]-> (循环条件)

(*, 获取, 当前位) -[arg1]-> (获取)
(*, 获取, 当前位) -[arg2]-> (当前位)

(*, 计算, 当前位, 和, 进位) -[arg1]-> (计算)
(*, 计算, 当前位, 和, 进位) -[arg2]-> (当前位)
(*, 计算, 当前位, 和, 进位) -[arg3]-> (和)
(*, 计算, 当前位, 和, 进位) -[arg4]-> (进位)

(*, 更新, 结果) -[arg1]-> (更新)
(*, 更新, 结果) -[arg2]-> (结果)

(*, 移动, 到, 下一位) -[arg1]-> (移动)
(*, 移动, 到, 下一位) -[arg2]-> (到)
(*, 移动, 到, 下一位) -[arg3]-> (下一位)
// 创建处理位循环子操作之间的顺承关系
(*, 检查, 循环条件) -[顺承]-> (*, 获取, 当前位)
(*, 获取, 当前位) -[顺承]-> (*, 计算, 当前位, 和, 进位)
(*, 计算, 当前位, 和, 进位) -[顺承]-> (*, 更新, 结果)
(*, 更新, 结果) -[顺承]-> (*, 移动, 到, 下一位)
(*, 移动, 到, 下一位) -[循环条件]-> (*, 检查, 循环条件)


```
#### 3.1 检查循环条件
```

// 点组结构操作点
(id=201, name="获取当前位索引值", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前位索引值"])

// 点组结构控制点
(id=202, name="判断是否大于等于零", type="控制：点组结构", control_type="condition_check", predicate="判断", args=["是否大于等于零"])
(id=203, name="设置循环结果", type="控制：点组结构", control_type="result_setting", predicate="设置", args=["循环结果"])

// 单点操作
判断(id=204, name="判断", type="操作", operation_type="condition_check")

// 单点控制
大于等于零(id=205, name="大于等于零", type="控制", control_type="comparison")
循环结果(id=206, name="循环结果", type="控制", control_type="result")

// 数据点
当前索引值(id=207, name="当前索引值", type="数据", data_type="int")
判断结果(id=208, name="判断结果", type="数据", data_type="boolean")

// 创建“检查循环条件”内部时序关系
(*, 检查, 循环条件) -[时序]-> (*, 获取, 当前位索引值)
(*, 检查, 循环条件) -[时序]-> (*, 判断, 是否大于等于零)
(*, 检查, 循环条件) -[时序]-> (*, 设置, 循环结果)

// 创建“检查循环条件”内部顺承关系
(*, 获取, 当前位索引值) -[顺承]-> (*, 判断, 是否大于等于零)
(*, 判断, 是否大于等于零) -[顺承]-> (*, 设置, 循环结果)

// 创建“检查循环条件”内部组件关系
(*, 获取, 当前位索引值) -[arg1]-> (获取)
(*, 获取, 当前位索引值) -[arg2]-> (当前位索引)

(*, 判断, 是否大于等于零) -[arg1]-> (判断)
(*, 判断, 是否大于等于零) -[arg2]-> (当前索引值)
(*, 判断, 是否大于等于零) -[arg3]-> (大于等于零)

(*, 设置, 循环结果) -[arg1]-> (设置)
(*, 设置, 循环结果) -[arg2]-> (循环结果)

// 创建“检查循环条件”内部数据流
(*, 当前位索引) -[数据流]-> (*, 获取, 当前位索引值)
(*, 获取, 当前位索引值) -[数据流]-> (*, 当前索引值)
(*, 当前索引值) -[数据流]-> (*, 判断, 是否大于等于零)
(*, 判断, 是否大于等于零) -[数据流]-> (*, 判断结果)
(*, 判断结果) -[数据流]-> (*, 设置, 循环结果)

// 创建子操作与API的关联
(*, 获取, 当前位索引值) -[API引用]-> (API变量获取值)
(*, 判断, 是否大于等于零) -[API引用]-> (API比较大于等于零)
(*, 设置, 循环结果) -[API引用]-> (API设置循环结果)

```
#### 3.2 获取当前位
```

// 点组结构操作点
(id=211, name="获取操作数1当前位", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["操作数1当前位"])
(id=212, name="获取操作数2当前位", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["操作数2当前位"])
(id=213, name="设置位1值", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["位1值"])
(id=214, name="设置位2值", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["位2值"])

// 点组结构控制点
(id=215, name="检查操作数1长度", type="控制：点组结构", control_type="condition_check", predicate="检查", args=["操作数1长度"])
(id=216, name="检查操作数2长度", type="控制：点组结构", control_type="condition_check", predicate="检查", args=["操作数2长度"])

// 单点操作
反向获取字符(id=217, name="反向获取字符", type="操作", operation_type="retrieval")
转换为数字(id=218, name="转换为数字", type="操作", operation_type="conversion")

// 单点控制
判断索引有效(id=219, name="判断索引有效", type="控制", control_type="validation")

// 数据点
字符(id=220, name="字符", type="数据", data_type="char")
数字值(id=221, name="数字值", type="数据", data_type="int")

// 创建“获取当前位”内部时序关系
(*, 获取, 当前位) -[时序]-> (*, 检查, 操作数1长度)
(*, 获取, 当前位) -[时序]-> (*, 检查, 操作数2长度)
(*, 获取, 当前位) -[时序]-> (*, 获取, 操作数1当前位)
(*, 获取, 当前位) -[时序]-> (*, 获取, 操作数2当前位)
(*, 获取, 当前位) -[时序]-> (*, 设置, 位1值)
(*, 获取, 当前位) -[时序]-> (*, 设置, 位2值)

// 创建“获取当前位”内部顺承关系
(*, 检查, 操作数1长度) -[顺承]-> (*, 检查, 操作数2长度)
(*, 检查, 操作数2长度) -[顺承]-> (*, 获取, 操作数1当前位)
(*, 获取, 操作数1当前位) -[顺承]-> (*, 获取, 操作数2当前位)
(*, 获取, 操作数2当前位) -[顺承]-> (*, 设置, 位1值)
(*, 设置, 位1值) -[顺承]-> (*, 设置, 位2值)

// 创建“获取当前位”内部组件关系
(*, 检查, 操作数1长度) -[arg1]-> (检查)
(*, 检查, 操作数1长度) -[arg2]-> (操作数1)
(*, 检查, 操作数1长度) -[arg3]-> (判断索引有效)

(*, 检查, 操作数2长度) -[arg1]-> (检查)
(*, 检查, 操作数2长度) -[arg2]-> (操作数2)
(*, 检查, 操作数2长度) -[arg3]-> (判断索引有效)

(*, 获取, 操作数1当前位) -[arg1]-> (获取)
(*, 获取, 操作数1当前位) -[arg2]-> (操作数1)
(*, 获取, 操作数1当前位) -[arg3]-> (当前位索引)
(*, 获取, 操作数1当前位) -[arg4]-> (反向获取字符)

(*, 获取, 操作数2当前位) -[arg1]-> (获取)
(*, 获取, 操作数2当前位) -[arg2]-> (操作数2)
(*, 获取, 操作数2当前位) -[arg3]-> (当前位索引)
(*, 获取, 操作数2当前位) -[arg4]-> (反向获取字符)

(*, 设置, 位1值) -[arg1]-> (设置)
(*, 设置, 位1值) -[arg2]-> (位1)
(*, 设置, 位1值) -[arg3]-> (转换为数字)

(*, 设置, 位2值) -[arg1]-> (设置)
(*, 设置, 位2值) -[arg2]-> (位2)
(*, 设置, 位2值) -[arg3]-> (转换为数字)

// 创建“获取当前位”内部数据流
(*, 操作数1) -[数据流]-> (*, 检查, 操作数1长度)
(*, 当前位索引) -[数据流]-> (*, 检查, 操作数1长度)

(*, 操作数2) -[数据流]-> (*, 检查, 操作数2长度)
(*, 当前位索引) -[数据流]-> (*, 检查, 操作数2长度)

(*, 操作数1) -[数据流]-> (*, 获取, 操作数1当前位)
(*, 当前位索引) -[数据流]-> (*, 获取, 操作数1当前位)
(*, 获取, 操作数1当前位) -[数据流]-> (*, 字符)

(*, 操作数2) -[数据流]-> (*, 获取, 操作数2当前位)
(*, 当前位索引) -[数据流]-> (*, 获取, 操作数2当前位)
(*, 获取, 操作数2当前位) -[数据流]-> (*, 字符)

(*, 字符) -[数据流]-> (*, 转换为数字)
(*, 转换为数字) -[数据流]-> (*, 数字值)

(*, 数字值) -[数据流]-> (*, 设置, 位1值)
(*, 设置, 位1值) -[数据流]-> (*, 位1)

(*, 数字值) -[数据流]-> (*, 设置, 位2值)
(*, 设置, 位2值) -[数据流]-> (*, 位2)

// 创建子操作与API的关联
(*, 检查, 操作数1长度) -[API引用]-> (API字符串检查索引有效)
(*, 检查, 操作数2长度) -[API引用]-> (API字符串检查索引有效)
(*, 获取, 操作数1当前位) -[API引用]-> (API字符串反向获取字符)
(*, 获取, 操作数2当前位) -[API引用]-> (API字符串反向获取字符)
(*, 设置, 位1值) -[API引用]-> (API字符转数字)
(*, 设置, 位2值) -[API引用]-> (API字符转数字)

// 创建处理位循环子操作与底层API的关联
(*, 检查, 循环条件) -[API引用]-> (API比较大于等于零)
(*, 获取, 当前位索引) -[API引用]-> (API变量获取值)

```
#### 3.3 计算当前位和进位
```

// 点组结构操作点
(id=231, name="计算位1加位2", type="操作：点组结构", operation_type="addition", predicate="计算", args=["位1加位2"])
(id=232, name="计算中间结果加进位", type="操作：点组结构", operation_type="addition", predicate="计算", args=["中间结果加进位"])
(id=233, name="计算当前位结果", type="操作：点组结构", operation_type="modulo", predicate="计算", args=["当前位结果"])
(id=234, name="计算新进位", type="操作：点组结构", operation_type="division", predicate="计算", args=["新进位"])

// 单点操作
加法(id=235, name="加法", type="操作", operation_type="addition")
取模(id=236, name="取模", type="操作", operation_type="modulo")
除法(id=237, name="除法", type="操作", operation_type="division")

// 数据点
中间结果(id=238, name="中间结果", type="数据", data_type="int")
总和(id=239, name="总和", type="数据", data_type="int")
十(id=240, name="10", type="数据", data_type="int", value=10)

// 创建“计算当前位和进位”内部时序关系
(*, 计算, 当前位, 和, 进位) -[时序]-> (*, 计算, 位1加位2)
(*, 计算, 当前位, 和, 进位) -[时序]-> (*, 计算, 中间结果加进位)
(*, 计算, 当前位, 和, 进位) -[时序]-> (*, 计算, 当前位结果)
(*, 计算, 当前位, 和, 进位) -[时序]-> (*, 计算, 新进位)

// 创建“计算当前位和进位”内部顺承关系
(*, 计算, 位1加位2) -[顺承]-> (*, 计算, 中间结果加进位)
(*, 计算, 中间结果加进位) -[顺承]-> (*, 计算, 当前位结果)
(*, 计算, 当前位结果) -[顺承]-> (*, 计算, 新进位)

// 创建“计算当前位和进位”内部组件关系
(*, 计算, 位1加位2) -[arg1]-> (计算)
(*, 计算, 位1加位2) -[arg2]-> (位1)
(*, 计算, 位1加位2) -[arg3]-> (加法)
(*, 计算, 位1加位2) -[arg4]-> (位2)

(*, 计算, 中间结果加进位) -[arg1]-> (计算)
(*, 计算, 中间结果加进位) -[arg2]-> (中间结果)
(*, 计算, 中间结果加进位) -[arg3]-> (加法)
(*, 计算, 中间结果加进位) -[arg4]-> (进位)

(*, 计算, 当前位结果) -[arg1]-> (计算)
(*, 计算, 当前位结果) -[arg2]-> (总和)
(*, 计算, 当前位结果) -[arg3]-> (取模)
(*, 计算, 当前位结果) -[arg4]-> (十)

(*, 计算, 新进位) -[arg1]-> (计算)
(*, 计算, 新进位) -[arg2]-> (总和)
(*, 计算, 新进位) -[arg3]-> (除法)
(*, 计算, 新进位) -[arg4]-> (十)

// 创建“计算当前位和进位”内部数据流
(*, 位1) -[数据流]-> (*, 计算, 位1加位2)
(*, 位2) -[数据流]-> (*, 计算, 位1加位2)
(*, 计算, 位1加位2) -[数据流]-> (*, 中间结果)

(*, 中间结果) -[数据流]-> (*, 计算, 中间结果加进位)
(*, 进位) -[数据流]-> (*, 计算, 中间结果加进位)
(*, 计算, 中间结果加进位) -[数据流]-> (*, 总和)

(*, 总和) -[数据流]-> (*, 计算, 当前位结果)
(*, 十) -[数据流]-> (*, 计算, 当前位结果)
(*, 计算, 当前位结果) -[数据流]-> (*, 当前位结果)

(*, 总和) -[数据流]-> (*, 计算, 新进位)
(*, 十) -[数据流]-> (*, 计算, 新进位)
(*, 计算, 新进位) -[数据流]-> (*, 进位)

// 创建子操作与API的关联
(*, 计算, 位1加位2) -[API引用]-> (API数学加法)
(*, 计算, 中间结果加进位) -[API引用]-> (API数学加法)
(*, 计算, 当前位结果) -[API引用]-> (API数学取模10)
(*, 计算, 新进位) -[API引用]-> (API数学除以10)

// 创建处理位循环内部数据流
(*, 获取, 当前位) -[数据流]-> (*, 计算, 当前位, 和, 进位)
(*, 进位) -[数据流]-> (*, 计算, 当前位, 和, 进位)

(*, 计算, 当前位, 和, 进位) -[数据流]-> (*, 进位)
(*, 计算, 当前位, 和, 进位) -[数据流]-> (*, 更新, 结果)

```
#### 3.4 更新结果
```

// 点组结构操作点
(id=251, name="获取当前结果值", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前结果值"])
(id=252, name="获取当前位结果值", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前位结果值"])
(id=253, name="转换为字符", type="操作：点组结构", operation_type="conversion", predicate="转换", args=["为字符"])
(id=254, name="添加到结果前", type="操作：点组结构", operation_type="concatenation", predicate="添加", args=["到结果前"])
(id=255, name="设置新结果", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["新结果"])

// 单点操作
转换(id=256, name="转换", type="操作", operation_type="conversion")
添加(id=257, name="添加", type="操作", operation_type="concatenation")

// 数据点
当前结果字符串(id=258, name="当前结果字符串", type="数据", data_type="string")
当前位字符(id=259, name="当前位字符", type="数据", data_type="char")
新结果字符串(id=260, name="新结果字符串", type="数据", data_type="string")

// 创建“更新结果”内部时序关系
(*, 更新, 结果) -[时序]-> (*, 获取, 当前结果值)
(*, 更新, 结果) -[时序]-> (*, 获取, 当前位结果值)
(*, 更新, 结果) -[时序]-> (*, 转换, 为字符)
(*, 更新, 结果) -[时序]-> (*, 添加, 到结果前)
(*, 更新, 结果) -[时序]-> (*, 设置, 新结果)

// 创建“更新结果”内部顺承关系
(*, 获取, 当前结果值) -[顺承]-> (*, 获取, 当前位结果值)
(*, 获取, 当前位结果值) -[顺承]-> (*, 转换, 为字符)
(*, 转换, 为字符) -[顺承]-> (*, 添加, 到结果前)
(*, 添加, 到结果前) -[顺承]-> (*, 设置, 新结果)

// 创建“更新结果”内部组件关系
(*, 获取, 当前结果值) -[arg1]-> (获取)
(*, 获取, 当前结果值) -[arg2]-> (结果)

(*, 获取, 当前位结果值) -[arg1]-> (获取)
(*, 获取, 当前位结果值) -[arg2]-> (当前位结果)

(*, 转换, 为字符) -[arg1]-> (转换)
(*, 转换, 为字符) -[arg2]-> (当前位结果)

(*, 添加, 到结果前) -[arg1]-> (添加)
(*, 添加, 到结果前) -[arg2]-> (当前位字符)
(*, 添加, 到结果前) -[arg3]-> (当前结果字符串)

(*, 设置, 新结果) -[arg1]-> (设置)
(*, 设置, 新结果) -[arg2]-> (结果)
(*, 设置, 新结果) -[arg3]-> (新结果字符串)

// 创建“更新结果”内部数据流
(*, 结果) -[数据流]-> (*, 获取, 当前结果值)
(*, 获取, 当前结果值) -[数据流]-> (*, 当前结果字符串)

(*, 当前位结果) -[数据流]-> (*, 获取, 当前位结果值)
(*, 获取, 当前位结果值) -[数据流]-> (*, 转换, 为字符)

(*, 转换, 为字符) -[数据流]-> (*, 当前位字符)
(*, 当前位字符) -[数据流]-> (*, 添加, 到结果前)
(*, 当前结果字符串) -[数据流]-> (*, 添加, 到结果前)

(*, 添加, 到结果前) -[数据流]-> (*, 新结果字符串)
(*, 新结果字符串) -[数据流]-> (*, 设置, 新结果)
(*, 设置, 新结果) -[数据流]-> (*, 结果)

// 创建子操作与API的关联
(*, 获取, 当前结果值) -[API引用]-> (API变量获取值)
(*, 获取, 当前位结果值) -[API引用]-> (API变量获取值)
(*, 转换, 为字符) -[API引用]-> (API数字转字符)
(*, 添加, 到结果前) -[API引用]-> (API字符串前置字符)
(*, 设置, 新结果) -[API引用]-> (API变量设置值)

(*, 当前位索引) -[数据流]-> (*, 移动, 到, 下一位)

```
#### 3.5 移动到下一位
```

// 点组结构操作点
(id=271, name="获取当前索引", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前索引"])
(id=272, name="减少索引", type="操作：点组结构", operation_type="subtraction", predicate="减少", args=["索引"])
(id=273, name="设置新索引", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["新索引"])

// 单点操作
减法(id=274, name="减法", type="操作", operation_type="subtraction")

// 数据点
当前索引值(id=275, name="当前索引值", type="数据", data_type="int")
一(id=276, name="1", type="数据", data_type="int", value=1)
新索引值(id=277, name="新索引值", type="数据", data_type="int")

// 创建“移动到下一位”内部时序关系
(*, 移动, 到, 下一位) -[时序]-> (*, 获取, 当前索引)
(*, 移动, 到, 下一位) -[时序]-> (*, 减少, 索引)
(*, 移动, 到, 下一位) -[时序]-> (*, 设置, 新索引)

// 创建“移动到下一位”内部顺承关系
(*, 获取, 当前索引) -[顺承]-> (*, 减少, 索引)
(*, 减少, 索引) -[顺承]-> (*, 设置, 新索引)

// 创建“移动到下一位”内部组件关系
(*, 获取, 当前索引) -[arg1]-> (获取)
(*, 获取, 当前索引) -[arg2]-> (当前位索引)

(*, 减少, 索引) -[arg1]-> (减法)
(*, 减少, 索引) -[arg2]-> (当前索引值)
(*, 减少, 索引) -[arg3]-> (一)

(*, 设置, 新索引) -[arg1]-> (设置)
(*, 设置, 新索引) -[arg2]-> (当前位索引)
(*, 设置, 新索引) -[arg3]-> (新索引值)

// 创建“移动到下一位”内部数据流
(*, 当前位索引) -[数据流]-> (*, 获取, 当前索引)
(*, 获取, 当前索引) -[数据流]-> (*, 当前索引值)

(*, 当前索引值) -[数据流]-> (*, 减少, 索引)
(*, 一) -[数据流]-> (*, 减少, 索引)
(*, 减少, 索引) -[数据流]-> (*, 新索引值)

(*, 新索引值) -[数据流]-> (*, 设置, 新索引)
(*, 设置, 新索引) -[数据流]-> (*, 当前位索引)

// 创建子操作与API的关联
(*, 获取, 当前索引) -[API引用]-> (API变量获取值)
(*, 减少, 索引) -[API引用]-> (API数学减1)
(*, 设置, 新索引) -[API引用]-> (API变量设置值)
```

### 4. 处理最终进位的图谱导入形式

接下来，我们细化处理最终进位的图谱导入形式：

```

// 点的详细属性

// 点组结构控制点
(id=26, name="检查进位是否大于零", type="控制：点组结构", control_type="condition_check", predicate="检查", args=["进位", "是否", "大于", "零"])
(id=28, name="跳过进位处理", type="控制：点组结构", control_type="skip", predicate="跳过", args=["进位", "处理"])

```
#### 4.1 检查进位是否大于零
```

// 点组结构操作点
(id=281, name="获取进位值", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["进位值"])

// 点组结构控制点
(id=282, name="比较进位与零", type="控制：点组结构", control_type="comparison", predicate="比较", args=["进位与零"])
(id=283, name="设置判断结果", type="控制：点组结构", control_type="result_setting", predicate="设置", args=["判断结果"])

// 单点操作
比较大于(id=284, name="比较大于", type="操作", operation_type="comparison", comparison_type="greater_than")

// 数据点
进位值(id=285, name="进位值", type="数据", data_type="int")
判断结果(id=286, name="判断结果", type="数据", data_type="boolean")

// 创建“检查进位是否大于零”内部时序关系
(*, 检查, 进位, 是否, 大于, 零) -[时序]-> (*, 获取, 进位值)
(*, 检查, 进位, 是否, 大于, 零) -[时序]-> (*, 比较, 进位与零)
(*, 检查, 进位, 是否, 大于, 零) -[时序]-> (*, 设置, 判断结果)

// 创建“检查进位是否大于零”内部顺承关系
(*, 获取, 进位值) -[顺承]-> (*, 比较, 进位与零)
(*, 比较, 进位与零) -[顺承]-> (*, 设置, 判断结果)

// 创建“检查进位是否大于零”内部组件关系
(*, 获取, 进位值) -[arg1]-> (获取)
(*, 获取, 进位值) -[arg2]-> (进位)

(*, 比较, 进位与零) -[arg1]-> (比较大于)
(*, 比较, 进位与零) -[arg2]-> (进位值)
(*, 比较, 进位与零) -[arg3]-> (零)

(*, 设置, 判断结果) -[arg1]-> (设置)
(*, 设置, 判断结果) -[arg2]-> (判断结果)

// 创建“检查进位是否大于零”内部数据流
(*, 进位) -[数据流]-> (*, 获取, 进位值)
(*, 获取, 进位值) -[数据流]-> (*, 进位值)

(*, 进位值) -[数据流]-> (*, 比较, 进位与零)
(*, 零) -[数据流]-> (*, 比较, 进位与零)
(*, 比较, 进位与零) -[数据流]-> (*, 判断结果)

(*, 判断结果) -[数据流]-> (*, 设置, 判断结果)

// 创建子操作与API的关联
(*, 获取, 进位值) -[API引用]-> (API变量获取进位值)
(*, 比较, 进位与零) -[API引用]-> (API比较大于零)
(*, 设置, 判断结果) -[API引用]-> (API设置判断结果)

// 点组结构操作点
(id=27, name="添加进位到结果", type="操作：点组结构", operation_type="addition", predicate="添加", args=["进位", "到", "结果"])

```
#### 4.2 添加进位到结果
```

// 点组结构操作点
(id=291, name="获取进位值", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["进位值"])
(id=292, name="获取当前结果", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["当前结果"])
(id=293, name="转换进位为字符串", type="操作：点组结构", operation_type="conversion", predicate="转换", args=["进位为字符串"])
(id=294, name="在结果前添加进位", type="操作：点组结构", operation_type="concatenation", predicate="在结果前添加", args=["进位"])
(id=295, name="设置新结果", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["新结果"])

// 单点操作
转换(id=296, name="转换", type="操作", operation_type="conversion")
连接(id=297, name="连接", type="操作", operation_type="concatenation")

// 数据点
进位字符串(id=298, name="进位字符串", type="数据", data_type="string")
当前结果字符串(id=299, name="当前结果字符串", type="数据", data_type="string")
新结果字符串(id=300, name="新结果字符串", type="数据", data_type="string")

// 创建“添加进位到结果”内部时序关系
(*, 添加, 进位, 到, 结果) -[时序]-> (*, 获取, 进位值)
(*, 添加, 进位, 到, 结果) -[时序]-> (*, 获取, 当前结果)
(*, 添加, 进位, 到, 结果) -[时序]-> (*, 转换, 进位为字符串)
(*, 添加, 进位, 到, 结果) -[时序]-> (*, 在结果前添加, 进位)
(*, 添加, 进位, 到, 结果) -[时序]-> (*, 设置, 新结果)

// 创建“添加进位到结果”内部顺承关系
(*, 获取, 进位值) -[顺承]-> (*, 获取, 当前结果)
(*, 获取, 当前结果) -[顺承]-> (*, 转换, 进位为字符串)
(*, 转换, 进位为字符串) -[顺承]-> (*, 在结果前添加, 进位)
(*, 在结果前添加, 进位) -[顺承]-> (*, 设置, 新结果)

// 创建“添加进位到结果”内部组件关系
(*, 获取, 进位值) -[arg1]-> (获取)
(*, 获取, 进位值) -[arg2]-> (进位)

(*, 获取, 当前结果) -[arg1]-> (获取)
(*, 获取, 当前结果) -[arg2]-> (结果)

(*, 转换, 进位为字符串) -[arg1]-> (转换)
(*, 转换, 进位为字符串) -[arg2]-> (进位值)

(*, 在结果前添加, 进位) -[arg1]-> (连接)
(*, 在结果前添加, 进位) -[arg2]-> (进位字符串)
(*, 在结果前添加, 进位) -[arg3]-> (当前结果字符串)

(*, 设置, 新结果) -[arg1]-> (设置)
(*, 设置, 新结果) -[arg2]-> (结果)
(*, 设置, 新结果) -[arg3]-> (新结果字符串)

// 创建“添加进位到结果”内部数据流
(*, 进位) -[数据流]-> (*, 获取, 进位值)
(*, 获取, 进位值) -[数据流]-> (*, 进位值)

(*, 结果) -[数据流]-> (*, 获取, 当前结果)
(*, 获取, 当前结果) -[数据流]-> (*, 当前结果字符串)

(*, 进位值) -[数据流]-> (*, 转换, 进位为字符串)
(*, 转换, 进位为字符串) -[数据流]-> (*, 进位字符串)

(*, 进位字符串) -[数据流]-> (*, 在结果前添加, 进位)
(*, 当前结果字符串) -[数据流]-> (*, 在结果前添加, 进位)
(*, 在结果前添加, 进位) -[数据流]-> (*, 新结果字符串)

(*, 新结果字符串) -[数据流]-> (*, 设置, 新结果)
(*, 设置, 新结果) -[数据流]-> (*, 结果)

// 创建子操作与API的关联
(*, 获取, 进位值) -[API引用]-> (API变量获取值)
(*, 获取, 当前结果) -[API引用]-> (API变量获取值)
(*, 转换, 进位为字符串) -[API引用]-> (API数字转字符串)
(*, 在结果前添加, 进位) -[API引用]-> (API字符串连接)
(*, 设置, 新结果) -[API引用]-> (API变量设置值)

// 单点操作
检查(id=130, name="检查", type="操作", operation_type="validation")
大于(id=133, name="大于", type="操作", operation_type="comparison", comparison_type="greater_than")
添加(id=135, name="添加", type="操作", operation_type="addition")
跳过(id=138, name="跳过", type="操作", operation_type="skip")
处理(id=139, name="处理", type="操作", operation_type="process")

// 数据点
进位(id=131, name="进位", type="数据", data_type="int")
是否(id=132, name="是否", type="连接词")
零(id=134, name="零", type="数据", data_type="int", value=0)
到(id=136, name="到", type="连接词")
结果(id=137, name="结果", type="数据", data_type="string")

// API点
(id=37, name="comparison/basic/greater_than_zero", type="API", category="comparison/basic")
(id=38, name="string/basic/prepend_int_to_string", type="API", category="string/basic")
(id=39, name="control/basic/no_operation", type="API", category="control/basic")

// 创建处理最终进位与子操作的时序关系
(*, 处理, 最终进位) -[时序]-> (*, 检查, 进位, 是否, 大于, 零)
(*, 处理, 最终进位) -[时序]-> (*, 添加, 进位, 到, 结果)
(*, 处理, 最终进位) -[时序]-> (*, 跳过, 进位, 处理)

// 创建点组结构与单点的组件关系
(*, 检查, 进位, 是否, 大于, 零) -[arg1]-> (检查)
(*, 检查, 进位, 是否, 大于, 零) -[arg2]-> (进位)
(*, 检查, 进位, 是否, 大于, 零) -[arg3]-> (是否)
(*, 检查, 进位, 是否, 大于, 零) -[arg4]-> (大于)
(*, 检查, 进位, 是否, 大于, 零) -[arg5]-> (零)

(*, 添加, 进位, 到, 结果) -[arg1]-> (添加)
(*, 添加, 进位, 到, 结果) -[arg2]-> (进位)
(*, 添加, 进位, 到, 结果) -[arg3]-> (到)
(*, 添加, 进位, 到, 结果) -[arg4]-> (结果)

(*, 跳过, 进位, 处理) -[arg1]-> (跳过)
(*, 跳过, 进位, 处理) -[arg2]-> (进位)
(*, 跳过, 进位, 处理) -[arg3]-> (处理)
// 创建处理最终进位子操作之间的判断关系
(*, 检查, 进位, 是否, 大于, 零) -[判断首]-> (*, 添加, 进位, 到, 结果)
(*, 检查, 进位, 是否, 大于, 零) -[判断]-> (*, 跳过, 进位, 处理)

// 创建处理最终进位子操作与底层API的关联
(*, 检查, 进位, 是否, 大于, 零) -[API引用]-> (API比较大于零)
(*, 添加, 进位, 到, 结果) -[API引用]-> (API字符串前置字符)
(*, 跳过, 进位, 处理) -[API引用]-> (id=39)

// 创建处理最终进位内部数据流
(*, 进位) -[数据流]-> (*, 检查, 进位, 是否, 大于, 零)
(*, 进位) -[数据流]-> (*, 添加, 进位, 到, 结果)
(*, 结果) -[数据流]-> (*, 添加, 进位, 到, 结果)
(*, 添加, 进位, 到, 结果) -[数据流]-> (*, 结果)
```

### 5. 返回结果的图谱导入形式

最后，我们细化返回结果的图谱导入形式：

```
// 点的详细属性

```
#### 5.1 返回结果操作
```

// 点组结构操作点
(id=29, name="获取结果值", type="操作：点组结构", operation_type="retrieval", predicate="获取", args=["结果", "值"])
(id=30, name="设置返回值", type="操作：点组结构", operation_type="assignment", predicate="设置", args=["返回", "值"])

// 单点操作
获取(id=140, name="获取", type="操作", operation_type="retrieval")
设置(id=143, name="设置", type="操作", operation_type="assignment")
返回(id=144, name="返回", type="操作", operation_type="return")

// 数据点
结果(id=141, name="结果", type="数据", data_type="string")
值(id=142, name="值", type="数据", data_type="string")

// API点
(id=40, name="variable/basic/get_value", type="API", category="variable/basic")
(id=41, name="variable/basic/set_return_value", type="API", category="variable/basic")

// 创建返回结果与子操作的时序关系
(*, 返回, 结果) -[时序]-> (*, 获取, 结果, 值)
(*, 返回, 结果) -[时序]-> (*, 设置, 返回, 值)

// 创建点组结构与单点的组件关系
(*, 获取, 结果, 值) -[arg1]-> (获取)
(*, 获取, 结果, 值) -[arg2]-> (结果)
(*, 获取, 结果, 值) -[arg3]-> (值)

(*, 设置, 返回, 值) -[arg1]-> (设置)
(*, 设置, 返回, 值) -[arg2]-> (返回)
(*, 设置, 返回, 值) -[arg3]-> (值)

// 创建返回结果子操作之间的顺承关系
(*, 获取, 结果, 值) -[顺承]-> (*, 设置, 返回, 值)

// 创建返回结果子操作与底层API的关联
(*, 获取, 结果, 值) -[API引用]-> (API变量获取值)
(*, 设置, 返回, 值) -[API引用]-> (API变量设置返回值)

// 创建返回结果内部数据流
(*, 结果) -[数据流]-> (*, 获取, 结果, 值)
(*, 获取, 结果, 值) -[数据流]-> (*, 设置, 返回, 值)
```

## 五、总结

本文档描述了多位数加法可执行图式的图谱导入形式，基于点边结构表示进一步细化为可直接导入图谱的形式。这种形式更加符合图数据库的存储和查询特性，同时保留了原有点边结构的语义表达能力。

主要特点包括：

1. **详细的点属性**：为每个点添加了详细的属性，包括ID、名称、类型、数据类型、值等，使得点的信息更加完整。

2. **单点操作与控制**：将基本操作和控制元素表示为单点，如检查(type="操作", operation_type="validation")和循环条件(type="控制", control_type="loop")，使得基本元素更加清晰。

3. **点组结构表示**：采用(*, 设置, 进位, 为, 0)等点组形式表示点组结构的操作和控制点，而非简单的(设置进位为0)，使得操作结构更加清晰。

4. **类型区分明确**：清晰区分了操作点和控制点，如type="操作：点组结构"和type="控制：点组结构"，使得点的功能分类更加明确。

5. **组件关系表示**：通过arg1、arg2等组件边表示点组结构与其组成部分的关系，使得图谱结构更加灵活。

6. **底层API关联**：通过API引用边将操作节点与底层API关联起来，使得图式可以直接执行。

这种图谱导入形式可以直接导入到图数据库中，并通过图数据库的查询语言进行查询和分析。同时，这种形式也可以作为图式执行引擎的输入，直接执行图式。

```

这种图谱导入形式可以直接导入到图数据库中，并通过图数据库的查询语言进行查询和分析。同时，这种形式也可以作为图式执行引擎的输入，直接执行图式。

## 子图式作为小点的表示

在上述图谱导入形式中，我们将子图式作为小点来表示，这样可以实现图式的嵌套和复用。具体来说，子图式的表示包含以下几个部分：

1. **子图式定义**：定义子图式的点、边和关系，如“检查操作数有效性”子图式包含了多个点组结构操作点、单点操作、数据点等。

2. **内部时序关系**：定义子图式内部点之间的时序关系，如：
   ```
   (*, 检查, 操作数有效性) -[时序]-> (*, 检查, 操作数1, 不为空)
   ```

3. **内部顺承关系**：定义子图式内部点之间的顺承关系，如：
   ```
   (*, 检查, 操作数1, 不为空) -[顺承]-> (*, 检查, 操作数2, 不为空)
   ```

4. **内部组件关系**：定义子图式内部点组结构与单点的组件关系，如：
   ```
   (*, 检查, 操作数1, 不为空) -[arg1]-> (检查)
   ```

5. **内部数据流**：定义子图式内部点之间的数据流关系，如：
   ```
   (*, 操作数1) -[数据流]-> (*, 检查, 操作数1, 不为空)
   ```

6. **API引用**：定义子图式内部点与底层API的关联，如：
   ```
   (*, 检查, 操作数1, 不为空) -[API引用]-> (id=311, name="validation/string/not_empty", type="API", category="validation/string")
   ```

通过这种方式，我们可以将复杂的操作分解为多个小的子图式，每个子图式可以作为一个独立的小点来使用。这样不仅可以提高图式的复用性，还可以提高图式的可读性和可维护性。

在实际实现中，我们可以将子图式定义为一个独立的图式模板，然后在需要的地方引用这个模板。这样可以实现图式的模块化和复用。