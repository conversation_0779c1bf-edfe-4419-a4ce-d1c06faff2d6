@font-face {
  font-family: "iconfont"; /* Project id 918214 */
  src: url('iconfont.woff2?t=1641395025082') format('woff2'),
       url('iconfont.woff?t=1641395025082') format('woff'),
       url('iconfont.ttf?t=1641395025082') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-screenshot:before {
  content: "\e61c";
}

.icon-explode:before {
  content: "\e67d";
}

.icon-collect:before {
  content: "\e67e";
}

.icon-quick-info-off:before {
  content: "\e67c";
}

.icon-quick-info-on:before {
  content: "\e6b4";
}

.icon-spiral:before {
  content: "\e654";
}

.icon-fly-out:before {
  content: "\e653";
}

.icon-add-line:before {
  content: "\e648";
}

.icon-save-as:before {
  content: "\e644";
}

.icon-save:before {
  content: "\e643";
}

.icon-vr-glasses-off:before {
  content: "\e62c";
}

.icon-show:before {
  content: "\e73e";
}

.icon-download:before {
  content: "\e640";
}

.icon-upload:before {
  content: "\e6d3";
}

.icon-force-disable:before {
  content: "\e621";
}

.icon-select-visible:before {
  content: "\ea75";
}

.icon-add-node:before {
  content: "\e60c";
}

.icon-add-edge:before {
  content: "\e902";
}

.icon-algorithm:before {
  content: "\e63f";
}

.icon-increase-space:before {
  content: "\e635";
}

.icon-decrease-space:before {
  content: "\e638";
}

.icon-rotate-y:before {
  content: "\e630";
}

.icon-rotate-x:before {
  content: "\e632";
}

.icon-rotate-z:before {
  content: "\e631";
}

.icon-grid:before {
  content: "\e62a";
}

.icon-cube:before {
  content: "\e60e";
}

.icon-line:before {
  content: "\e626";
}

.icon-circle:before {
  content: "\e628";
}

.icon-force:before {
  content: "\e629";
}

.icon-hide:before {
  content: "\e625";
}

.icon-warn:before {
  content: "\e60b";
}

.icon-discount:before {
  content: "\e636";
}

.icon-restart:before {
  content: "\e667";
}

.icon-start:before {
  content: "\e614";
}

.icon-stop:before {
  content: "\e624";
}

.icon-play:before {
  content: "\e60a";
}

.icon-edit-2:before {
  content: "\e652";
}

.icon-recent:before {
  content: "\e609";
}

.icon-operation:before {
  content: "\e647";
}

.icon-demo:before {
  content: "\e623";
}

.icon-personal:before {
  content: "\e618";
}

.icon-tag-right:before {
  content: "\e62b";
}

.icon-close:before {
  content: "\e681";
}

.icon-share:before {
  content: "\e622";
}

.icon-un-share:before {
  content: "\e615";
}

.icon-check-all:before {
  content: "\e637";
}

.icon-check-none:before {
  content: "\e76a";
}

.icon-check-some:before {
  content: "\e66c";
}

.icon-logo:before {
  content: "\e608";
}

.icon-survey:before {
  content: "\e60d";
}

.icon-shortcut-fill:before {
  content: "\e8e9";
}

.icon-image:before {
  content: "\f024";
}

.icon-code:before {
  content: "\e607";
}

.icon-node:before {
  content: "\e606";
}

.icon-database:before {
  content: "\e605";
}

.icon-vr-glasses-on:before {
  content: "\e601";
}

.icon-leaf-trimming:before {
  content: "\e613";
}

.icon-forward:before {
  content: "\ea55";
}

.icon-backward:before {
  content: "\e612";
}

.icon-expand:before {
  content: "\e8cb";
}

.icon-inverse:before {
  content: "\e610";
}

.icon-clean:before {
  content: "\e6d1";
}

.icon-delete:before {
  content: "\e634";
}

.icon-rotate-left:before {
  content: "\e604";
}

.icon-remove:before {
  content: "\e73d";
}

.icon-video:before {
  content: "\e63e";
}

.icon-annotate:before {
  content: "\e6ac";
}

.icon-link:before {
  content: "\e61a";
}

.icon-info:before {
  content: "\e72f";
}

.icon-pin:before {
  content: "\e9f2";
}

.icon-un-pin:before {
  content: "\e9f5";
}

.icon-fly-to:before {
  content: "\e957";
}

.icon-tag:before {
  content: "\e603";
}

.icon-rotate-right:before {
  content: "\ea53";
}

.icon-zoom-in:before {
  content: "\e62f";
}

.icon-zoom-out:before {
  content: "\e627";
}

.icon-nav-center:before {
  content: "\e600";
}

.icon-rotate:before {
  content: "\e66b";
}

.icon-reset:before {
  content: "\e633";
}

.icon-nav-up:before {
  content: "\e8fc";
}

.icon-nav-down:before {
  content: "\e8fe";
}

.icon-nav-left:before {
  content: "\e900";
}

.icon-trace-neighbor:before {
  content: "\e602";
}

.icon-nav-right:before {
  content: "\e901";
}

