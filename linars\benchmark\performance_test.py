
import time
from neo4j import GraphDatabase

class PerformanceTester:
    def __init__(self, uri, user, password):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        
    def run_query_test(self, query, params=None):
        """执行查询性能测试"""
        start = time.time()
        with self.driver.session() as session:
            result = session.run(query, params)
            records = list(result)
        duration = time.time() - start
        return len(records), duration
        
    def test_basic_queries(self):
        """基础查询测试集"""
        tests = [
            ("MATCH (n) RETURN count(n)", None),
            ("MATCH (n)-[r]->(m) RETURN count(r)", None),
            ("MATCH path=(n)-[r*3]->(m) RETURN count(path)", None)
        ]
        
        print("基准测试结果:")
        for query, params in tests:
            count, duration = self.run_query_test(query, params)
            print(f"查询: {query[:50]}... 返回{count}条记录, 耗时{duration:.3f}秒")

if __name__ == "__main__":
    tester = PerformanceTester("bolt://localhost:7687", "neo4j", "password")
    tester.test_basic_queries()
