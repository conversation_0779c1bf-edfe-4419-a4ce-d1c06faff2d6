"""
File Controller

This module provides a controller for file operations.
"""
import os
import uuid
from flask import Blueprint, request, jsonify, send_from_directory
from werkzeug.utils import secure_filename

from linars.com.warmer.kgmaker.config.web_app_config import WebAppConfig
from linars.com.warmer.kgmaker.utils.response import R

# 创建蓝图
file_blueprint = Blueprint('file', __name__, url_prefix='/file')

# 文件上传
@file_blueprint.route('/upload', methods=['POST'])
def upload_file():
    """文件上传"""
    result = R()
    
    try:
        # 获取文件
        file = request.files.get('file')
        if not file:
            result.code = 500
            result.set_msg("请选择文件")
            return jsonify(result.to_dict())
        
        # 检查文件类型
        if not WebAppConfig.allowed_file(file.filename):
            result.code = 500
            result.set_msg("不支持的文件类型")
            return jsonify(result.to_dict())
        
        # 保存文件
        filename = secure_filename(file.filename)
        file_path = WebAppConfig.get_location()
        
        # 确保目录存在
        if not os.path.exists(file_path):
            os.makedirs(file_path)
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}_{filename}"
        file.save(os.path.join(file_path, unique_filename))
        
        # 构建文件URL
        server_url = request.host
        file_url = f"http://{server_url}/file/download/{unique_filename}"
        
        # 设置结果
        result.code = 200
        result.set_data({
            "filename": filename,
            "fileUrl": file_url
        })
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")
    
    return jsonify(result.to_dict())

# 文件下载
@file_blueprint.route('/download/<filename>', methods=['GET'])
def download_file(filename):
    """文件下载"""
    # 构建文件路径
    file_path = WebAppConfig.get_location()
    
    # 返回文件
    return send_from_directory(file_path, filename, as_attachment=True)
