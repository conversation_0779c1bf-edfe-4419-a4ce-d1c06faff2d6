# 可执行图式结构模板

本文档提供基于当前系统可执行图式结构模型的模板，用于快速生成案例。

## 一、基本结构模板

### 1. 顺序结构模板

顺序结构是最基本的控制结构，表示按顺序执行的操作序列。

#### 1.1 模板结构

```
// 图式结构
时序主题 -[时序首]-> 步骤1
时序主题 -[时序]-> 步骤2
时序主题 -[时序]-> 步骤3

步骤1 -[顺承]-> 步骤2
步骤2 -[顺承]-> 步骤3
```

#### 1.2 JSON表示

```json
{
  "structure_type": "sequence",
  "theme": "顺序操作",
  "steps": [
    {
      "id": "步骤1",
      "action": "获取文件",
      "parameters": {
        "file_path": "example.txt"
      }
    },
    {
      "id": "步骤2",
      "action": "读取内容",
      "parameters": {
        "encoding": "UTF-8"
      }
    },
    {
      "id": "步骤3",
      "action": "显示内容",
      "parameters": {
        "format": "text"
      }
    }
  ]
}
```

#### 1.3 使用示例

**场景**：打开文件并读取内容

```
// 图式结构
文件操作 -[时序首]-> 打开文件
文件操作 -[时序]-> 读取内容
文件操作 -[时序]-> 关闭文件

打开文件 -[顺承]-> 读取内容
读取内容 -[顺承]-> 关闭文件
```

### 2. 条件结构模板

条件结构表示基于条件的分支执行。

#### 2.1 模板结构

```
// 图式结构
条件主题 -[判断首]-> 条件为真时执行的分支  // then分支
条件主题 -[判断]-> 条件为假时执行的分支  // else分支
```

#### 2.2 JSON表示

```json
{
  "structure_type": "conditional",
  "theme": "条件判断",
  "condition": {
    "type": "comparison",
    "operator": "equals",
    "left_operand": "$variable",
    "right_operand": "value"
  },
  "then_branch": {
    "id": "真分支",
    "action": "执行操作A"
  },
  "else_branch": {
    "id": "假分支",
    "action": "执行操作B"
  }
}
```

#### 2.3 使用示例

**场景**：检查文件是否存在

```
// 图式结构
文件检查 -[判断首]-> 文件存在处理  // 文件存在时执行
文件检查 -[判断]-> 文件不存在处理  // 文件不存在时执行
```

### 3. 循环结构模板

循环结构表示重复执行的操作。

#### 3.1 模板结构

```
// 图式结构
循环主题 -[时序]-> 循环体  // 先执行循环体（do部分）
循环体 -[循环条件]-> 循环主题  // 如果条件满足，返回循环主题继续执行（while部分）
```

#### 3.2 JSON表示

```json
{
  "structure_type": "do_while_loop",
  "theme": "循环操作",
  "loop_body": {
    "id": "循环体",
    "action": "处理元素",
    "parameters": {
      "element": "$current_element"
    }
  },
  "condition": {
    "type": "comparison",
    "operator": "less_than",
    "left_operand": "$counter",
    "right_operand": "$limit"
  }
}
```

#### 3.3 使用示例

**场景**：处理列表中的所有元素

```
// 图式结构
列表处理 -[时序]-> 处理当前元素
处理当前元素 -[循环条件]-> 列表处理  // 如果还有元素，继续循环
```

### 4. 变量绑定模板

变量绑定结构表示变量与实际值的关系。

#### 4.1 模板结构

```
// 图式结构
变量容器 -[变量]-> 变量值1  // 变量1的值
变量容器 -[变量]-> 变量值2  // 变量2的值
```

#### 4.2 JSON表示

```json
{
  "structure_type": "variable_binding",
  "container": "变量容器",
  "variables": [
    {
      "name": "$var1",
      "value": "value1",
      "type": "string"
    },
    {
      "name": "$var2",
      "value": 42,
      "type": "integer"
    }
  ]
}
```

#### 4.3 使用示例

**场景**：设置文件操作的参数

```
// 图式结构
文件参数 -[变量]-> "example.txt"  // 文件路径变量
文件参数 -[变量]-> "UTF-8"  // 编码变量
```

## 二、复合结构模板

### 1. 条件嵌套顺序模板

条件结构中嵌套顺序结构。

#### 1.1 模板结构

```
// 图式结构
条件主题 -[判断首]-> 顺序主题A  // 条件为真时执行顺序A
条件主题 -[判断]-> 顺序主题B  // 条件为假时执行顺序B

顺序主题A -[时序首]-> 步骤A1
顺序主题A -[时序]-> 步骤A2
步骤A1 -[顺承]-> 步骤A2

顺序主题B -[时序首]-> 步骤B1
顺序主题B -[时序]-> 步骤B2
步骤B1 -[顺承]-> 步骤B2
```

#### 1.2 JSON表示

```json
{
  "structure_type": "conditional_with_sequence",
  "theme": "条件顺序操作",
  "condition": {
    "type": "comparison",
    "operator": "equals",
    "left_operand": "$condition",
    "right_operand": true
  },
  "then_branch": {
    "structure_type": "sequence",
    "theme": "顺序A",
    "steps": [
      {
        "id": "步骤A1",
        "action": "操作A1"
      },
      {
        "id": "步骤A2",
        "action": "操作A2"
      }
    ]
  },
  "else_branch": {
    "structure_type": "sequence",
    "theme": "顺序B",
    "steps": [
      {
        "id": "步骤B1",
        "action": "操作B1"
      },
      {
        "id": "步骤B2",
        "action": "操作B2"
      }
    ]
  }
}
```

#### 1.3 使用示例

**场景**：根据文件类型执行不同的处理流程

```
// 图式结构
文件类型检查 -[判断首]-> 文本文件处理  // 文本文件处理流程
文件类型检查 -[判断]-> 二进制文件处理  // 二进制文件处理流程

文本文件处理 -[时序首]-> 读取文本
文本文件处理 -[时序]-> 解析文本
读取文本 -[顺承]-> 解析文本

二进制文件处理 -[时序首]-> 读取二进制
二进制文件处理 -[时序]-> 解析二进制
读取二进制 -[顺承]-> 解析二进制
```

### 2. 循环嵌套条件模板

循环结构中嵌套条件结构。

#### 2.1 模板结构

```
// 图式结构
循环主题 -[时序]-> 循环体
循环体 -[时序首]-> 条件主题
条件主题 -[判断首]-> 条件为真分支
条件主题 -[判断]-> 条件为假分支
循环体 -[循环条件]-> 循环主题
```

#### 2.2 JSON表示

```json
{
  "structure_type": "loop_with_conditional",
  "theme": "循环条件操作",
  "loop_body": {
    "structure_type": "conditional",
    "theme": "条件判断",
    "condition": {
      "type": "comparison",
      "operator": "equals",
      "left_operand": "$element_type",
      "right_operand": "special"
    },
    "then_branch": {
      "id": "特殊处理",
      "action": "特殊处理操作"
    },
    "else_branch": {
      "id": "普通处理",
      "action": "普通处理操作"
    }
  },
  "condition": {
    "type": "comparison",
    "operator": "less_than",
    "left_operand": "$index",
    "right_operand": "$total"
  }
}
```

#### 2.3 使用示例

**场景**：处理列表中的元素，对特殊元素进行特殊处理

```
// 图式结构
列表处理 -[时序]-> 元素处理
元素处理 -[时序首]-> 元素类型检查
元素类型检查 -[判断首]-> 特殊元素处理
元素类型检查 -[判断]-> 普通元素处理
元素处理 -[循环条件]-> 列表处理
```

### 3. 多层嵌套结构模板

多层嵌套的复杂结构。

#### 3.1 模板结构

```
// 图式结构
顺序主题 -[时序首]-> 步骤1
顺序主题 -[时序]-> 条件主题
顺序主题 -[时序]-> 步骤3

步骤1 -[顺承]-> 条件主题
条件主题 -[判断首]-> 循环主题
条件主题 -[判断]-> 步骤2
步骤2 -[顺承]-> 步骤3

循环主题 -[时序]-> 循环体
循环体 -[循环条件]-> 循环主题
```

#### 3.2 JSON表示

```json
{
  "structure_type": "complex_nested",
  "theme": "多层嵌套操作",
  "sequence": {
    "steps": [
      {
        "id": "步骤1",
        "action": "初始化操作"
      },
      {
        "structure_type": "conditional",
        "theme": "条件判断",
        "condition": {
          "type": "comparison",
          "operator": "equals",
          "left_operand": "$condition",
          "right_operand": true
        },
        "then_branch": {
          "structure_type": "do_while_loop",
          "theme": "循环操作",
          "loop_body": {
            "id": "循环体",
            "action": "循环处理"
          },
          "condition": {
            "type": "comparison",
            "operator": "less_than",
            "left_operand": "$counter",
            "right_operand": "$limit"
          }
        },
        "else_branch": {
          "id": "步骤2",
          "action": "替代操作"
        }
      },
      {
        "id": "步骤3",
        "action": "完成操作"
      }
    ]
  }
}
```

#### 3.3 使用示例

**场景**：文件批处理操作

```
// 图式结构
文件批处理 -[时序首]-> 初始化环境
文件批处理 -[时序]-> 文件存在检查
文件批处理 -[时序]-> 清理环境

初始化环境 -[顺承]-> 文件存在检查
文件存在检查 -[判断首]-> 文件列表处理
文件存在检查 -[判断]-> 创建空结果
创建空结果 -[顺承]-> 清理环境

文件列表处理 -[时序]-> 处理单个文件
处理单个文件 -[循环条件]-> 文件列表处理
```

## 三、特定场景模板

### 1. 数学计算模板

用于数学计算的图式结构模板。

#### 1.1 简单计算模板

```
// 图式结构
计算主题 -[时序首]-> 获取操作数
计算主题 -[时序]-> 执行计算
计算主题 -[时序]-> 返回结果

获取操作数 -[顺承]-> 执行计算
执行计算 -[顺承]-> 返回结果

// 变量绑定
计算主题 -[变量]-> 操作数1
计算主题 -[变量]-> 操作数2
计算主题 -[变量]-> 操作符
```

#### 1.2 多位数加法模板

```
// 图式结构
加法计算 -[时序首]-> 初始化结果
加法计算 -[时序]-> 处理位循环
加法计算 -[时序]-> 返回结果

初始化结果 -[顺承]-> 处理位循环
处理位循环 -[时序]-> 获取当前位
处理位循环 -[时序]-> 计算当前位和进位
处理位循环 -[时序]-> 更新结果
处理位循环 -[时序]-> 移动到下一位

获取当前位 -[顺承]-> 计算当前位和进位
计算当前位和进位 -[顺承]-> 更新结果
更新结果 -[顺承]-> 移动到下一位
移动到下一位 -[循环条件]-> 处理位循环

处理位循环 -[顺承]-> 返回结果
```

### 2. 文本处理模板

用于文本处理的图式结构模板。

#### 2.1 文本分析模板

```
// 图式结构
文本分析 -[时序首]-> 读取文本
文本分析 -[时序]-> 分词处理
文本分析 -[时序]-> 词频统计
文本分析 -[时序]-> 生成报告

读取文本 -[顺承]-> 分词处理
分词处理 -[顺承]-> 词频统计
词频统计 -[顺承]-> 生成报告

// 分词处理子结构
分词处理 -[时序]-> 初始化词表
分词处理 -[时序]-> 分词循环

初始化词表 -[顺承]-> 分词循环
分词循环 -[时序]-> 提取当前词
分词循环 -[时序]-> 添加到词表
提取当前词 -[顺承]-> 添加到词表
添加到词表 -[循环条件]-> 分词循环
```

#### 2.2 文本替换模板

```
// 图式结构
文本替换 -[时序首]-> 读取文本
文本替换 -[时序]-> 查找模式
文本替换 -[时序]-> 执行替换
文本替换 -[时序]-> 保存结果

读取文本 -[顺承]-> 查找模式
查找模式 -[判断首]-> 执行替换  // 找到匹配时执行替换
查找模式 -[判断]-> 保存结果  // 未找到匹配时直接保存
执行替换 -[顺承]-> 保存结果
```

### 3. 数据处理模板

用于数据处理的图式结构模板。

#### 3.1 数据过滤模板

```
// 图式结构
数据过滤 -[时序首]-> 读取数据
数据过滤 -[时序]-> 过滤循环
数据过滤 -[时序]-> 输出结果

读取数据 -[顺承]-> 过滤循环
过滤循环 -[时序]-> 获取当前项
过滤循环 -[时序]-> 条件检查
条件检查 -[判断首]-> 添加到结果
条件检查 -[判断]-> 跳过当前项
添加到结果 -[顺承]-> 移动到下一项
跳过当前项 -[顺承]-> 移动到下一项
移动到下一项 -[循环条件]-> 过滤循环

过滤循环 -[顺承]-> 输出结果
```

#### 3.2 数据排序模板

```
// 图式结构
数据排序 -[时序首]-> 读取数据
数据排序 -[时序]-> 外层循环
数据排序 -[时序]-> 输出结果

读取数据 -[顺承]-> 外层循环
外层循环 -[时序]-> 初始化内层索引
外层循环 -[时序]-> 内层循环
初始化内层索引 -[顺承]-> 内层循环
内层循环 -[时序]-> 比较元素
内层循环 -[时序]-> 交换元素
比较元素 -[判断首]-> 交换元素  // 需要交换时执行
比较元素 -[判断]-> 移动内层索引  // 不需要交换时跳过
交换元素 -[顺承]-> 移动内层索引
移动内层索引 -[循环条件]-> 内层循环
内层循环 -[顺承]-> 移动外层索引
移动外层索引 -[循环条件]-> 外层循环

外层循环 -[顺承]-> 输出结果
```

## 四、模板使用指南

### 1. 模板选择

根据需要实现的功能选择合适的模板：

1. **基本操作**：选择顺序结构模板
2. **条件判断**：选择条件结构模板
3. **重复操作**：选择循环结构模板
4. **复杂逻辑**：选择复合结构模板
5. **特定场景**：选择特定场景模板

### 2. 模板参数化

根据具体需求对模板进行参数化：

1. **替换节点名称**：使用具体的操作名称替换模板中的通用名称
2. **设置变量值**：为变量节点设置具体的值
3. **定义条件表达式**：为条件节点设置具体的条件表达式
4. **调整结构**：根据需要调整模板的结构，如增加或减少步骤

### 3. 模板组合

通过组合多个模板构建复杂的图式结构：

1. **顺序组合**：将多个模板按顺序连接
2. **嵌套组合**：将一个模板嵌套在另一个模板的某个节点中
3. **并行组合**：将多个模板并行放置，通过条件或其他方式选择执行路径

### 4. 模板验证

验证生成的图式结构的正确性：

1. **结构完整性**：确保所有节点都有正确的连接
2. **执行路径**：验证所有可能的执行路径
3. **变量绑定**：检查变量绑定的正确性
4. **终止条件**：确保循环结构有正确的终止条件
