 *, *:hover, *:focus{
    outline: none !important;
 } 
 
 
 .node-detailer-panel {
     padding: 10px;
     max-width: 400px;
     font-size: 14px;
     left: -1000px;
     position: fixed;
     z-index: 0;
 }

 .node-detailer-panel * {
     background-color: rgba(0, 0, 0, 0);
 }

 .node-detailer-panel {
     background-color: rgba(0, 0, 0, 0);
 }

 .node-detailer-panel.debug {
     left: 10px;
     bottom: 10px;
     z-index: 999;
 }

 .node-detailer-panel .item>td {
     border-style: solid;
     border-width: 2px;
     padding: 8px;
     max-height: 30px;
     overflow-y: hidden;
 }

 .node-detailer-panel .item.item-head {
     font-size: 16px;
 }

 .node-detailer-panel .item .item-key {
     width: 60px;
     text-align: right;
     vertical-align: middle;
 }

 .node-detailer-panel .item .item-value {
     padding: 4px;
     text-align: left;
     overflow-wrap: break-word;
     word-wrap: break-word;
     hyphens: auto;
 }

/*detail-table*/
.detail-table{
    width: 100%;
    max-height: 500px;
    overflow:auto;
    margin-bottom:10px;
}

.detail-table > table.table{
    width: max-content;
    max-width: max-content;
    min-width: 100%;
    margin-bottom:0;
    background-color: transparent;
    color: #DDDDDD;
}

.detail-table > table.table > tbody > tr:first-child td {
    color: #DBDBDB;
}
.detail-table > table.table > tbody > tr > td{
    max-width: 360px !important;
    word-wrap: break-word !important;
}

.detail-table > table.table > tbody > tr > td span{
    line-height: 22px;
    max-height: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.detail-table table thead,  .detail-table tbody tr {
    display: table-row;
}