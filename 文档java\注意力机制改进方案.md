# 注意力机制改进方案

## 一、当前注意力机制分析

当前系统中的注意力机制主要基于LIDA框架的实现，但存在一些局限性，无法达到人类级别的注意力控制。通过对现有代码的分析，发现以下问题：

1. **缺乏动机驱动的主动注意力**：
   - 现有系统主要依赖于自下而上的激活扩散，缺乏自上而下的目标导向注意力控制
   - 无法实现有意识地将注意力集中在特定信息上的能力
   - 注意力小码（AttentionCodelet）主要是被动地检测激活值超过阈值的内容

2. **多感知通道整合不足**：
   - 虽然系统中有SensoryChannel的概念，但缺乏对不同感知模态的优先级调控
   - 无法像人类大脑那样动态调整对不同感知通道的关注度
   - 缺少类似脑干对感知模态的调控机制

3. **注意力资源分配机制简单**：
   - 缺乏注意力资源的精细化分配策略
   - 无法根据任务重要性和紧急程度动态调整注意力资源
   - 当前的Coalition（联盟）形成机制过于简单，主要基于激活值

4. **注意力转移机制不完善**：
   - 缺乏自然的注意力转移机制
   - 无法模拟人类在多任务环境中的注意力切换能力
   - 缺少抑制性控制机制

## 二、改进目标与设计原则

基于上述分析，我们提出以下注意力机制的改进目标和设计原则：

### 1. 改进目标

1. **实现动机性注意力**：
   - 支持有意识地将注意力集中在特定信息上
   - 实现目标导向的自上而下注意力控制
   - 支持目的性搜索和注意力引导

2. **多感知通道整合**：
   - 实现对不同感知模态的动态优先级调控
   - 模拟脑干对感知模态的调控机制
   - 支持在模态内部关注特定区域（如视觉中的特定区域）

3. **注意力资源精细化分配**：
   - 实现有限注意力资源的精细化分配
   - 支持基于任务重要性和紧急程度的动态调整
   - 实现注意力资源的竞争机制

4. **自然的注意力转移**：
   - 实现自然的注意力转移机制
   - 支持多任务环境中的注意力切换
   - 实现抑制性控制机制

### 2. 设计原则

1. **多层次注意力架构**：
   - 将注意力机制分为感知层、工作记忆层和动机层
   - 每一层都有独立的注意力控制机制
   - 各层之间通过清晰的接口进行交互

2. **自上而下与自下而上的结合**：
   - 结合自上而下（目标导向）和自下而上（刺激驱动）的注意力控制
   - 支持两种机制的动态平衡
   - 允许在不同情况下调整两种机制的权重

3. **有限资源管理**：
   - 将注意力视为有限资源
   - 实现资源的显式分配和管理
   - 支持资源的动态重新分配

4. **生物启发的设计**：
   - 参考人类和动物大脑中的注意力机制
   - 模拟脑干对感知模态的调控
   - 借鉴前额叶皮层的执行控制机制

## 三、多层次注意力框架设计

基于上述设计原则，我们提出一个多层次的注意力框架，包括以下几个关键组件：

```java
public class MultiLevelAttentionSystem {
    // 感知层注意力控制
    private final PerceptualAttentionController perceptualController;

    // 工作记忆注意力控制
    private final WorkingMemoryAttentionController workingMemoryController;

    // 动机驱动的注意力控制
    private final MotivationalAttentionController motivationalController;

    // 全局注意力资源管理
    private final AttentionResourceManager resourceManager;

    // 注意力状态监控
    private final AttentionStateMonitor stateMonitor;

    public MultiLevelAttentionSystem() {
        this.resourceManager = new AttentionResourceManager();
        this.stateMonitor = new AttentionStateMonitor();
        this.perceptualController = new PerceptualAttentionController(resourceManager);
        this.workingMemoryController = new WorkingMemoryAttentionController(resourceManager);
        this.motivationalController = new MotivationalAttentionController(resourceManager);

        // 建立各控制器之间的协调机制
        establishCoordinationMechanisms();
    }

    // 处理新的感知输入
    public void processPerceptualInput(Map<SensoryModality, PerceptualInput> inputs) {
        // 1. 根据当前注意力状态和动机分配感知资源
        Map<SensoryModality, Double> modalityWeights =
            motivationalController.calculateModalityWeights();

        // 2. 对每个感知模态应用注意力过滤
        Map<SensoryModality, FilteredPerceptualInput> filteredInputs =
            perceptualController.applyAttentionalFiltering(inputs, modalityWeights);

        // 3. 将过滤后的感知输入传递给工作记忆
        workingMemoryController.updateWithFilteredInputs(filteredInputs);

        // 4. 更新注意力状态
        stateMonitor.updateAttentionalState(filteredInputs);
    }

    // 设置动机驱动的注意力目标
    public void setAttentionalGoal(AttentionalGoal goal) {
        motivationalController.setCurrentGoal(goal);

        // 根据新目标调整各层次的注意力分配
        perceptualController.adjustForGoal(goal);
        workingMemoryController.adjustForGoal(goal);

        // 记录注意力目标变化
        stateMonitor.recordGoalChange(goal);
    }

    // 获取当前注意力焦点
    public AttentionalFocus getCurrentFocus() {
        return stateMonitor.getCurrentFocus();
    }
}
```

### 1. 感知层注意力控制

感知层注意力控制负责管理不同感知模态的输入，类似于人类脑干对感知通道的控制：

```java
public class PerceptualAttentionController {
    // 支持的感知模态
    private final Map<SensoryModality, ModalityProcessor> modalityProcessors;

    // 注意力资源管理器
    private final AttentionResourceManager resourceManager;

    // 当前模态优先级
    private Map<SensoryModality, Double> modalityPriorities;

    // 空间注意力焦点（用于视觉等空间模态）
    private SpatialAttentionFocus spatialFocus;

    public PerceptualAttentionController(AttentionResourceManager resourceManager) {
        this.resourceManager = resourceManager;
        this.modalityProcessors = initializeModalityProcessors();
        this.modalityPriorities = initializeDefaultPriorities();
        this.spatialFocus = new SpatialAttentionFocus();
    }

    // 应用注意力过滤
    public Map<SensoryModality, FilteredPerceptualInput> applyAttentionalFiltering(
            Map<SensoryModality, PerceptualInput> inputs,
            Map<SensoryModality, Double> modalityWeights) {

        Map<SensoryModality, FilteredPerceptualInput> filteredInputs = new HashMap<>();

        // 分配注意力资源给各个模态
        Map<SensoryModality, Double> allocatedResources =
            resourceManager.allocateResourcesForModalities(modalityWeights);

        // 对每个模态应用注意力过滤
        for (Map.Entry<SensoryModality, PerceptualInput> entry : inputs.entrySet()) {
            SensoryModality modality = entry.getKey();
            PerceptualInput input = entry.getValue();
            double resourceAmount = allocatedResources.getOrDefault(modality, 0.0);

            // 获取对应的模态处理器
            ModalityProcessor processor = modalityProcessors.get(modality);
            if (processor != null) {
                // 应用模态特定的注意力过滤
                FilteredPerceptualInput filteredInput =
                    processor.applyAttentionalFilter(input, resourceAmount, spatialFocus);
                filteredInputs.put(modality, filteredInput);
            }
        }

        return filteredInputs;
    }

    // 调整空间注意力焦点（例如视觉注意力的位置）
    public void adjustSpatialFocus(SpatialCoordinates coordinates, double scopeSize) {
        spatialFocus.setCoordinates(coordinates);
        spatialFocus.setScopeSize(scopeSize);
    }

    // 根据目标调整注意力
    public void adjustForGoal(AttentionalGoal goal) {
        // 根据目标调整模态优先级
        modalityPriorities = calculateModalityPrioritiesForGoal(goal);

        // 如果目标指定了空间位置，调整空间注意力
        if (goal.hasSpatialComponent()) {
            adjustSpatialFocus(goal.getSpatialCoordinates(), goal.getSpatialScopeSize());
        }
    }
}
```

### 2. 动机驱动的注意力控制

动机驱动的注意力控制是实现主动注意力的关键组件：

```java
public class MotivationalAttentionController {
    // 注意力资源管理器
    private final AttentionResourceManager resourceManager;

    // 当前注意力目标
    private AttentionalGoal currentGoal;

    // 目标优先级队列
    private PriorityQueue<AttentionalGoal> goalQueue;

    // 动机系统接口
    private final MotivationalSystem motivationalSystem;

    public MotivationalAttentionController(AttentionResourceManager resourceManager) {
        this.resourceManager = resourceManager;
        this.goalQueue = new PriorityQueue<>(
            Comparator.comparingDouble(AttentionalGoal::getPriority).reversed());
        this.motivationalSystem = new MotivationalSystem();
    }

    // 设置当前注意力目标
    public void setCurrentGoal(AttentionalGoal goal) {
        this.currentGoal = goal;

        // 分配资源给新目标
        resourceManager.allocateResourcesForGoal(goal);
    }

    // 添加注意力目标到队列
    public void addGoalToQueue(AttentionalGoal goal) {
        goalQueue.add(goal);

        // 如果新目标优先级高于当前目标，切换注意力
        if (currentGoal == null || goal.getPriority() > currentGoal.getPriority()) {
            setCurrentGoal(goal);
        }
    }

    // 计算各感知模态的权重
    public Map<SensoryModality, Double> calculateModalityWeights() {
        Map<SensoryModality, Double> weights = new HashMap<>();

        // 如果有当前目标，根据目标调整模态权重
        if (currentGoal != null) {
            weights = currentGoal.getModalityPreferences();
        } else {
            // 默认权重分配
            for (SensoryModality modality : SensoryModality.values()) {
                weights.put(modality, 1.0 / SensoryModality.values().length);
            }
        }

        // 根据当前动机状态调整权重
        Map<MotivationType, Double> motivationalState =
            motivationalSystem.getCurrentMotivationalState();

        // 应用动机对模态权重的影响
        for (Map.Entry<MotivationType, Double> entry : motivationalState.entrySet()) {
            MotivationType motivationType = entry.getKey();
            double motivationStrength = entry.getValue();

            // 获取此动机类型偏好的模态
            SensoryModality preferredModality =
                motivationalSystem.getPreferredModalityForMotivation(motivationType);

            // 增强偏好模态的权重
            if (preferredModality != null) {
                double currentWeight = weights.getOrDefault(preferredModality, 0.0);
                weights.put(preferredModality,
                           currentWeight + (motivationStrength * 0.2));
            }
        }

        // 归一化权重
        normalizeWeights(weights);

        return weights;
    }

    // 触发目的性搜索
    public SearchQuery generatePurposefulSearchQuery() {
        if (currentGoal == null) {
            return null;
        }

        // 根据当前目标生成搜索查询
        return new SearchQuery()
            .setTargetConcept(currentGoal.getTargetConcept())
            .setSearchDepth(calculateSearchDepthFromGoal(currentGoal))
            .setPriority(currentGoal.getPriority())
            .setSearchStrategy(determineSearchStrategyFromGoal(currentGoal));
    }
}
```

### 3. 注意力资源管理

注意力资源管理器负责分配有限的注意力资源：

```java
public class AttentionResourceManager {
    // 总注意力资源量
    private static final double TOTAL_ATTENTION_RESOURCES = 100.0;

    // 当前分配给各组件的资源
    private final Map<AttentionComponent, Double> componentResources;

    // 当前分配给各模态的资源
    private final Map<SensoryModality, Double> modalityResources;

    public AttentionResourceManager() {
        this.componentResources = new HashMap<>();
        this.modalityResources = new HashMap<>();

        // 初始化默认资源分配
        initializeDefaultResourceAllocation();
    }

    // 为各感知模态分配资源
    public Map<SensoryModality, Double> allocateResourcesForModalities(
            Map<SensoryModality, Double> modalityWeights) {

        // 获取分配给感知层的总资源
        double perceptualLayerResources =
            componentResources.getOrDefault(AttentionComponent.PERCEPTUAL_LAYER, 30.0);

        // 根据权重分配资源
        Map<SensoryModality, Double> allocatedResources = new HashMap<>();
        double totalWeight = modalityWeights.values().stream().mapToDouble(Double::doubleValue).sum();

        for (Map.Entry<SensoryModality, Double> entry : modalityWeights.entrySet()) {
            SensoryModality modality = entry.getKey();
            double weight = entry.getValue();

            // 计算分配给此模态的资源
            double resourceAmount = (weight / totalWeight) * perceptualLayerResources;
            allocatedResources.put(modality, resourceAmount);

            // 更新模态资源记录
            modalityResources.put(modality, resourceAmount);
        }

        return allocatedResources;
    }

    // 为注意力目标分配资源
    public void allocateResourcesForGoal(AttentionalGoal goal) {
        // 根据目标优先级分配资源
        double goalResources = Math.min(50.0, goal.getPriority() * 100.0);

        // 调整各组件的资源分配
        double remainingResources = TOTAL_ATTENTION_RESOURCES - goalResources;

        // 分配资源给工作记忆
        componentResources.put(AttentionComponent.WORKING_MEMORY,
                              remainingResources * 0.4);

        // 分配资源给感知层
        componentResources.put(AttentionComponent.PERCEPTUAL_LAYER,
                              remainingResources * 0.3);

        // 分配资源给长期记忆访问
        componentResources.put(AttentionComponent.LONG_TERM_MEMORY,
                              remainingResources * 0.2);

        // 保留一些资源用于意外情况
        componentResources.put(AttentionComponent.RESERVE,
                              remainingResources * 0.1);
    }

    // 动态调整资源分配以响应紧急情况
    public void respondToUrgentStimulus(SensoryModality modality, double urgencyLevel) {
        // 临时增加给定模态的资源
        double currentResource = modalityResources.getOrDefault(modality, 0.0);
        double additionalResource = Math.min(20.0, urgencyLevel * 30.0);

        // 从其他模态借用资源
        borrowResourcesFromOtherModalities(modality, additionalResource);

        // 更新模态资源
        modalityResources.put(modality, currentResource + additionalResource);

        // 设置资源恢复计时器
        scheduleResourceRestoration(3000); // 3秒后恢复正常资源分配
    }
}
```

### 4. 工作记忆注意力控制

工作记忆注意力控制管理工作记忆中的信息焦点：

```java
public class WorkingMemoryAttentionController {
    // 注意力资源管理器
    private final AttentionResourceManager resourceManager;

    // 工作记忆内容
    private final List<WorkingMemoryItem> workingMemoryItems;

    // 当前焦点项目
    private WorkingMemoryItem currentFocus;

    // 注意力窗口大小（可同时关注的项目数）
    private int attentionWindowSize = 4; // 默认值，类似于人类工作记忆容量

    public WorkingMemoryAttentionController(AttentionResourceManager resourceManager) {
        this.resourceManager = resourceManager;
        this.workingMemoryItems = new ArrayList<>();
    }

    // 使用过滤后的感知输入更新工作记忆
    public void updateWithFilteredInputs(Map<SensoryModality, FilteredPerceptualInput> filteredInputs) {
        // 将新的感知输入转换为工作记忆项目
        List<WorkingMemoryItem> newItems = convertInputsToWorkingMemoryItems(filteredInputs);

        // 计算新项目的显著性
        calculateSaliencyForItems(newItems);

        // 将新项目与现有项目合并
        mergeWithExistingItems(newItems);

        // 应用注意力窗口限制
        applyAttentionWindowConstraint();

        // 更新当前焦点
        updateCurrentFocus();
    }

    // 根据目标调整工作记忆注意力
    public void adjustForGoal(AttentionalGoal goal) {
        // 调整注意力窗口大小
        if (goal.requiresFocusedAttention()) {
            // 缩小注意力窗口，集中关注少数项目
            attentionWindowSize = 2;
        } else if (goal.requiresBroadAttention()) {
            // 扩大注意力窗口，关注更多项目
            attentionWindowSize = 6;
        } else {
            // 恢复默认窗口大小
            attentionWindowSize = 4;
        }

        // 根据目标调整项目优先级
        for (WorkingMemoryItem item : workingMemoryItems) {
            // 计算项目与目标的相关性
            double relevance = calculateRelevanceToGoal(item, goal);

            // 调整项目优先级
            item.adjustPriority(relevance);
        }

        // 重新应用注意力窗口约束
        applyAttentionWindowConstraint();

        // 更新当前焦点
        updateCurrentFocus();
    }

    // 应用注意力窗口约束
    private void applyAttentionWindowConstraint() {
        // 按优先级排序
        workingMemoryItems.sort(Comparator.comparingDouble(
            WorkingMemoryItem::getPriority).reversed());

        // 如果项目数超过窗口大小，降低超出部分的激活度
        for (int i = 0; i < workingMemoryItems.size(); i++) {
            WorkingMemoryItem item = workingMemoryItems.get(i);
            if (i < attentionWindowSize) {
                // 窗口内项目保持高激活度
                item.setActivationLevel(Math.max(0.7, item.getActivationLevel()));
            } else {
                // 窗口外项目激活度降低
                item.setActivationLevel(item.getActivationLevel() * 0.5);

                // 如果激活度太低，移除项目
                if (item.getActivationLevel() < 0.1) {
                    workingMemoryItems.remove(i);
                    i--;
                }
            }
        }
    }
}
```

### 5. 注意力状态监控

注意力状态监控器跟踪系统的注意力状态，提供反馈和分析：

```java
public class AttentionStateMonitor {
    // 当前注意力焦点
    private AttentionalFocus currentFocus;

    // 注意力历史记录
    private final List<AttentionEvent> attentionHistory;

    // 注意力指标
    private double focusStability = 1.0;
    private double distractibility = 0.0;
    private double attentionalEfficiency = 1.0;

    public AttentionStateMonitor() {
        this.attentionHistory = new ArrayList<>();
    }

    // 更新注意力状态
    public void updateAttentionalState(Map<SensoryModality, FilteredPerceptualInput> filteredInputs) {
        // 确定当前最显著的输入
        FilteredPerceptualInput mostSalientInput = findMostSalientInput(filteredInputs);

        // 创建新的注意力焦点
        AttentionalFocus newFocus = createFocusFromInput(mostSalientInput);

        // 检查是否发生注意力转移
        boolean attentionShifted = hasAttentionShifted(currentFocus, newFocus);

        // 更新注意力焦点
        AttentionalFocus previousFocus = currentFocus;
        currentFocus = newFocus;

        // 记录注意力事件
        recordAttentionEvent(previousFocus, currentFocus, attentionShifted);

        // 更新注意力指标
        updateAttentionMetrics(attentionShifted);
    }

    // 记录目标变化
    public void recordGoalChange(AttentionalGoal goal) {
        // 创建目标变化事件
        AttentionEvent event = new AttentionEvent(
            AttentionEventType.GOAL_CHANGE,
            System.currentTimeMillis(),
            currentFocus,
            null,
            goal
        );

        // 添加到历史记录
        attentionHistory.add(event);
    }

    // 获取当前注意力焦点
    public AttentionalFocus getCurrentFocus() {
        return currentFocus;
    }

    // 获取注意力指标报告
    public AttentionMetricsReport getAttentionMetrics() {
        return new AttentionMetricsReport(
            focusStability,
            distractibility,
            attentionalEfficiency,
            calculateAverageFocusDuration(),
            calculateAttentionShiftFrequency()
        );
    }
}
```

## 四、实现路径与集成方案

为了将上述设计集成到现有系统中，我们提出以下实现路径和集成方案：

### 1. 分阶段实现计划

建议采用分阶段的实现计划，每个阶段都可以独立测试和集成：

1. **第一阶段：注意力资源管理**
   - 实现AttentionResourceManager类
   - 定义注意力组件和感知模态的枚举类
   - 实现资源分配和管理算法
   - 测试资源分配的正确性

2. **第二阶段：感知层注意力控制**
   - 实现PerceptualAttentionController类
   - 为各感知模态实现ModalityProcessor
   - 实现空间注意力控制
   - 集成与现有的SensoryChannel类

3. **第三阶段：动机驱动的注意力控制**
   - 实现MotivationalAttentionController类
   - 定义AttentionalGoal类
   - 实现目标优先级队列
   - 集成与现有的动机系统

4. **第四阶段：工作记忆注意力控制**
   - 实现WorkingMemoryAttentionController类
   - 定义WorkingMemoryItem类
   - 实现注意力窗口约束
   - 集成与现有的工作记忆系统

5. **第五阶段：注意力状态监控**
   - 实现AttentionStateMonitor类
   - 定义AttentionEvent和AttentionalFocus类
   - 实现注意力指标计算
   - 实现注意力历史记录和分析

6. **第六阶段：多层次注意力系统集成**
   - 实现MultiLevelAttentionSystem类
   - 集成所有组件
   - 实现组件间的协调机制
   - 全面测试系统功能

### 2. 与现有系统的集成方案

为了将新的注意力机制与现有系统集成，需要考虑以下几个关键点：

1. **与LIDA框架的集成**
   - 替换现有的AttentionCodeletModule与AttentionCodeletImpl
   - 保持对现有AttentionCodelet接口的兼容
   - 将新的注意力控制器与GlobalWorkspace集成
   - 修改Coalition形成机制，使用新的注意力资源分配

2. **与感知通道的集成**
   - 为现有的SensoryChannel类添加注意力过滤功能
   - 实现模态特定的注意力处理器
   - 为视觉、听觉等模态实现空间注意力控制
   - 修改VisionChannel等类以支持空间注意力焦点

3. **与动机系统的集成**
   - 将新的动机驱动注意力控制器与现有的MotivationPAMemory集成
   - 实现动机状态到注意力目标的转换
   - 修改现有的激励显著性机制，使其影响注意力分配
   - 实现目的性搜索与现有搜索机制的集成

4. **与工作记忆的集成**
   - 修改现有的WorkspaceBuffer实现
   - 实现工作记忆项目与现有NodeStructure的转换
   - 修改当前情境模型（CSM）的更新机制
   - 实现注意力窗口约束与现有工作记忆容量限制的集成

### 3. 测试与评估方案

为了确保新的注意力机制满足需求并有效工作，需要设计全面的测试和评估方案：

1. **单元测试**
   - 为每个组件编写单元测试
   - 测试资源分配算法的正确性
   - 测试注意力过滤的效果
   - 测试动机驱动的注意力控制

2. **集成测试**
   - 测试各组件之间的协作
   - 测试与现有系统的集成
   - 测试在不同场景下的注意力分配
   - 测试注意力转移机制

3. **性能测试**
   - 测试注意力机制的计算效率
   - 测试在高负载情况下的表现
   - 测试内存使用情况
   - 测试并发处理能力

4. **功能测试**
   - 测试动机性注意力的有效性
   - 测试多感知通道整合
   - 测试注意力资源分配的合理性
   - 测试注意力转移的自然性

### 4. 演进路线图

下面是注意力机制改进的演进路线图，展示了从当前状态到目标状态的迁移路径：

```
当前状态                                 目标状态
+------------------------+                      +------------------------+
| 简单的注意力小码      |                      | 多层次注意力系统    |
| 基于激活值的联盟形成  | ===第一阶段===> | 注意力资源管理      |
| 简单的感知通道      |                      | 感知层注意力控制    |
+------------------------+                      +------------------------+
           |                                              ^
           |                                              |
           v                                              |
+------------------------+                      +------------------------+
| 注意力资源管理      |                      | 动机驱动的注意力控制  |
| 感知层注意力控制    | ===第二阶段===> | 工作记忆注意力控制    |
| 基本的感知通道集成  |                      | 注意力状态监控      |
+------------------------+                      +------------------------+
           |                                              ^
           |                                              |
           v                                              |
+------------------------+                      +------------------------+
| 动机驱动的注意力控制  |                      | 完全集成的多层次注意力 |
| 工作记忆注意力控制    | ===第三阶段===> | 与现有系统的无缝集成   |
| 注意力状态监控      |                      | 全面测试和优化      |
+------------------------+                      +------------------------+
```

## 五、预期效益与持续改进

实现这一改进方案将带来以下效益：

1. **增强的认知能力**
   - 支持人类级别的注意力控制
   - 提高系统处理复杂任务的能力
   - 增强对环境变化的适应性

2. **更高的资源利用效率**
   - 通过精细化的资源分配提高效率
   - 减少在不相关信息上的资源浪费
   - 支持在有限资源下处理更复杂的任务

3. **更自然的交互体验**
   - 支持更自然的注意力转移
   - 提高系统对用户意图的理解
   - 增强多模态交互能力

4. **更强的自主性**
   - 支持目标导向的主动注意力
   - 增强系统的自主决策能力
   - 提高系统的目标导向行为

同时，我们也认识到这只是注意力机制改进的第一步，未来还可以考虑以下持续改进方向：

1. **上下文敏感的注意力**
   - 基于当前任务和环境上下文动态调整注意力策略
   - 学习不同上下文下的最佳注意力分配

2. **元认知注意力**
   - 实现对自身注意力状态的监控和调节
   - 支持自我反思和自我调节

3. **社交注意力**
   - 支持对其他智能体的注意力状态的理解
   - 实现共同注意力和注意力协调

4. **情绪影响的注意力**
   - 考虑情绪状态对注意力分配的影响
   - 实现情绪调节的注意力控制