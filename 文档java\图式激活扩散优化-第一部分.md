# 图式激活扩散机制优化方案 - 第一部分：深度与广度控制

> 注：本文档是图式激活扩散机制优化方案的第一部分，主要关注深度与广度控制。相关内容请参考[第二部分：任务优先级管理](./图式激活扩散优化-第二部分.md)和[第三部分：任务队列与资源分配优化](./图式激活扩散优化-第三部分.md)。

## 一、当前实现分析

### 1. 现有深度控制机制

通过代码分析，当前系统的激活扩散深度控制主要依赖于简单的深度阈值：

```java
public void propagateActivationToParents(Node pn, int deep, String from) {
    // 设置传播深度
    deep++;
    int deepThreshold = 6;

    // 控制激活深度
    if (deep > deepThreshold) {
        return;
    }

    // 获取连接的节点并传播激活
    parentLinkSet = pamNodeStructure.getConnectedSinks(pn);
    for (Link parent : parentLinkSet) {
        // 激活传播逻辑...
    }
}
```

这种简单的深度控制存在以下问题：

1. 固定阈值：对所有类型的节点和连接使用相同的深度阈值(6)
2. 无差异传播：不考虑节点和连接的重要性或相关性
3. 缺乏方向性：激活向所有方向均匀扩散，无法聚焦于相关路径
4. 资源浪费：可能激活大量与当前任务无关的节点

### 2. 现有广度控制机制

当前系统的广度控制主要体现在以下代码中：

```java
// 获取连接的节点并传播激活
parentLinkSet = pamNodeStructure.getConnectedSinks(pn);
for (Link parent : parentLinkSet) {
    sink = (Node) parent.getSink();
    // 避免循环激活
    if (pn.getFromnodeid() == sink.getNodeId()) {continue;}

    // 计算传播量
    double amountToPropagate = propagationStrategy.getActivationToPropagate(propagateParams);

    // 传播激活
    propagateActivation(sink, (PamLink) parent, amountToPropagate, deep, "pam");
}
```

这种广度控制存在以下问题：

1. 全量传播：对所有连接的节点都进行激活传播
2. 无优先级：不考虑连接的权重或重要性
3. 并发问题：多线程环境下可能导致激活爆炸
4. 资源竞争：重要任务可能被大量低优先级激活淹没

## 二、优化目标

针对上述问题，我们的优化目标是：

1. 实现智能深度控制，根据节点类型、连接类型和上下文动态调整深度限制
2. 实现选择性广度控制，基于权重和相关性选择性地传播激活
3. 引入方向性激活，优先激活与当前任务相关的路径
4. 优化资源分配，确保重要任务获得足够的计算资源

## 三、深度控制优化

### 1. 动态深度阈值

根据节点类型、连接类型和激活来源动态计算最佳深度：

```java
private int calculateOptimalDepth(Node node, String linkType, String from) {
    // 基础深度
    int baseDepth = 4;

    // 根据节点类型调整
    if (isHighPriorityNode(node)) {
        baseDepth += 2;  // 高优先级节点允许更深的激活
    }

    // 根据连接类型调整
    switch (linkType) {
        case "相似":
        case "对等":
            baseDepth -= 1;  // 语义关系限制较浅的激活
            break;
        case "顺承":
        case "时序":
            baseDepth += 1;  // 时序关系允许更深的激活
            break;
        case "arg0":
        case "arg1":
            baseDepth += 1;  // 核心参数关系允许更深的激活
            break;
    }

    // 根据激活来源调整
    switch (from) {
        case "listen":
        case "see":
            baseDepth += 1;  // 感知输入允许更深的激活
            break;
        case "varwantplan":
            baseDepth += 2;  // 计划相关允许更深的激活
            break;
    }

    // 确保深度在合理范围内
    return Math.max(2, Math.min(baseDepth, 10));
}
```

### 2. 激活衰减机制

随着深度增加，激活强度应该更快衰减：

```java
private double calculateActivationDecay(double activation, int depth, String linkType) {
    // 基础衰减率
    double baseDecayRate = 0.7;

    // 根据连接类型调整衰减率
    switch (linkType) {
        case "相似":
        case "对等":
            baseDecayRate = 0.6;  // 语义关系衰减更快
            break;
        case "顺承":
        case "时序":
            baseDecayRate = 0.8;  // 时序关系衰减更慢
            break;
    }

    // 应用深度指数衰减
    return activation * Math.pow(baseDecayRate, depth);
}
```

### 3. 激活阈值控制

引入动态激活阈值，只有超过阈值的节点才会继续传播：

```java
private boolean shouldPropagate(Node node, double activation, int depth) {
    // 基础阈值
    double baseThreshold = 0.1;

    // 随深度增加阈值
    double depthFactor = 1.0 + (depth * 0.05);

    // 根据节点类型调整阈值
    if (isHighPriorityNode(node)) {
        baseThreshold *= 0.8;  // 高优先级节点降低阈值
    }

    // 计算最终阈值
    double threshold = baseThreshold * depthFactor;

    // 判断是否超过阈值
    return activation > threshold;
}
```

## 四、广度控制优化

### 1. 选择性激活传播

不是对所有连接都进行激活传播，而是基于权重和相关性选择性传播：

```java
private List<Link> selectLinksForPropagation(Set<Link> allLinks, Node sourceNode, String from) {
    // 为所有连接计算传播优先级
    List<ScoredLink> scoredLinks = new ArrayList<>();
    for (Link link : allLinks) {
        double score = calculateLinkScore(link, sourceNode, from);
        scoredLinks.add(new ScoredLink(link, score));
    }

    // 按分数排序
    Collections.sort(scoredLinks, (a, b) -> Double.compare(b.score, a.score));

    // 选择前N个或超过阈值的连接
    int maxLinks = calculateMaxLinks(sourceNode, from);
    double minScore = calculateMinScore(sourceNode, from);

    List<Link> selectedLinks = new ArrayList<>();
    for (int i = 0; i < Math.min(maxLinks, scoredLinks.size()); i++) {
        ScoredLink scoredLink = scoredLinks.get(i);
        if (scoredLink.score >= minScore) {
            selectedLinks.add(scoredLink.link);
        }
    }

    return selectedLinks;
}

private double calculateLinkScore(Link link, Node sourceNode, String from) {
    // 基础分数 = 连接权重
    double score = link.getWeight();

    // 根据连接类型调整
    String linkType = link.getCategory().getName();
    switch (linkType) {
        case "顺承":
        case "时序":
            score *= 1.5;  // 时序关系更重要
            break;
        case "arg0":
        case "arg1":
            score *= 1.3;  // 核心参数关系更重要
            break;
    }

    // 考虑目标节点的激活状态
    Node targetNode = (Node) link.getSink();
    score *= (1.0 + targetNode.getActivation());

    // 考虑与当前任务的相关性
    if (isRelatedToCurrentTask(targetNode)) {
        score *= 2.0;
    }

    return score;
}
```

### 2. 批量处理机制

将激活任务分批处理，避免一次性创建过多任务：

```java
private void propagateActivationInBatches(List<Link> selectedLinks, Node sourceNode,
                                         double activation, int depth, String from) {
    // 计算批次大小
    int batchSize = calculateBatchSize(selectedLinks.size());

    // 分批处理
    for (int i = 0; i < selectedLinks.size(); i += batchSize) {
        int endIndex = Math.min(i + batchSize, selectedLinks.size());
        List<Link> batch = selectedLinks.subList(i, endIndex);

        // 创建批处理任务
        PropagationBatchTask task = new PropagationBatchTask(batch, sourceNode,
                                                           activation, depth, from);
        taskSpawner.addTask(task);
    }
}

private class PropagationBatchTask extends FrameworkTaskImpl {
    private final List<Link> links;
    private final Node sourceNode;
    private final double activation;
    private final int depth;
    private final String from;

    public PropagationBatchTask(List<Link> links, Node sourceNode,
                               double activation, int depth, String from) {
        super(1);  // 立即执行
        this.links = links;
        this.sourceNode = sourceNode;
        this.activation = activation;
        this.depth = depth;
        this.from = from;
    }

    @Override
    protected void runThisFrameworkTask() {
        for (Link link : links) {
            Node targetNode = (Node) link.getSink();

            // 计算传播量
            double amountToPropagate = calculatePropagationAmount(sourceNode, link, activation);

            // 传播激活
            if (amountToPropagate > 0) {
                propagateActivation(targetNode, (PamLink) link, amountToPropagate, depth, from);
            }
        }
    }
}
```

### 3. 激活配额管理

引入激活配额机制，限制单个节点可以发起的激活传播数量：

```java
private class ActivationQuotaManager {
    private final Map<String, Integer> nodeTypeQuotas = new HashMap<>();
    private final Map<String, Integer> fromTypeQuotas = new HashMap<>();
    private final Map<Node, Integer> nodeUsage = new ConcurrentHashMap<>();

    public ActivationQuotaManager() {
        // 设置不同节点类型的配额
        nodeTypeQuotas.put("概念", 20);
        nodeTypeQuotas.put("场景", 30);
        nodeTypeQuotas.put("动作", 15);

        // 设置不同来源的配额
        fromTypeQuotas.put("listen", 40);
        fromTypeQuotas.put("see", 40);
        fromTypeQuotas.put("pam", 20);
        fromTypeQuotas.put("varwantplan", 50);
    }

    public int getRemainingQuota(Node node, String from) {
        // 获取节点类型配额
        String nodeType = getNodeType(node);
        int typeQuota = nodeTypeQuotas.getOrDefault(nodeType, 10);

        // 获取来源配额
        int fromQuota = fromTypeQuotas.getOrDefault(from, 15);

        // 取较小值作为基础配额
        int baseQuota = Math.min(typeQuota, fromQuota);

        // 获取已使用配额
        int used = nodeUsage.getOrDefault(node, 0);

        // 计算剩余配额
        return Math.max(0, baseQuota - used);
    }

    public void useQuota(Node node, int amount) {
        nodeUsage.compute(node, (k, v) -> (v == null ? 0 : v) + amount);
    }

    public void resetQuota(Node node) {
        nodeUsage.remove(node);
    }
}
```

## 五、实现建议

### 1. 改进的激活扩散方法

整合上述优化，实现改进的激活扩散方法：

```java
public void propagateActivationToParents(Node pn, int deep, String from) {
    // 获取节点名称
    String pname = pn.getTNname();

    // 计算最佳深度
    int optimalDepth = calculateOptimalDepth(pn, pn.getFromLinkType(), from);
    deep++;

    // 控制激活深度
    if (deep > optimalDepth) {
        return;
    }

    // 获取所有连接的节点
    Set<Link> parentLinkSet = pamNodeStructure.getConnectedSinks(pn);

    // 检查激活配额
    int remainingQuota = activationQuotaManager.getRemainingQuota(pn, from);
    if (remainingQuota <= 0) {
        return;  // 配额用尽，停止传播
    }

    // 选择性传播
    List<Link> selectedLinks = selectLinksForPropagation(parentLinkSet, pn, from);

    // 使用配额
    int quotaUsed = Math.min(remainingQuota, selectedLinks.size());
    activationQuotaManager.useQuota(pn, quotaUsed);

    // 如果配额不足，进一步筛选链接
    if (selectedLinks.size() > quotaUsed) {
        selectedLinks = selectedLinks.subList(0, quotaUsed);
    }

    // 批量处理激活传播
    propagateActivationInBatches(selectedLinks, pn, pn.getTotalActivation(), deep, from);
}
```

### 2. 激活任务优先级管理

为激活任务分配合理的优先级，确保重要任务优先执行：

```java
private int calculateTaskPriority(Node node, Link link, String from, int depth) {
    // 基础优先级
    int basePriority = 50;

    // 根据深度调整（深度越浅优先级越高）
    int depthFactor = Math.max(0, 10 - depth * 2);

    // 根据节点类型调整
    if (isHighPriorityNode(node)) {
        basePriority += 20;
    }

    // 根据连接类型调整
    String linkType = link != null ? link.getCategory().getName() : "";
    switch (linkType) {
        case "顺承":
        case "时序":
            basePriority += 15;  // 时序关系优先级更高
            break;
        case "arg0":
        case "arg1":
            basePriority += 10;  // 核心参数关系优先级更高
            break;
    }

    // 根据激活来源调整
    switch (from) {
        case "listen":
        case "see":
            basePriority += 10;  // 感知输入优先级更高
            break;
        case "varwantplan":
            basePriority += 20;  // 计划相关优先级更高
            break;
    }

    // 根据激活值调整
    basePriority += (int)(node.getActivation() * 10);

    return basePriority;
}
```

### 3. 激活监控与调整

实现激活监控机制，动态调整激活参数：

```java
private class ActivationMonitor {
    private int totalActivationTasks = 0;
    private int completedActivationTasks = 0;
    private long lastAdjustmentTime = System.currentTimeMillis();
    private final long adjustmentInterval = 1000;  // 1秒调整一次

    public synchronized void recordTaskCreation() {
        totalActivationTasks++;
    }

    public synchronized void recordTaskCompletion() {
        completedActivationTasks++;
    }

    public synchronized void adjustParameters() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastAdjustmentTime < adjustmentInterval) {
            return;  // 未到调整时间
        }

        // 计算任务完成率
        double completionRate = totalActivationTasks > 0 ?
                               (double)completedActivationTasks / totalActivationTasks : 1.0;

        // 根据完成率调整参数
        if (completionRate < 0.7) {
            // 任务堆积，减少激活传播
            decreaseActivationParameters();
        } else if (completionRate > 0.9 && totalActivationTasks < 1000) {
            // 任务处理良好，可以适当增加激活传播
            increaseActivationParameters();
        }

        // 重置计数器
        lastAdjustmentTime = currentTime;
        totalActivationTasks = 0;
        completedActivationTasks = 0;
    }

    private void decreaseActivationParameters() {
        // 减少激活参数，如降低配额、提高阈值等
    }

    private void increaseActivationParameters() {
        // 增加激活参数，如增加配额、降低阈值等
    }
}
```

## 六、总结

本优化方案针对图式激活扩散机制的深度和广度控制提出了一系列改进措施：

1. 动态深度阈值：根据节点类型、连接类型和激活来源动态调整深度限制
2. 激活衰减机制：随深度增加加速激活衰减，减少无关激活
3. 激活阈值控制：引入动态阈值，只有重要节点才能继续传播
4. 选择性激活传播：基于权重和相关性选择性地传播激活
5. 批量处理机制：分批处理激活任务，避免资源竞争
6. 激活配额管理：限制单个节点的激活传播数量
7. 任务优先级管理：为激活任务分配合理的优先级
8. 激活监控与调整：动态监控和调整激活参数

这些优化措施将显著提高图式激活扩散的效率和精确性，减少资源浪费，确保重要任务能够及时执行，从而提升系统的整体性能和响应能力。
