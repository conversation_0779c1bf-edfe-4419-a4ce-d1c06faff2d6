"""
XR Controller

This module provides controllers for XR API endpoints.
"""
from typing import Dict, Any, List
from flask import Blueprint, request, jsonify

from linars.com.warmer.kgmaker.utils.neo4j_util import Neo4jUtil

# Create blueprint
xr_blueprint = Blueprint('xr', __name__, url_prefix='/api/graph')

# Initialize services
neo4j_util = None

def init_controller(app_neo4j_util, app_kg_service, config):
    """
    Initialize the controller with required services
    
    Args:
        app_neo4j_util: Neo4j utility instance
        app_kg_service: Knowledge graph service instance
        config: Application configuration
    """
    global neo4j_util
    neo4j_util = app_neo4j_util

@xr_blueprint.route('/neo4j/project/getInfo', methods=['GET', 'POST'])
def get_info():
    """
    Get project information
    
    Returns:
        JSON response with status code
    """
    # Get request parameters
    params = request.args.to_dict() if request.method == 'GET' else request.form.to_dict()
    
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/project/getUISetting', methods=['GET', 'POST'])
def get_ui_setting():
    """
    Get UI settings for project
    
    Returns:
        JSON response with status code
    """
    # Get request parameters
    params = request.args.to_dict() if request.method == 'GET' else request.form.to_dict()
    project_id = request.args.get('projectId') or request.form.get('projectId')
    
    print(f"Project ID: {project_id}")
    
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/project/connect', methods=['GET', 'POST'])
def connect():
    """
    Connect to Neo4j project
    
    Returns:
        JSON response with status code
    """
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/project/getProjectSettings', methods=['GET', 'POST'])
def get_project_settings():
    """
    Get Neo4j project settings
    
    Returns:
        JSON response with status code
    """
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/neo4jBaseInfo', methods=['GET', 'POST'])
def neo4j_base_info():
    """
    Get Neo4j base information
    
    Returns:
        JSON response with status code
    """
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/neo4jBaseInfo/SampleQuery', methods=['GET', 'POST'])
def sample_query():
    """
    Execute sample query
    
    Returns:
        JSON response with status code
    """
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/excuteCommand', methods=['GET', 'POST'])
def excute_command():
    """
    Execute Cypher command
    
    Returns:
        JSON response with query results
    """
    command = request.args.get('command') or request.form.get('command')
    project_id = request.args.get('projectId') or request.form.get('projectId')
    query_limit = request.args.get('queryLimit') or request.form.get('queryLimit')
    options = request.args.get('options') or request.form.get('options')
    params = request.args.get('params') or request.form.get('params')
    
    print(f"参数 ---- {command} ---- {project_id} ---- {query_limit} ---- {options} ---- {params}")
    
    # Execute Cypher query
    graph_data0 = neo4j_util.excute_cypher_sql(command, "GetGraphNodeAndShip")[0]
    
    graph_data = {
        "data": graph_data0,
        "type": "GRAPH",
        "summary": "4.4.10"
    }
    
    result = {
        "content": graph_data,
        "status": 0,
        "message": "Query successful."
    }
    
    return jsonify(result)

@xr_blueprint.route('/neo4j/project/updateforceLayoutSettings', methods=['GET', 'POST'])
def update_force_layout_settings():
    """
    Update force layout settings
    
    Returns:
        JSON response with status code
    """
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/project/updateProjectSettings', methods=['GET', 'POST'])
def update_project_settings():
    """
    Update project settings
    
    Returns:
        JSON response with status code
    """
    result = {"code": 200}
    return jsonify(result)

@xr_blueprint.route('/neo4j/label/<string:e>/properties', methods=['GET', 'POST'])
def label_properties(e):
    """
    Get properties for a label
    
    Args:
        e: Label name
        
    Returns:
        JSON response with status code
    """
    # Get request parameters
    params = request.args.to_dict() if request.method == 'GET' else request.form.to_dict()
    
    print(f"---e--- {e}")
    
    result = {"code": 200}
    return jsonify(result)
