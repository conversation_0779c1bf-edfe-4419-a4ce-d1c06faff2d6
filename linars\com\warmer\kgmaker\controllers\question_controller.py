"""
Question Controller

This module provides controllers for question processing and classification.
"""
import os
import pickle
from typing import Dict, List, Any, Set
from flask import Blueprint, request, jsonify

# Create blueprint
question_blueprint = Blueprint('question', __name__, url_prefix='/kg')

# Constants
CORPUS_FOLDER = "data/sogou-text-classification-corpus-mini"
MODEL_PATH = "data/test/classification-model.ser"

class QuestionController:
    """
    Controller for handling questions and classification
    """
    
    def __init__(self):
        """
        Initialize the controller
        """
        self.abstract_map = {}
    
    """
    # The following methods are commented out as they require specific NLP libraries
    # They are kept as reference for future implementation
    
    @question_blueprint.route('/query', methods=['GET'])
    def query():
        question = request.args.get('question', '')
        result_map = {}
        
        # Load or train classifier
        classifier = NaiveBayesClassifier(train_or_load_model())
        
        # Predict classification
        predict(classifier, question)
        
        # Get word analysis
        words = query_abstract(question)
        
        result_map = {
            "domain": classifier.classify(question),
            "words": words
        }
        
        return jsonify(result_map)
    
    def predict(classifier, text):
        print(f"《{text}》 属于分类 【{classifier.classify(text)}】")
    
    def train_or_load_model():
        # Try to load existing model
        try:
            with open(MODEL_PATH, 'rb') as f:
                model = pickle.load(f)
                if model:
                    return model
        except:
            pass
        
        # Check if corpus folder exists
        corpus_folder = os.path.join(os.getcwd(), CORPUS_FOLDER)
        if not os.path.exists(corpus_folder) or not os.path.isdir(corpus_folder):
            print("没有文本分类语料，请下载语料库")
            return None
        
        # Create and train classifier
        classifier = NaiveBayesClassifier()
        classifier.train(CORPUS_FOLDER)
        model = classifier.get_model()
        
        # Save model for future use
        with open(MODEL_PATH, 'wb') as f:
            pickle.dump(model, f)
        
        return model
    
    def query_abstract(query_sentence):
        # Sentence abstraction
        # This would use a Python NLP library like HanLP or spaCy
        
        abstract_query = ""
        abstract_map = {}
        nr_count = 0  # Count of person names
        
        # Example implementation:
        # import spacy
        # nlp = spacy.load("zh_core_web_sm")
        # doc = nlp(query_sentence)
        # for token in doc:
        #     word = token.text
        #     term_str = f"{word}/{token.pos_}"
        #     print(term_str)
        #     
        #     if "PROPN" in token.pos_ and "PERSON" in token.ent_type_:  # nm - movie name
        #         abstract_query += "nm "
        #         abstract_map["nm"] = word
        #     elif "PROPN" in token.pos_ and nr_count == 0:  # nr - person name
        #         abstract_query += "nnt "
        #         abstract_map["nnt"] = word
        #         nr_count += 1
        #     elif "PROPN" in token.pos_ and nr_count == 1:  # nr - second person name
        #         abstract_query += "nnr "
        #         abstract_map["nnr"] = word
        #         nr_count += 1
        #     elif "NUM" in token.pos_:  # x - rating
        #         abstract_query += "x "
        #         abstract_map["x"] = word
        #     elif "NOUN" in token.pos_ and "GENRE" in token.ent_type_:  # ng - genre
        #         abstract_query += "ng "
        #         abstract_map["ng"] = word
        #     else:
        #         abstract_query += word + " "
        
        print("========NLP分词结束========")
        return abstract_query
    
    def query_extension(query_pattern):
        # Sentence restoration
        keys = abstract_map.keys()
        for key in keys:
            # If the sentence template contains abstract part of speech
            if key in query_pattern:
                # Replace abstract part of speech with specific value
                value = abstract_map[key]
                query_pattern = query_pattern.replace(key, value)
        
        extended_query = query_pattern
        
        # Clear abstract map for next sentence
        abstract_map.clear()
        abstract_map = None
        
        return extended_query
    """
