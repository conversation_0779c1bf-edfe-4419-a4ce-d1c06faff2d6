# 可执行图式操作模型优化建议

## 一、当前实现与问题分析

### 1. 当前实现概述

当前的可执行图式实现采用了从图式主题节点找到时序首节点，然后按照图结构一步步执行的方式。这种实现主要在`pam/tasks`文件夹下，包括`DoSelectTreeTask`（条件结构）、`ForEachTask`（循环结构）、`DoSuccTask`（顺序执行）等类。

当前实现的主要特点包括：

1. **树状结构**：使用时序主题节点统筹整个时序图式，通过时序边连接各步骤节点
2. **执行流程**：从主题节点开始，找到时序首节点，然后按照顺承边的指引依次执行各步骤
3. **任务调度**：通过`AssistingTaskSpawner`类实现任务的创建和调度
4. **图数据库查询**：使用Neo4j图数据库查询来定位和遍历图结构
5. **变量绑定**：支持变量绑定和替换，实现数据传递
6. **控制结构**：支持条件判断、循环等控制结构，实现复杂的执行逻辑

关键实现代码如下：

```java
// 在PamImpl0类中的getActRoot方法，从时序首开始执行
@Override
public void getActRoot(Link link, boolean isVar, boolean isLoop, String actStamp) {
    Node sink = (Node) link.getSink();
    Node source = link.getSource();
    String sname = sink.getTNname();
    putMap(sink,sname);

    // 从时序首开始执行，递归查找到最上头时序
    String query = "match (m)-[r:时序首]->(i) where id(m) = " + sink.getNodeId() + " return r";
    Link link0 = null;
    try (Transaction tx0 = graphDb.beginTx()) {
        // ... 查询并处理结果 ...
        tx0.commit();
    }

    if(link0 != null) {
        if(!isVar){
            // 如果有可能的后续嵌套时序，则将上位时序存入主路线，以便回溯执行
            mainlist.add(link);
        }
        // 尾递归查找执行全部子时序
        getActRoot(link0, false, false, actStamp);
    } else {
        // 这里是尽头，后面没有时序，从这根据节点类型开始执行具体逻辑
        if(AgentStarter.ifelsemap.containsKey(sname)){
            DoSelectTreeTask doSelectTreeTask = new DoSelectTreeTask(link, this, goalNs, sceneNs, actStamp);
            taskSpawner.addTask(doSelectTreeTask);
        } else {
            // 除了判断，都统一执行，有操作的再针对处理，顺承、变量、初始化等
            DoMindActTask doMindActTask = new DoMindActTask(sink, source, this, seqNs, goalNs, actStamp);
            taskSpawner.addTask(doMindActTask);
        }
    }
}
```

### 2. 存在的问题

尽管当前实现已经采用了图结构表示可执行图式，但仍然存在一些问题：

1. **操作与图谱数据分离**：当前的实现中，图式结构存储在图谱中，但操作的实际执行需要通过代码来解释和执行，而不是完全基于图谱数据
2. **扩展性受限**：AGI系统需要支持数以万计的操作，当前的实现方式可能难以扩展到如此规模
3. **学习能力受限**：系统难以通过学习自动获取新的操作能力，因为操作的执行需要代码的支持
4. **代码与数据分离**：人类大脑中的操作知识是以数据形式存在的，而当前的实现中操作的执行需要代码的支持
5. **记忆机制不完善**：当前的实现中，缺乏对操作结果的系统化记忆和复用机制，可能导致重复计算

## 二、优化方案

基于当前实现的分析，我们提出以下优化方案，旨在保留当前图式执行模型的优势，同时解决操作代码固化的问题。

### 1. 分层操作模型

我们建议采用分层操作模型，将操作分为三个层次：

1. **底层API操作**：少量核心操作，通过实体类实现，如基本的字符串处理、数学运算、图谱搜索等
2. **高频复合操作**：常用的复合操作，也可通过实体类实现，以提高执行效率
3. **图式定义操作**：大多数操作，通过图式结构定义，不需要专门的实体类

这种分层模型保留了当前实现的图式执行机制（从主题节点找到时序首节点，然后一步步执行），同时将操作的实际执行逻辑从代码中移到图谱数据中，使操作知识也能像图式结构一样存储在图谱中。

### 2. 操作注册与解释机制

为了支持图式定义操作，我们需要实现一个操作注册与解释机制。这个机制将与当前的`AssistingTaskSpawner`和图式执行流程集成，使操作的定义和执行都可以基于图谱数据：

```java
public class OperationRegistry {
    // 底层API操作映射
    private static final Map<String, Operation> coreOperations = new HashMap<>();

    // 图式定义操作映射
    private static final Map<String, ExecutableSchema> schemaOperations = new HashMap<>();

    // 操作路径映射
    private static final Map<String, String> operationPaths = new HashMap<>();

    // 注册底层API操作
    public static void registerCoreOperation(String path, Operation operation) {
        coreOperations.put(path, operation);
        operationPaths.put(path, path);
    }

    // 注册图式定义操作
    public static void registerSchemaOperation(String path, ExecutableSchema schema) {
        schemaOperations.put(path, schema);
        operationPaths.put(path, path);
    }

    // 根据操作路径获取操作执行器
    public static OperationExecutor getOperationByPath(String path) {
        if (coreOperations.containsKey(path)) {
            return new CoreOperationExecutor(coreOperations.get(path));
        } else if (schemaOperations.containsKey(path)) {
            return new SchemaOperationExecutor(schemaOperations.get(path));
        }
        return null;
    }

    // 根据操作名称获取操作执行器
    public static OperationExecutor getOperationByName(String name) {
        String path = operationPaths.get(name);
        if (path != null) {
            return getOperationByPath(path);
        }
        return null;
    }
}

// 操作执行器接口
public interface OperationExecutor {
    Object execute(ExecutionContext context);
}

// 底层API操作执行器
public class CoreOperationExecutor implements OperationExecutor {
    private Operation operation;

    public CoreOperationExecutor(Operation operation) {
        this.operation = operation;
    }

    @Override
    public Object execute(ExecutionContext context) {
        // 先尝试从记忆中搜索结果
        String cacheKey = generateCacheKey(context);
        if (context.hasCachedResult(cacheKey)) {
            return context.getCachedResult(cacheKey);
        }

        // 如果没有缓存结果，执行操作
        Object result = operation.execute(context);

        // 缓存结果供未来使用
        context.cacheResult(cacheKey, result);

        return result;
    }

    // 生成缓存键
    private String generateCacheKey(ExecutionContext context) {
        // 基于操作类型和输入参数生成唯一的缓存键
        StringBuilder keyBuilder = new StringBuilder(operation.getClass().getSimpleName());

        // 添加相关参数值
        for (String paramName : operation.getRequiredParameters()) {
            Object paramValue = context.getVariable(paramName);
            keyBuilder.append("_").append(paramName).append("=");
            keyBuilder.append(paramValue != null ? paramValue.toString() : "null");
        }

        return keyBuilder.toString();
    }
}

// 图式定义操作执行器
public class SchemaOperationExecutor implements OperationExecutor {
    private ExecutableSchema schema;

    public SchemaOperationExecutor(ExecutableSchema schema) {
        this.schema = schema;
    }

    @Override
    public Object execute(ExecutionContext context) {
        // 先尝试从记忆中搜索结果
        String cacheKey = generateCacheKey(context);
        if (context.hasCachedResult(cacheKey)) {
            return context.getCachedResult(cacheKey);
        }

        // 创建子执行上下文
        ExecutionContext subContext = new ExecutionContext(context);

        // 执行图式
        SchemaExecutionEngine engine = new SchemaExecutionEngine();
        ExecutionResult result = engine.execute(schema, subContext);

        // 将子上下文的变量合并回父上下文
        context.mergeVariables(subContext);

        // 缓存结果供未来使用
        context.cacheResult(cacheKey, result.getResult());

        return result.getResult();
    }

    // 生成缓存键
    private String generateCacheKey(ExecutionContext context) {
        StringBuilder keyBuilder = new StringBuilder(schema.getId());

        // 添加相关参数值
        for (String paramName : schema.getRequiredParameters()) {
            Object paramValue = context.getVariable(paramName);
            keyBuilder.append("_").append(paramName).append("=");
            keyBuilder.append(paramValue != null ? paramValue.toString() : "null");
        }

        return keyBuilder.toString();
    }
}
```

### 3. 操作原子化与组合

按照您的建议，我们应该将操作原子化，并通过组合这些原子操作来实现复杂功能：

1. **操作原子化**：将复杂操作分解为不可再分的原子操作
2. **图式组合**：通过图式结构组合原子操作，实现复杂功能
3. **操作标记**：在操作节点上标记操作名称，通过特定路径关联到底层API

这种方法的优势在于：

- **可扩展性**：只需实现有限数量的原子操作，就可以组合出无限多的复杂操作
- **灵活性**：可以动态定义新的操作，无需修改代码
- **可维护性**：原子操作数量有限，易于维护
- **资源效率**：减少类定义，降低内存占用

### 4. 操作与底层API关联

与传统的链式路径不同，操作与底层API的关联是多维的。在一个可执行图式中，操作节点可以直接关联到底层API，也可以关联到其他图式。当操作被分解到原子操作层面时，最终会关联到底层API。

```java
public class OperationNode extends Node {
    private String operationType;
    private String apiReference;  // 底层API引用
    private String schemaReference;  // 图式引用（当操作由其他图式实现时）
    private List<String> parameters = new ArrayList<>();

    // 构造函数省略...

    // 设置底层API引用
    public void setApiReference(String apiReference) {
        this.apiReference = apiReference;
        this.schemaReference = null;  // 不能同时引用API和图式
    }

    // 设置图式引用
    public void setSchemaReference(String schemaReference) {
        this.schemaReference = schemaReference;
        this.apiReference = null;  // 不能同时引用API和图式
    }

    // 检查是否关联到底层API
    public boolean isApiReferenced() {
        return apiReference != null;
    }

    // 检查是否关联到图式
    public boolean isSchemaReferenced() {
        return schemaReference != null;
    }

    // 获取底层API引用
    public String getApiReference() {
        return apiReference;
    }

    // 获取图式引用
    public String getSchemaReference() {
        return schemaReference;
    }
}
```

在执行引擎中，我们需要根据操作节点的引用类型来决定如何执行：

```java
public Object executeOperationNode(OperationNode node, ExecutionContext context) {
    // 先尝试从记忆中搜索结果
    String cacheKey = generateCacheKey(node, context);
    if (context.hasCachedResult(cacheKey)) {
        return context.getCachedResult(cacheKey);
    }

    Object result = null;

    if (node.isApiReferenced()) {
        // 如果关联到底层API，直接执行
        String apiReference = node.getApiReference();
        OperationExecutor executor = OperationRegistry.getOperationByPath(apiReference);
        if (executor == null) {
            throw new RuntimeException("找不到API: " + apiReference);
        }
        result = executor.execute(context);
    } else if (node.isSchemaReferenced()) {
        // 如果关联到图式，执行子图式
        String schemaReference = node.getSchemaReference();
        ExecutableSchema subSchema = SchemaRegistry.getSchema(schemaReference);
        if (subSchema == null) {
            throw new RuntimeException("找不到图式: " + schemaReference);
        }

        // 创建子执行上下文
        ExecutionContext subContext = new ExecutionContext(context);

        // 执行子图式
        SchemaExecutionEngine engine = new SchemaExecutionEngine();
        ExecutionResult subResult = engine.execute(subSchema, subContext);

        // 将子上下文的变量合并回父上下文
        context.mergeVariables(subContext);

        result = subResult.getResult();
    } else {
        throw new RuntimeException("操作节点没有关联到API或图式: " + node.getName());
    }

    // 缓存结果供未来使用
    context.cacheResult(cacheKey, result);

    return result;
}

// 生成缓存键
private String generateCacheKey(OperationNode node, ExecutionContext context) {
    StringBuilder keyBuilder = new StringBuilder(node.getName());

    // 添加相关参数值
    for (String paramName : node.getParameters()) {
        Object paramValue = context.getVariable(paramName);
        keyBuilder.append("_").append(paramName).append("=");
        keyBuilder.append(paramValue != null ? paramValue.toString() : "null");
    }

    return keyBuilder.toString();
}
```
## 三、实现示例

### 1. 底层API操作示例

```java
// 字符串操作API
public class StringOperations {
    // 连接字符串
    public static class Concat implements Operation {
        @Override
        public Object execute(ExecutionContext context) {
            String str1 = (String) context.getVariable("str1");
            String str2 = (String) context.getVariable("str2");
            return str1 + str2;
        }

        @Override
        public List<String> getRequiredParameters() {
            return Arrays.asList("str1", "str2");
        }
    }

    // 截取字符串
    public static class Substring implements Operation {
        @Override
        public Object execute(ExecutionContext context) {
            String str = (String) context.getVariable("str");
            int start = (int) context.getVariable("start");
            int end = (int) context.getVariable("end");
            return str.substring(start, end);
        }

        @Override
        public List<String> getRequiredParameters() {
            return Arrays.asList("str", "start", "end");
        }
    }
}

// 数学运算API
public class MathOperations {
    // 加法运算（带进位）
    public static class AddWithCarry implements Operation {
        @Override
        public Object execute(ExecutionContext context) {
            int digit1 = (int) context.getVariable("位1");
            int digit2 = (int) context.getVariable("位2");
            int carry = (int) context.getVariable("进位");

            int sum = digit1 + digit2 + carry;
            int currentDigitResult = sum % 10;
            int newCarry = sum / 10;

            Map<String, Object> result = new HashMap<>();
            result.put("digit_result", currentDigitResult);
            result.put("new_carry", newCarry);

            return result;
        }

        @Override
        public List<String> getRequiredParameters() {
            return Arrays.asList("位1", "位2", "进位");
        }
    }
}

// 记忆搜索API
public class MemoryOperations {
    // 搜索加法结果
    public static class SearchAddition implements Operation {
        @Override
        public Object execute(ExecutionContext context) {
            int digit1 = (int) context.getVariable("位1");
            int digit2 = (int) context.getVariable("位2");
            int carry = (int) context.getVariable("进位");

            // 构建搜索键
            String searchKey = "add_" + digit1 + "_" + digit2 + "_" + carry;

            // 从记忆中搜索
            if (context.hasCachedResult(searchKey)) {
                return context.getCachedResult(searchKey);
            }

            // 如果没有缓存，返回null，让调用者执行实际计算
            return null;
        }

        @Override
        public List<String> getRequiredParameters() {
            return Arrays.asList("位1", "位2", "进位");
        }
    }
}

// 注册底层API
OperationRegistry.registerCoreOperation("string/basic/concat", new StringOperations.Concat());
OperationRegistry.registerCoreOperation("string/basic/substring", new StringOperations.Substring());
OperationRegistry.registerCoreOperation("math/basic/add_with_carry", new MathOperations.AddWithCarry());
OperationRegistry.registerCoreOperation("memory/search/addition", new MemoryOperations.SearchAddition());
```

### 2. 图式定义操作示例

```java
// 创建一个"多位数加法"的图式操作
ExecutableSchema multiDigitAdditionSchema = new ExecutableSchema("multi_digit_addition", "多位数加法");

// 创建上下文节点
ContextNode additionContext = new ContextNode("1", "加法计算", "global");
multiDigitAdditionSchema.setContextNode(additionContext);

// 创建操作节点
OperationNode initOperation = new OperationNode("10", "初始化", "initialization",
        Arrays.asList("操作数1", "操作数2"));
initOperation.setApiReference("variable/basic/set_multiple");

OperationNode getCurrentDigit = new OperationNode("11", "获取当前位", "data_access",
        Arrays.asList("操作数1", "操作数2", "当前位索引"));
// 先尝试从记忆中搜索，如果没有再计算
getCurrentDigit.setApiReference("memory/search/digit_position");

OperationNode calculateDigitAndCarry = new OperationNode("12", "计算当前位和进位", "arithmetic",
        Arrays.asList("位1", "位2", "进位"));
// 先尝试从记忆中搜索，如果没有再计算
calculateDigitAndCarry.setApiReference("memory/search/addition");

OperationNode updateResult = new OperationNode("13", "更新结果", "data_update",
        Arrays.asList("结果", "当前位结果"));
updateResult.setApiReference("string/basic/prepend");

OperationNode moveToNextDigit = new OperationNode("14", "移动到下一位", "arithmetic",
        Arrays.asList("当前位索引"));
moveToNextDigit.setApiReference("math/basic/decrement");

OperationNode addFinalCarry = new OperationNode("15", "添加进位", "data_update",
        Arrays.asList("结果", "进位"));
addFinalCarry.setApiReference("string/basic/prepend_int_to_string");

OperationNode skipCarry = new OperationNode("16", "跳过进位", "noop",
        Collections.emptyList());
skipCarry.setApiReference("control/basic/noop");

OperationNode returnResult = new OperationNode("17", "返回结果", "return",
        Arrays.asList("结果"));
returnResult.setApiReference("variable/basic/get");

// 创建控制节点
ControlNode processDigitsLoop = new ControlNode("8", "处理位循环", "loop", "当前位索引 >= 0");
ControlNode processFinalCarry = new ControlNode("9", "处理最终进位", "conditional", "进位 > 0");

// 添加节点到图式
multiDigitAdditionSchema.addNode(initOperation);
multiDigitAdditionSchema.addNode(getCurrentDigit);
multiDigitAdditionSchema.addNode(calculateDigitAndCarry);
multiDigitAdditionSchema.addNode(updateResult);
multiDigitAdditionSchema.addNode(moveToNextDigit);
multiDigitAdditionSchema.addNode(addFinalCarry);
multiDigitAdditionSchema.addNode(skipCarry);
multiDigitAdditionSchema.addNode(returnResult);
multiDigitAdditionSchema.addNode(processDigitsLoop);
multiDigitAdditionSchema.addNode(processFinalCarry);

// 创建时序边（树状结构）
Edge contextToInit = new SequenceEdge("101", additionContext, initOperation, true);
Edge contextToLoop = new SequenceEdge("102", additionContext, processDigitsLoop, false);
Edge contextToCondition = new SequenceEdge("103", additionContext, processFinalCarry, false);
Edge contextToReturn = new SequenceEdge("104", additionContext, returnResult, false);

// 添加时序边到图式
multiDigitAdditionSchema.addEdge(contextToInit);
multiDigitAdditionSchema.addEdge(contextToLoop);
multiDigitAdditionSchema.addEdge(contextToCondition);
multiDigitAdditionSchema.addEdge(contextToReturn);

// 创建循环内部时序边
Edge loopToGetDigit = new SequenceEdge("105", processDigitsLoop, getCurrentDigit, true);
Edge loopToCalculate = new SequenceEdge("106", processDigitsLoop, calculateDigitAndCarry, false);
Edge loopToUpdate = new SequenceEdge("107", processDigitsLoop, updateResult, false);
Edge loopToMove = new SequenceEdge("108", processDigitsLoop, moveToNextDigit, false);

// 添加循环内部时序边到图式
multiDigitAdditionSchema.addEdge(loopToGetDigit);
multiDigitAdditionSchema.addEdge(loopToCalculate);
multiDigitAdditionSchema.addEdge(loopToUpdate);
multiDigitAdditionSchema.addEdge(loopToMove);

// 创建条件分支时序边
Edge conditionToAddCarry = new ConditionalEdge("109", processFinalCarry, addFinalCarry, true);
Edge conditionToSkipCarry = new ConditionalEdge("110", processFinalCarry, skipCarry, false);

// 添加条件分支时序边到图式
multiDigitAdditionSchema.addEdge(conditionToAddCarry);
multiDigitAdditionSchema.addEdge(conditionToSkipCarry);

// 创建顺承边（链式结构）
Edge initToLoop = new SuccessionEdge("201", initOperation, processDigitsLoop);
Edge loopToCondition = new SuccessionEdge("202", processDigitsLoop, processFinalCarry);
Edge conditionToReturn = new SuccessionEdge("203", processFinalCarry, returnResult);

// 添加顺承边到图式
multiDigitAdditionSchema.addEdge(initToLoop);
multiDigitAdditionSchema.addEdge(loopToCondition);
multiDigitAdditionSchema.addEdge(conditionToReturn);

// 创建循环内部顺承边
Edge getDigitToCalculate = new SuccessionEdge("204", getCurrentDigit, calculateDigitAndCarry);
Edge calculateToUpdate = new SuccessionEdge("205", calculateDigitAndCarry, updateResult);
Edge updateToMove = new SuccessionEdge("206", updateResult, moveToNextDigit);

// 添加循环内部顺承边到图式
multiDigitAdditionSchema.addEdge(getDigitToCalculate);
multiDigitAdditionSchema.addEdge(calculateToUpdate);
multiDigitAdditionSchema.addEdge(updateToMove);

// 创建循环条件边
Edge moveToLoop = new LoopEdge("207", moveToNextDigit, processDigitsLoop, "当前位索引 >= 0");

// 添加循环条件边到图式
multiDigitAdditionSchema.addEdge(moveToLoop);

// 创建条件分支顺承边
Edge addCarryToReturn = new SuccessionEdge("208", addFinalCarry, returnResult);
Edge skipCarryToReturn = new SuccessionEdge("209", skipCarry, returnResult);

// 添加条件分支顺承边到图式
multiDigitAdditionSchema.addEdge(addCarryToReturn);
multiDigitAdditionSchema.addEdge(skipCarryToReturn);

// 创建数据流边
// ... 省略数据流边的创建代码

// 注册图式操作
OperationRegistry.registerSchemaOperation("math/advanced/multi_digit_addition", multiDigitAdditionSchema);
```

### 3. 使用示例

```java
// 获取操作执行器
OperationExecutor additionExecutor = OperationRegistry.getOperationByPath("math/advanced/multi_digit_addition");

// 创建执行上下文
ExecutionContext context = new ExecutionContext();
context.setVariable("操作数1", "123");
context.setVariable("操作数2", "456");

// 执行操作
Object result = additionExecutor.execute(context);
System.out.println("结果: " + result);  // 输出: "结果: 579"

// 再次执行，这次将使用缓存的结果
context = new ExecutionContext();
context.setVariable("操作数1", "123");
context.setVariable("操作数2", "456");
result = additionExecutor.execute(context);
System.out.println("结果(使用缓存): " + result);  // 输出: "结果(使用缓存): 579"

// 执行单位加法（使用底层API）
OperationExecutor digitAddExecutor = OperationRegistry.getOperationByPath("math/basic/add_with_carry");
ExecutionContext digitContext = new ExecutionContext();
digitContext.setVariable("位1", 9);
digitContext.setVariable("位2", 5);
digitContext.setVariable("进位", 0);
Map<String, Object> digitResult = (Map<String, Object>) digitAddExecutor.execute(digitContext);
System.out.println("位结果: " + digitResult.get("digit_result") + ", 进位: " + digitResult.get("new_carry"));
// 输出: "位结果: 4, 进位: 1"

// 下次执行相同的单位加法时，将使用缓存的结果
digitContext = new ExecutionContext();
digitContext.setVariable("位1", 9);
digitContext.setVariable("位2", 5);
digitContext.setVariable("进位", 0);
digitResult = (Map<String, Object>) digitAddExecutor.execute(digitContext);
System.out.println("位结果(使用缓存): " + digitResult.get("digit_result") + ", 进位: " + digitResult.get("new_carry"));
// 输出: "位结果(使用缓存): 4, 进位: 1"
```

## 四、与当前系统的集成

为了将这种优化方案与当前系统集成，我们需要进行以下改进：

### 1. 修改执行引擎

修改`SchemaExecutionEngine`类，支持操作路径解析：

```java
private Object executeOperationNode(OperationNode node, ExecutableSchema schema, ExecutionContext context) {
    String operationName = node.getName();
    String operationPath = node.getProperty("operation_path").toString();

    // 获取操作执行器
    OperationExecutor executor = OperationRegistry.getOperation(operationPath);
    if (executor == null) {
        throw new RuntimeException("找不到操作: " + operationPath);
    }

    // 执行操作
    return executor.execute(context);
}
```

### 2. 扩展操作节点

扩展`OperationNode`类，添加操作路径属性：

```java
public class OperationNode extends Node {
    private String operationType;
    private String operationPath;
    private List<String> parameters = new ArrayList<>();

    public OperationNode(String id, String name, String operationType, String operationPath, List<String> parameters) {
        super(id, name, "OperationNode");
        this.operationType = operationType;
        this.operationPath = operationPath;
        if (parameters != null) {
            this.parameters.addAll(parameters);
        }
        setProperty("operation_type", operationType);
        setProperty("operation_path", operationPath);
        setProperty("parameters", parameters);
    }

    public String getOperationPath() {
        return operationPath;
    }
}
```

### 3. 实现动态加载机制

实现动态加载机制，支持从配置文件或数据库加载图式定义操作：

```java
public class SchemaLoader {
    // 从配置文件加载图式定义
    public static void loadSchemasFromConfig(String configPath) {
        // 读取配置文件
        // 解析图式定义
        // 注册到OperationRegistry
    }

    // 从数据库加载图式定义
    public static void loadSchemasFromDatabase(Connection connection) {
        // 查询数据库
        // 解析图式定义
        // 注册到OperationRegistry
    }
}
```

## 五、记忆搜索与执行效率

在优化方案中，记忆搜索机制是一个关键组件，它模拟了人类计算的方式，显著提高了执行效率。

### 1. 记忆搜索机制

记忆搜索机制的核心思想是：在执行操作前，先尝试从记忆中搜索结果，如果找到则直接使用，如果找不到才执行计算。这与人类的计算方式非常相似。

例如，当计算9+5时，人类通常不会重新计算，而是直接从记忆中提取结果14。同样，在多位数加法中，当计算单位数字相加时，系统可以先尝试从记忆中搜索结果。

```java
// 记忆搜索的实现
public Object executeWithMemorySearch(String operationKey, ExecutionContext context, Supplier<Object> calculator) {
    // 构建缓存键
    String cacheKey = operationKey + "_" + generateParameterKey(context);

    // 尝试从记忆中搜索
    if (context.hasCachedResult(cacheKey)) {
        return context.getCachedResult(cacheKey);
    }

    // 如果没有缓存，执行计算
    Object result = calculator.get();

    // 缓存结果供未来使用
    context.cacheResult(cacheKey, result);

    return result;
}
```

### 2. 多层次记忆搜索

在复杂操作中，记忆搜索可以在多个层次进行：

1. **操作级别**：搜索整个操作的结果（如整个多位数加法）
2. **子操作级别**：搜索子操作的结果（如单位加法）
3. **原子操作级别**：搜索原子操作的结果（如基本算术运算）

这种多层次的记忆搜索可以显著提高执行效率，特别是对于重复执行的操作。

### 3. 记忆管理

为了防止记忆无限增长，需要实现记忆管理机制：

1. **过期策略**：定期清理长时间未使用的记忆
2. **容量限制**：限制记忆的最大容量，超过时清理最不常用的记忆
3. **优先级策略**：为不同类型的记忆设置不同的优先级，优先保留高频使用的记忆

## 六、总结与建议

基于以上分析，我们建议采用以下方案优化可执行图式的操作模型：

1. **实现分层操作模型**：将操作分为底层API操作、高频复合操作和图式定义操作
2. **操作原子化**：将复杂操作分解为原子操作，通过图式结构组合实现复杂功能
3. **实现操作注册与解释机制**：支持动态注册和解释操作
4. **实现操作与底层API关联**：通过引用将操作节点与底层API或其他图式关联
5. **集成记忆搜索机制**：在执行操作前先尝试从记忆中搜索结果，提高执行效率
6. **保留树状结构**：保留时序主题节点统筹整个时序图式的树状结构，便于查询和管理
7. **扩展现有系统**：修改执行引擎和操作节点，支持新的操作模型

这种方案可以大幅提高系统的可扩展性和灵活性，同时保持与现有系统的兼容性。通过这种方式，我们可以实现"一次实现，多处使用"的目标，使系统能够支持数以万计的操作，而无需为每个操作都创建专门的实体类。

通过集成记忆搜索机制，系统的执行效率将显著提高，特别是对于重复执行的操作。这种方式不仅模拟了人类的计算方式，也使系统的行为更接近人类认知。

最重要的是，这种方案符合AGI系统的设计理念，支持系统的持续进化和扩展，使其能够适应不断变化的需求和环境。
