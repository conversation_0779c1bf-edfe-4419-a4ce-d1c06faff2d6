"""
Graph Query

This module provides a class for querying graphs.
"""

class GraphQuery:
    """Graph Query class for querying graphs."""
    
    def __init__(self, domain="", page_index=1, page_size=10, node_name=None, limit=None):
        """
        Initialize a Graph Query.
        
        Args:
            domain: The domain to query
            page_index: The page index
            page_size: The page size
            node_name: The node name to filter by
            limit: The maximum number of results to return
        """
        self.domain = domain
        self.page_index = page_index
        self.page_size = page_size
        self.node_name = node_name
        self.limit = limit
    
    def get_domain(self):
        """
        Get the domain.
        
        Returns:
            The domain
        """
        return self.domain
    
    def set_domain(self, domain):
        """
        Set the domain.
        
        Args:
            domain: The domain
        """
        self.domain = domain
        return self
    
    def get_page_index(self):
        """
        Get the page index.
        
        Returns:
            The page index
        """
        return self.page_index
    
    def set_page_index(self, page_index):
        """
        Set the page index.
        
        Args:
            page_index: The page index
        """
        self.page_index = page_index
        return self
    
    def get_page_size(self):
        """
        Get the page size.
        
        Returns:
            The page size
        """
        return self.page_size
    
    def set_page_size(self, page_size):
        """
        Set the page size.
        
        Args:
            page_size: The page size
        """
        self.page_size = page_size
        return self
    
    def get_node_name(self):
        """
        Get the node name.
        
        Returns:
            The node name
        """
        return self.node_name
    
    def set_node_name(self, node_name):
        """
        Set the node name.
        
        Args:
            node_name: The node name
        """
        self.node_name = node_name
        return self
    
    def get_limit(self):
        """
        Get the limit.
        
        Returns:
            The limit
        """
        return self.limit
    
    def set_limit(self, limit):
        """
        Set the limit.
        
        Args:
            limit: The limit
        """
        self.limit = limit
        return self
