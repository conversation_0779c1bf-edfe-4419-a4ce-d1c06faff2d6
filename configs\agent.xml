<?xml version="1.0" encoding="UTF-8"?>
<!--
    Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved.
    This program and the accompanying materials are made available
    under the terms of the LIDA Software Framework Non-Commercial License v1.0
    which accompanies this distribution, and is available at
    http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
 -->
<lida xmlns="http://ccrg.cs.memphis.edu/LidaXMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://ccrg.cs.memphis.edu/LidaXMLSchema LidaXMLSchema.xsd ">
	<globalparams>
		<param name="ff" type="int">50</param>
	</globalparams>
    <taskmanager>
        <param name="taskManager.tickDuration" type="int">10</param>
        <param name="taskManager.maxNumberOfThreads" type="int"> 100</param>
    </taskmanager>
    <taskspawners>
        <taskspawner name="defaultTS">
            <class>linars.edu.memphis.ccrg.lida.Framework.Tasks.TaskSpawnerImpl</class>
        </taskspawner>
    </taskspawners>
    <submodules>
        <module name="Environment">
            <class>linars.edu.memphis.ccrg.lida.Alifeagent.Environment.ALifeEnvironment</class>
            <param name="environment.healthDecayRate" type="double">0.003</param>
            <param name="environment.width" type="int">10</param>
            <param name="environment.height" type="int">10</param>
            <param name="environment.ticksPerRun" type="int">250</param>
            <param name="environment.operationsConfig">configs/operations.properties</param>
            <param name="environment.objectsConfig">configs/objects.properties</param>
            <param name="environment.agentName">agent</param>
            <taskspawner>defaultTS</taskspawner>
        </module>

        <module name="SensoryMemory">
            <class>linars.edu.memphis.ccrg.lida.Alifeagent.Modules.BasicSensoryMemory</class>
            <associatedmodule>Environment</associatedmodule>
            <taskspawner>defaultTS</taskspawner>
            <initialTasks>
                <task name="backgroundTask">
                    <tasktype>SensoryMemoryBackgroundTask</tasktype>
                    <ticksperrun>6</ticksperrun>
                </task>
            </initialTasks>
        </module>
        <!-- 这样没法激活常驻+也没法成大哥=需要接口类？但可激活=调用才进线程池 20220808 -->
<!--        <module name="GoalManager">-->
<!--            <class>linars.edu.memphis.ccrg.lida.PAM.Tasks.GoalBackgroundTask</class>-->
<!--            <associatedmodule>PAMemory</associatedmodule>-->
<!--            <taskspawner>defaultTS</taskspawner>-->
<!--            <initialTasks>-->
<!--                <task name="GoalTask">-->
<!--                    <tasktype>GoalBackgroundTask</tasktype>-->
<!--                    <ticksperrun>5</ticksperrun>-->
<!--                </task>-->
<!--            </initialTasks>-->
<!--        </module>-->

        <module name="PAMemory">
            <class>linars.edu.memphis.ccrg.lida.PAM.PamImpl0</class>
            <associatedmodule>Environment</associatedmodule>
            <associatedmodule>GlobalWorkspace</associatedmodule>
            <associatedmodule>Workspace</associatedmodule>
            <param name="pam.Upscale" type="double">0.7</param>
            <param name="pam.Downscale" type="double">0.5</param>
            <param name="pam.perceptThreshold" type="double">0.4</param>
            <param name="pam.excitationTicksPerRun" type="int">1</param>
            <param name="pam.propagationTicksPerRun" type="int">1</param>
            <param name="pam.propagateActivationThreshold" type="double">0.05</param>

            <taskspawner>defaultTS</taskspawner>
            <initialTasks>
                <task name="HealthDetector">
                    <tasktype>HealthDetector</tasktype>
                    <ticksperrun>2000</ticksperrun><!--原本是200-->
                    <param name="nodes" type="string">goodHealth,fairHealth,badHealth</param>
                </task>

                <task name="ListenDetector">
                    <tasktype>ListenDetector</tasktype>
                    <ticksperrun>50</ticksperrun><!-- 修改为更小的值，使其能够更早地执行 -->
                    <param name="nodes" type="string">listen</param>
                </task>

                <task name="ObjectDetector">
                    <tasktype>ObjectDetector</tasktype>
                    <ticksperrun>2000</ticksperrun><!--原本是20-->
                    <param name="object" type="string"></param>
                </task>

                <task name="VarManagerTask0">
                    <tasktype>VarManagerTask</tasktype>
                    <ticksperrun>1000</ticksperrun><!--原本是10-->
                </task>

                <!-- 目标动机线程做固有必须有大哥带，除非自己成大哥，factory那边是设置参数，两边都需要 -->
                <task name="GoalBackgroundTask">
                    <tasktype>GoalBackgroundTask</tasktype>
                    <ticksperrun>5</ticksperrun><!--原本是50-->
                </task>

            </initialTasks>
            <initializerclass>linars.edu.memphis.ccrg.lida.PAM.BasicPamInitializer</initializerclass>
        </module>

        <module name="Workspace">
            <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceImpl</class>
            <submodules>
                <module name="EpisodicBuffer">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="PerceptualBuffer">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="CurrentSituationalModel">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="SceneGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="GrammarGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="UnderstandGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="NonGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

<!--                <module name="narmemory">-->
<!--                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>-->
<!--                    <taskspawner>defaultTS</taskspawner>-->
<!--                </module>-->

                <module name="FeelGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <!-- 目标用树图=竞争=非先进先出 -->
                <module name="GoalGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WSBufferImpl_graph</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="ConcentGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="WordGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>

                <module name="SeqGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>


                <module name="VisionGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="ListenGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="TextGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="VVGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>
                <module name="VListenGraph">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.WMBufferImpl_mem</class>
                    <taskspawner>defaultTS</taskspawner>
                </module>


                <module name="BroadcastQueue">
                    <class>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceBuffers.BroadcastQueueImpl</class>
                    <param name="Workspace.broadcastQueueCapacity" type="int">30</param>
                    <taskspawner>defaultTS</taskspawner>
                </module>
            </submodules>
            <taskspawner>defaultTS</taskspawner>
            <initialTasks>
                <task name="updateCsmBackground">
                    <tasktype>UpdateCsmBackgroundTask</tasktype>
                    <ticksperrun>5</ticksperrun>
                </task>
            </initialTasks>
        </module>
        <module name="StructureBuildingCodeletModule">
            <class>linars.edu.memphis.ccrg.lida.Workspace.StructureBuildingCodelets.StructureBuildingCodeletModule</class>
            <associatedmodule>Workspace</associatedmodule>
            <taskspawner>defaultTS</taskspawner>
        </module>
        <module name="GlobalWorkspace">
            <class>linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspaceImpl</class>
            <param name="globalWorkspace.coalitionRemovalThreshold" type="double">0.0</param>
            <param name="globalWorkspace.coalitionDecayStrategy">coalitionDecay</param>
            <param name="globalWorkspace.refractoryPeriod" type="int">15</param>
            <!-- Trigger parameters -->
            <param name="globalWorkspace.delayNoBroadcast" type="int">100</param>
            <param name="globalWorkspace.delayNoNewCoalition" type="int">50</param>
            <param name="globalWorkspace.aggregateActivationThreshold" type="double">0.8</param>
            <param name="globalWorkspace.individualActivationThreshold" type="double">0.5</param>
            <taskspawner>defaultTS</taskspawner>
            <initializerclass>linars.edu.memphis.ccrg.lida.GlobalWorkspace.GlobalWorkspaceInitializer</initializerclass>
        </module>
        <!--非固有线程，这里也可激活，但需要时才纳入线程池-->
        <module name="LanGen">
            <class>linars.edu.memphis.ccrg.lida.Nlanguage.LanGenImpl</class>
            <associatedmodule>Workspace</associatedmodule>
            <associatedmodule>PAMemory</associatedmodule>
            <param name="LanGen.ticksPerStep" type="int">14</param>
            <param name="LanGen.conditionDecayStrategy">conditionDecay</param>
            <param name="LanGen.schemeSelectionThreshold" type="double">0.1</param>
            <param name="LanGen.contextWeight" type="double">1.0</param>
            <param name="LanGen.addingListWeight"  type="double">1.0</param>
            <param name="linkCategory">temporal-link</param>
            <taskspawner>defaultTS</taskspawner>

<!--            <initialTasks>-->
<!--                <task name="VarManagerTask">-->
<!--                    <tasktype>VarManagerTask</tasktype>-->
<!--                    <ticksperrun>10</ticksperrun>-->
<!--                </task>-->
<!--            </initialTasks>-->

            <initializerclass>linars.edu.memphis.ccrg.lida.Nlanguage.LanGenInitializer</initializerclass>
        </module>

        <module name="ProceduralMemory">
            <class>linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralMemoryImpl</class>
            <associatedmodule>Workspace</associatedmodule>
            <param name="proceduralMemory.ticksPerStep" type="int">14</param>
           	<param name="proceduralMemory.conditionDecayStrategy">conditionDecay</param>
           	<param name="proceduralMemory.schemeSelectionThreshold" type="double">0.1</param>
           	<param name="proceduralMemory.contextWeight" type="double">1.0</param>
           	<param name="proceduralMemory.addingListWeight"  type="double">1.0</param>
           	<param name="proceduralMemory.schemeClass">linars.edu.memphis.ccrg.lida.ProceduralMemory.SchemeImpl</param>

            <param name="linkCategory">temporal-link</param>

            <param name="scheme.0">get|()()|get|190591|()()|0.01</param>

            <param name="scheme.1">left|()()|turnLeft|190653|()()|0.01</param>
            <param name="scheme.2">right|()()|turnRight|190730|()()|0.01</param>
            <param name="scheme.3">around|()()|turnAround|190533|()()|0.01</param>
            <param name="scheme.4">food|()()|moveAgent|190514|()()|0.1</param>
            <param name="scheme.5">origin|()()|eat|190532|()()|0.35</param>
            <param name="scheme.6">front|()()|fleeAgent|2043|()()|0.4</param>

            <taskspawner>defaultTS</taskspawner>
            <initializerclass>linars.edu.memphis.ccrg.lida.ProceduralMemory.BasicProceduralMemoryInitializer</initializerclass>
        </module>

        <module name="ActionSelection">
            <class>linars.edu.memphis.ccrg.lida.ActionSelection.BasicActionSelection</class>
<!--            <class>linars.edu.memphis.ccrg.lida.ActionSelection.BehaviorNetwork</class>-->
            <param name="ActionSelection.broadcastExcitationFactor" type="double">0.05</param>
            <param name="ActionSelection.successorExcitationFactor" type="double">0.04</param>
            <param name="ActionSelection.conflictorExcitationFactor" type="double">0.04</param>
            <param name="ActionSelection.predecessorExcitationFactor" type="double">0.1</param>
            <param name="ActionSelection.contextSatisfactionThreshold" type="double"> 0.5</param>
            <param name="ActionSelection.initialCandidateThreshold" type="double"> 1.0</param>
            <param name="ActionSelection.candidateThresholdDecayName" type="string">defaultDecay</param>
            <param name="ActionSelection.behaviorDecayName" type="string">behaviorDecay</param>
            <param name="actionSelection.ticksPerRun" type="int">10</param>
	    <taskspawner>defaultTS</taskspawner>
        </module>
        <module name="SensoryMotorMemory">
            <class>linars.edu.memphis.ccrg.lida.SensoryMotorMemory.BasicSensoryMotorMemory</class>
            <associatedmodule>Environment</associatedmodule>
            <param name="smm.processActionTaskTicks" type="int">1</param>
            <param name="smm.mapping.1">turnLeft,turnLeft</param>
            <param name="smm.mapping.2">turnRight,turnRight</param>
            <param name="smm.mapping.3">turnAround,turnAround</param>
            <param name="smm.mapping.4">moveAgent,moveAgent</param>
            <param name="smm.mapping.0">get,get</param>
            <param name="smm.mapping.5">eat,eat</param>
            <param name="smm.mapping.6">fleeAgent,flee</param>
            <taskspawner>defaultTS</taskspawner>
            <initializerclass>linars.edu.memphis.ccrg.lida.SensoryMotorMemory.BasicSensoryMotorMemoryInitializer</initializerclass>
        </module>
    </submodules>
    <listeners>
        <listener>
			<listenertype>linars.edu.memphis.ccrg.lida.SensoryMotorMemory.SensoryMotorMemoryListener</listenertype>
			<modulename>SensoryMotorMemory</modulename>
			<listenername>SensoryMemory</listenername>
		</listener>
		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.SensoryMemory.SensoryMemoryListener</listenertype>
			<modulename>SensoryMemory</modulename>
			<listenername>SensoryMotorMemory</listenername>
		</listener>
		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.PAM.PamListener</listenertype>
			<modulename>PAMemory</modulename>
			<listenername>Workspace</listenername>
		</listener>

        <listener>
            <listenertype>linars.edu.memphis.ccrg.lida.PAM.PamListener</listenertype>
            <modulename>LanGen</modulename>
            <listenername>Workspace</listenername>
        </listener>

		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.Workspace.WorkspaceListener</listenertype>
			<modulename>Workspace</modulename>
			<listenername>PAMemory</listenername>
		</listener>
		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>PAMemory</listenername>
		</listener>
		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>Workspace</listenername>
		</listener>
		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>TransientEpisodicMemory</listenername>
		</listener>
		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>ProceduralMemory</listenername>
		</listener>

		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.GlobalWorkspace.BroadcastListener</listenertype>
			<modulename>GlobalWorkspace</modulename>
			<listenername>LanGen</listenername>
		</listener>

		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.ProceduralMemory.ProceduralMemoryListener</listenertype>
			<modulename>ProceduralMemory</modulename>
			<listenername>ActionSelection</listenername>
		</listener>
		<listener>
			<listenertype>linars.edu.memphis.ccrg.lida.episodicmemory.LocalAssociationListener</listenertype>
			<modulename>TransientEpisodicMemory</modulename>
			<listenername>Workspace</listenername>
		</listener>
		<listener>
            <listenertype>linars.edu.memphis.ccrg.lida.ActionSelection.ActionSelectionListener</listenertype>
            <modulename>ActionSelection</modulename>
            <listenername>SensoryMotorMemory</listenername>
        </listener>
	</listeners>
</lida>