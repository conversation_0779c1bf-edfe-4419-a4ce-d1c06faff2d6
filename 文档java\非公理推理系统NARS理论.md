# 非公理推理系统（NARS）理论

## 一、NARS基本原理

### 1. 非公理推理系统概述

非公理推理系统（Non-Axiomatic Reasoning System，NARS）是一种基于经验的通用人工智能系统，由王培博士提出。NARS的核心思想是：

- **有限资源假设**：系统在有限的处理能力、存储空间和时间下运行
- **开放世界假设**：系统在开放环境中运行，持续接收新知识
- **经验驱动**：系统基于经验进行学习和推理，而非预设公理
- **目标导向**：系统行为由目标和期望驱动

NARS通过三段论推理、真值理论和预算控制等机制，实现了在有限资源下的自适应智能行为。

### 2. NARS的认知模型

NARS的认知模型包括以下关键组件：

#### 2.1 知识表示

- **术语（Term）**：表示概念、关系和操作等
- **语句（Sentence）**：包含术语、真值、时态和标记
- **任务（Task）**：包含语句和预算值
- **概念（Concept）**：围绕特定术语组织的知识单元

#### 2.2 推理机制

- **三段论推理**：演绎、归纳和渐进推理
- **时序推理**：处理时间相关的推理
- **程序性推理**：处理操作和行为的推理
- **元级推理**：系统对自身认知过程的推理

#### 2.3 控制机制

- **注意力机制**：基于预算值分配处理资源
- **记忆管理**：管理概念和任务的存储和检索
- **目标管理**：处理系统的目标和期望
- **情感机制**：评估系统状态和调整行为

### 3. NARS的语言

NARS使用Narsese语言表示知识和任务：

#### 3.1 基本语法

- **继承关系**：`<S --> P>`，表示S是P的一种
- **相似关系**：`<S <-> P>`，表示S和P相似
- **蕴含关系**：`<S ==> P>`，表示S蕴含P
- **等价关系**：`<S <=> P>`，表示S和P等价
- **合取**：`(S & P)`，表示S和P同时成立
- **析取**：`(S | P)`，表示S或P成立

#### 3.2 语句类型

- **判断（Judgment）**：`<S --> P>. %f;c%`，表示一个具有频率f和置信度c的断言
- **问题（Question）**：`<S --> P>?`，询问S和P之间的关系
- **目标（Goal）**：`<S --> P>!`，表示系统希望实现的状态
- **请求（Quest）**：`<S --> P>@`，询问如何实现特定状态

## 二、NARS的推理机制

### 1. 真值理论

NARS使用频率和置信度表示知识的不确定性：

#### 1.1 真值表示

- **频率（f）**：表示语句为真的比例，范围[0,1]
- **置信度（c）**：表示真值判断的可靠性，范围[0,1]
- **证据（w）**：置信度的基础，表示支持真值判断的证据量

#### 1.2 真值计算

NARS中的真值计算规则包括：

- **演绎**：`f = f1 * f2, c = f1 * c1 * c2`
- **归纳**：`f = f1, c = c1 * c2 / (c1 + c2 - c1 * c2)`
- **渐进**：`f = f2, c = c1 * c2 / (c1 + c2 - c1 * c2)`
- **比较**：`f = (f1 + f2) / 2, c = (c1 + c2) / 2`
- **类比**：`f = f1, c = c1 * c2`

这些计算规则确保了推理结果的真值反映了前提的不确定性。

### 2. 预算理论

NARS使用预算值控制资源分配：

#### 2.1 预算值表示

- **优先级（p）**：决定任务处理的顺序
- **耐久性（d）**：决定任务在系统中保留的时间
- **质量（q）**：反映任务的长期价值

#### 2.2 预算值计算

预算值的计算考虑多种因素：

- **真值**：真值越高，预算值越高
- **复杂度**：复杂度越高，预算值越低
- **新颖性**：新颖的内容获得更高的预算值
- **相关性**：与当前关注内容相关的任务获得更高的预算值

### 3. 三段论推理

NARS实现了多种三段论推理形式：

#### 3.1 演绎推理（Deduction）

- **前提1**：M是P
- **前提2**：S是M
- **结论**：S是P（必然）

```java
public static TruthValue deduction(TruthValue t1, TruthValue t2, Parameters narParameters) {
    float f1 = t1.getFrequency();
    float f2 = t2.getFrequency();
    float c1 = t1.getConfidence();
    float c2 = t2.getConfidence();
    float f = f1 * f2;
    float c = c1 * c2 * f1;
    return new TruthValue(f, c, narParameters);
}
```

#### 3.2 归纳推理（Induction）

- **前提1**：M是P
- **前提2**：M是S
- **结论**：S是P（可能）

```java
public static TruthValue induction(TruthValue t1, TruthValue t2, Parameters narParameters) {
    float f1 = t1.getFrequency();
    float f2 = t2.getFrequency();
    float c1 = t1.getConfidence();
    float c2 = t2.getConfidence();
    float f = f2;
    float c = c1 * c2 * f1;
    return new TruthValue(f, c, narParameters);
}
```

#### 3.3 渐进推理（Abduction）

- **前提1**：P是M
- **前提2**：S是M
- **结论**：S是P（可能）

```java
public static TruthValue abduction(TruthValue t1, TruthValue t2, Parameters narParameters) {
    float f1 = t1.getFrequency();
    float f2 = t2.getFrequency();
    float c1 = t1.getConfidence();
    float c2 = t2.getConfidence();
    float f = f1;
    float c = c1 * c2 * f2;
    return new TruthValue(f, c, narParameters);
}
```

### 4. 时序推理

NARS支持时序推理，处理事件之间的时间关系：

#### 4.1 时序关系

- **时序继承**：`<(&/, A, +n) =/> B>`，表示A发生后n个时间单位，B发生
- **时序等价**：`<(&/, A, +n) <=> B>`，表示A发生后n个时间单位等价于B发生
- **时序蕴含**：`<A ==> (&/, B, +n)>`，表示A蕴含B在n个时间单位后发生

#### 4.2 时序推理规则

- **时序演绎**：`(A =/> B), (B =/> C) |- (A =/> C)`
- **时序归纳**：`(A =/> B), (A =/> C) |- (B =/> C)`
- **时序渐进**：`(A =/> C), (B =/> C) |- (A =/> B)`

## 三、NARS的概念与记忆

### 1. 概念结构

NARS中的概念是围绕特定术语组织的知识单元：

#### 1.1 概念组成

```java
public class Concept extends Item<Term> implements Serializable {
    // 概念的唯一标识
    public Term term;
    
    // 任务链接，用于间接处理
    public Bag1<TaskLink,Task> taskLinks;
    
    // 术语链接，连接术语与其组件和复合词
    public Set<TermLink> termLinks;
    
    // 术语链接模板，用于提高术语链接构建的效率
    public List<TermLink> termLinkTemplates;
    
    // 直接询问关于该术语的待处理问题
    public List<Task> questions;
    
    // 待回答的请求
    public List<Task> quests;
    
    // 直接关于该术语的判断
    public List<Task> beliefs;
    
    // 直接关于该术语的目标
    public List<Task> desires;
}
```

#### 1.2 概念处理

概念处理包括以下步骤：

1. **任务接收**：概念接收相关的任务
2. **信念修订**：新判断与已有信念比较和修订
3. **目标处理**：处理与概念相关的目标
4. **问题回答**：尝试回答与概念相关的问题
5. **推理**：基于概念的知识进行推理
6. **链接构建**：构建与其他概念的链接

### 2. 记忆系统

NARS的记忆系统管理概念和任务：

#### 2.1 记忆结构

```java
public class Memory implements Serializable {
    // 概念包，存储所有概念
    public Bag1<Concept, Term> concepts;
    
    // 新任务缓冲区
    public Bag1<Task<Term>, Sentence<Term>> newTasks;
    
    // 操作符表
    private Map<String, Operator> operators;
    
    // 内部经验缓冲区
    private InternalExperienceBuffer internalExperienceBuffer;
}
```

#### 2.2 记忆操作

记忆系统的主要操作包括：

1. **概念检索**：根据术语检索概念
2. **概念创建**：为新术语创建概念
3. **任务处理**：处理新任务并分发到相关概念
4. **资源管理**：管理系统资源，包括概念和任务的淘汰

### 3. 注意力机制

NARS的注意力机制基于预算值控制资源分配：

#### 3.1 注意力分配

- **任务选择**：基于优先级选择任务处理
- **概念选择**：基于预算值选择概念激活
- **推理控制**：控制推理的深度和广度
- **资源回收**：回收低预算值的概念和任务

#### 3.2 注意力调整

注意力机制根据以下因素动态调整：

- **目标相关性**：与当前目标相关的内容获得更多注意力
- **新颖性**：新颖的内容获得更多注意力
- **意外性**：意外的结果获得更多注意力
- **成功经验**：成功的经验增强相关内容的注意力

## 四、NARS的情感与内部体验

### 1. 情感机制

NARS实现了基本的情感机制，用于评估系统状态：

#### 1.1 情感表示

```java
public class Emotions implements Plugin, Serializable {
    // 满意度阈值
    public volatile float HAPPY_EVENT_HIGHER_THRESHOLD = 0.75f;
    public volatile float HAPPY_EVENT_LOWER_THRESHOLD = 0.25f;
    
    // 忙碌度阈值
    public volatile float BUSY_EVENT_HIGHER_THRESHOLD = 0.9f;
    public volatile float BUSY_EVENT_LOWER_THRESHOLD = 0.1f;
    
    // 平均期望值
    private float happy;
    
    // 平均优先级
    private float busy;
}
```

#### 1.2 情感调整

情感值根据系统经验动态调整：

```java
public void adjustSatisfaction(final float newValue, final float weight, final DerivationContext nal) {
    // 更新满意度
    happy += newValue * weight;
    happy /= 1.0f + weight;
    
    // 检查是否超过阈值
    if(Math.abs(happy-lasthappy) > CHANGE_THRESHOLD && nal.time.time()-last_happy_time > CHANGE_STEPS_DEMANDED) {
        if(happy > HAPPY_EVENT_HIGHER_THRESHOLD && lasthappy <= HAPPY_EVENT_HIGHER_THRESHOLD) {
            frequency = 1.0f;
        }
        if(happy < HAPPY_EVENT_LOWER_THRESHOLD && lasthappy >= HAPPY_EVENT_LOWER_THRESHOLD) {
            frequency = 0.0f;
        }
        lasthappy = happy;
        last_happy_time = nal.time.time();
    }
    
    // 生成情感事件
    if(frequency != -1) {
        // 创建表示满意状态的语句
        final Term predicate = SetInt.make(new Term("satisfied"));
        final Term subject = Term.SELF;
        final Inheritance inh = Inheritance.make(subject, predicate);
        final TruthValue truth = new TruthValue(happy, nar.narParameters.DEFAULT_JUDGMENT_CONFIDENCE, nar.narParameters);
        
        // 创建任务并添加到系统
        final Sentence s = new Sentence(inh, Symbols.JUDGMENT_MARK, truth, new Stamp(nal.time, nar.memory));
        s.stamp.setOccurrenceTime(nal.time.time());
        final BudgetValue budgetOfNewTask = new BudgetValue(nar.narParameters.DEFAULT_JUDGMENT_PRIORITY,
                                                          nar.narParameters.DEFAULT_JUDGMENT_DURABILITY,
                                                          BudgetFunctions.truthToQuality(truth),
                                                          nar.narParameters);
        final Task t = new Task(s, budgetOfNewTask, Task.EnumType.INPUT);
        nal.addTask(t, "emotion");
    }
}
```

### 2. 内部体验

NARS通过内部体验机制实现自我监控和反思：

#### 2.1 内部体验类型

- **信念**：`<(*,SELF,<S --> P>) --> believe>`，表示系统相信S是P
- **欲望**：`<(*,SELF,<S --> P>) --> want>`，表示系统希望S是P
- **疑问**：`<(*,SELF,<S --> P>) --> wonder>`，表示系统疑惑S是否是P
- **评估**：`<(*,SELF,<S --> P>) --> evaluate>`，表示系统评估S是否是P

#### 2.2 内部体验生成

```java
public static void InternalExperienceFromTask(final Task task, final boolean includeTarget, final DerivationContext nal, boolean SucceedingEvent, boolean addToMemory) {
    if(!enabled) {
        return;
    }
    
    // 获取任务的语句和真值
    final Sentence sentence = task.sentence;
    final TruthValue truth = sentence.truth;
    
    // 根据任务类型生成不同的内部体验
    if(sentence.punctuation == Symbols.JUDGMENT_MARK) {
        if(truth.getExpectation() > nal.narParameters.DEFAULT_CONFIRMATION_EXPECTATION) {
            InternalExperienceFromTaskInternal(task, includeTarget, nal, addToMemory, "believe", SucceedingEvent);
        } else {
            InternalExperienceFromTaskInternal(task, includeTarget, nal, addToMemory, "doubt", SucceedingEvent);
        }
    } else if(sentence.punctuation == Symbols.QUESTION_MARK) {
        InternalExperienceFromTaskInternal(task, includeTarget, nal, addToMemory, "wonder", SucceedingEvent);
    } else if(sentence.punctuation == Symbols.GOAL_MARK) {
        InternalExperienceFromTaskInternal(task, includeTarget, nal, addToMemory, "want", SucceedingEvent);
    } else if(sentence.punctuation == Symbols.QUEST_MARK) {
        InternalExperienceFromTaskInternal(task, includeTarget, nal, addToMemory, "evaluate", SucceedingEvent);
    }
}
```

## 五、NARS的操作与行为

### 1. 操作表示

NARS使用操作表示系统的行为：

#### 1.1 操作结构

```java
public class Operation extends Term {
    // 操作符
    private final Operator operator;
    
    // 操作参数
    private final Term[] args;
    
    // 是否是心理操作
    private final boolean mental;
}
```

#### 1.2 操作执行

操作执行涉及以下步骤：

1. **操作识别**：识别需要执行的操作
2. **参数绑定**：绑定操作的参数
3. **执行检查**：检查操作是否可执行
4. **操作执行**：执行操作并生成结果
5. **结果反馈**：将执行结果反馈到系统

### 2. 目标处理

NARS通过目标处理机制实现目标导向行为：

#### 2.1 目标表示

目标使用特殊标记的语句表示：`<S --> P>!`

#### 2.2 目标处理流程

```java
public static void processGoal(final Concept concept, final DerivationContext nal, final Task task) {
    // 获取目标语句
    final Sentence goal = task.sentence;
    
    // 与已有目标比较和修订
    final Task oldGoalT = concept.selectCandidate(task, concept.desires, nal.time);
    if (oldGoalT != null) {
        final Sentence oldGoal = oldGoalT.sentence;
        final Stamp newStamp = goal.stamp;
        final Stamp oldStamp = oldGoal.stamp;
        if (newStamp.equals(oldStamp, false, false, true)) {
            return; // 重复目标
        }
    }
    
    // 检查目标是否已实现
    Task beliefT = null;
    if(task.aboveThreshold()) {
        for (final Task iQuest : concept.quests) {
            trySolution(task.sentence, iQuest, nal, true);
        }
        beliefT = concept.selectCandidate(task, concept.beliefs, nal.time);
    }
    
    // 处理操作目标
    if(task.sentence.term instanceof Operation) {
        Operation op = (Operation) task.sentence.term;
        if(op != null && op.getSubject() instanceof Operator) {
            executeOperation(nal, task);
            return;
        }
    }
    
    // 存储目标
    concept.desires.putIn(task);
    
    // 生成内部体验
    InternalExperience.InternalExperienceFromTask(task, false, nal, true, false);
    
    // 目标推导
    nal.memory.generatePotentialNegConfirmation(task, nal);
    if(beliefT != null) {
        trySolution(beliefT.sentence, task, nal, true);
    }
}
```

### 3. 行为选择

NARS通过行为选择机制决定系统的行为：

#### 3.1 行为候选项生成

行为候选项通过以下方式生成：

1. **目标分解**：将复杂目标分解为简单目标
2. **操作匹配**：寻找可以实现目标的操作
3. **条件检查**：检查操作的执行条件
4. **预期形成**：形成操作执行的预期结果

#### 3.2 行为选择标准

行为选择基于多种标准：

- **期望值**：行为预期实现目标的程度
- **紧急性**：目标的紧急程度
- **成本**：行为执行的资源成本
- **可行性**：行为执行的可行性

## 六、NARS与LIDA的集成

### 1. 集成架构

NARS与LIDA的集成架构包括：

#### 1.1 知识表示集成

- **术语映射**：NARS术语映射到LIDA节点
- **语句映射**：NARS语句映射到LIDA链接
- **概念映射**：NARS概念映射到LIDA节点结构
- **任务映射**：NARS任务映射到LIDA工作空间内容

#### 1.2 推理过程集成

- **NARS推理**：NARS负责逻辑推理和知识生成
- **LIDA感知**：LIDA负责感知处理和特征提取
- **LIDA注意力**：LIDA负责注意力分配和内容选择
- **LIDA行动**：LIDA负责行动选择和执行

### 2. 信息流动

NARS与LIDA之间的信息流动包括：

#### 2.1 从LIDA到NARS

1. **感知输入**：LIDA的感知内容转换为NARS的输入
2. **注意内容**：LIDA的注意内容转换为NARS的高优先级任务
3. **行动反馈**：LIDA的行动执行结果反馈给NARS

#### 2.2 从NARS到LIDA

1. **推理结果**：NARS的推理结果转换为LIDA的工作空间内容
2. **目标生成**：NARS的目标转换为LIDA的行动目标
3. **情感状态**：NARS的情感状态影响LIDA的注意力分配

### 3. 协同工作

NARS与LIDA协同工作的方式包括：

#### 3.1 感知-推理-行动循环

1. **感知**：LIDA处理感知输入并提取特征
2. **推理**：NARS基于感知内容进行推理
3. **注意**：LIDA基于推理结果分配注意力
4. **行动**：LIDA基于注意内容选择和执行行动
5. **反馈**：行动结果反馈给NARS和LIDA

#### 3.2 知识共享与学习

1. **经验学习**：LIDA的感知经验转换为NARS的知识
2. **规则学习**：NARS学习的规则指导LIDA的行动选择
3. **目标共享**：LIDA和NARS共享系统目标
4. **情感整合**：NARS的情感状态与LIDA的情感状态整合

## 七、结论

非公理推理系统（NARS）是一种基于经验的通用人工智能系统，通过三段论推理、真值理论和预算控制等机制，实现了在有限资源下的自适应智能行为。

NARS的核心特点包括：
1. 基于经验的推理，而非预设公理
2. 使用频率和置信度表示知识的不确定性
3. 使用预算值控制资源分配
4. 目标导向的行为选择

通过与LIDA认知架构的集成，NARS能够结合逻辑推理和感知处理的优势，实现更全面的认知能力。这种集成架构为构建通用人工智能系统提供了有力的理论基础和实现框架。

未来的研究方向包括：
1. 增强NARS的推理效率和准确性
2. 深化NARS与LIDA的集成
3. 扩展NARS的情感和内部体验机制
4. 应用NARS解决实际问题
