# 多位数加法可执行图式 - 当前实现版本

## 一、概述

本文档描述了当前系统中多位数加法的可执行图式实现。多位数加法是一个典型的算法示例，涉及到循环、条件判断和变量操作等基本控制结构，是测试可执行图式能力的良好案例。

当前系统的实现基于现有的图式结构模型，使用场景节点、时序边、顺承边、判断边和循环边等基本元素构建。

## 二、图式结构设计

### 1. 基本结构

多位数加法的可执行图式主要包含以下组件：

- **主题节点**：表示整个加法操作的主题
- **步骤节点**：表示加法算法的各个步骤
- **变量节点**：表示算法中使用的变量（如操作数、结果、进位等）
- **时序边**：连接主题节点和步骤节点，表示"包含步骤"的关系
- **顺承边**：连接步骤节点，表示执行顺序
- **判断边**：表示条件分支
- **循环边**：表示循环结构
- **变量边**：连接变量节点和其值

### 2. 点边结构表示

多位数加法的完整点边结构如下：

```
// 主题和步骤节点
加法计算 -[时序首]-> 初始化
加法计算 -[时序]-> 处理位循环
加法计算 -[时序]-> 处理最终进位
加法计算 -[时序]-> 返回结果

// 执行顺序
初始化 -[顺承]-> 处理位循环
处理位循环 -[顺承]-> 处理最终进位
处理最终进位 -[顺承]-> 返回结果

// 循环结构
处理位循环 -[时序]-> 获取当前位
处理位循环 -[时序]-> 计算当前位和进位
处理位循环 -[时序]-> 更新结果
处理位循环 -[时序]-> 移动到下一位

获取当前位 -[顺承]-> 计算当前位和进位
计算当前位和进位 -[顺承]-> 更新结果
更新结果 -[顺承]-> 移动到下一位
移动到下一位 -[循环条件]-> 处理位循环  // 如果还有位需要处理，继续循环

// 条件结构
处理最终进位 -[判断首]-> 添加进位  // 如果有最终进位，添加到结果
处理最终进位 -[判断]-> 跳过进位  // 如果没有最终进位，跳过

添加进位 -[顺承]-> 返回结果
跳过进位 -[顺承]-> 返回结果

// 变量绑定
加法计算 -[变量]-> 操作数1
加法计算 -[变量]-> 操作数2
加法计算 -[变量]-> 结果
加法计算 -[变量]-> 进位
加法计算 -[变量]-> 当前位索引
```

### 3. 图数据库查询表示

在当前系统中，这些结构通过图数据库查询实现。以下是关键查询示例：

```java
// 查询时序结构
String query = "match (n:场景)-[r:时序]->(m:场景)<-[r0:顺承]-(i:场景) where n.name = '" + 
               加法计算.getName() + "' and i.name = '" + 初始化.getName() + "' return r";

// 查询循环条件
query = "match (n)-[r:循环条件]->(m) where id(m) = " + 处理位循环.getNodeId() + " return r";

// 查询判断结构
query = "match (m:场景)-[r:判断首]->(i:场景) where m.name = '" + 
         处理最终进位.getName() + "' return r";

// 查询变量绑定
candidateLinks = NeoUtil.getSomeLinks(加法计算, null, null, null, "变量");
```

## 三、算法实现

### 1. 初始化阶段

初始化阶段设置必要的变量和初始状态：

```
// 伪代码
function 初始化():
    结果 = 空字符串
    进位 = 0
    当前位索引 = min(操作数1.length, 操作数2.length) - 1  // 从最低位开始
```

### 2. 处理位循环阶段

循环处理每一位的加法操作：

```
// 伪代码
function 处理位循环():
    while (当前位索引 >= 0):
        // 获取当前位
        位1 = (当前位索引 < 操作数1.length) ? 操作数1[当前位索引] : 0
        位2 = (当前位索引 < 操作数2.length) ? 操作数2[当前位索引] : 0
        
        // 计算当前位和进位
        和 = 位1 + 位2 + 进位
        当前位结果 = 和 % 10
        进位 = 和 / 10
        
        // 更新结果
        结果 = 当前位结果 + 结果  // 在结果前面添加当前位
        
        // 移动到下一位
        当前位索引 = 当前位索引 - 1
```

### 3. 处理最终进位阶段

处理计算完所有位后可能剩余的进位：

```
// 伪代码
function 处理最终进位():
    if (进位 > 0):
        结果 = 进位 + 结果  // 在结果前面添加进位
```

### 4. 返回结果阶段

返回最终计算结果：

```
// 伪代码
function 返回结果():
    return 结果
```

## 四、执行流程

多位数加法的执行流程如下：

1. **开始执行**：从加法计算主题节点开始
2. **初始化**：设置初始变量值
3. **处理位循环**：
   - 获取当前位
   - 计算当前位和进位
   - 更新结果
   - 移动到下一位
   - 如果还有位需要处理，返回循环开始
4. **处理最终进位**：
   - 如果有最终进位，添加到结果
   - 如果没有最终进位，跳过
5. **返回结果**：返回最终计算结果
6. **结束执行**

## 五、实现特点与限制

### 1. 特点

- **基于现有结构**：充分利用当前系统的图式结构模型
- **完整算法**：实现了完整的多位数加法算法
- **可扩展**：可以扩展为其他算术操作（如减法、乘法等）

### 2. 限制

- **结构复杂**：需要大量的点和边来表示相对简单的算法
- **点类型混淆**：场景节点承担了多种角色（操作、条件、数据等）
- **边类型重叠**："时序"和"顺承"边的语义有重叠
- **隐式结构**：部分结构关系通过代码逻辑隐式表示，而非显式的点边关系
- **执行效率**：由于结构复杂，执行效率可能不高

## 六、示例

### 1. 示例输入

```
操作数1 = "123"
操作数2 = "456"
```

### 2. 执行过程

1. **初始化**：
   - 结果 = ""
   - 进位 = 0
   - 当前位索引 = 2

2. **处理位循环**（第一次）：
   - 位1 = 3, 位2 = 6
   - 和 = 3 + 6 + 0 = 9
   - 当前位结果 = 9, 进位 = 0
   - 结果 = "9"
   - 当前位索引 = 1

3. **处理位循环**（第二次）：
   - 位1 = 2, 位2 = 5
   - 和 = 2 + 5 + 0 = 7
   - 当前位结果 = 7, 进位 = 0
   - 结果 = "79"
   - 当前位索引 = 0

4. **处理位循环**（第三次）：
   - 位1 = 1, 位2 = 4
   - 和 = 1 + 4 + 0 = 5
   - 当前位结果 = 5, 进位 = 0
   - 结果 = "579"
   - 当前位索引 = -1（循环结束）

5. **处理最终进位**：
   - 进位 = 0，跳过进位

6. **返回结果**：
   - 结果 = "579"

### 3. 示例输出

```
结果 = "579"
```

## 七、总结

当前系统实现的多位数加法可执行图式成功地实现了基本功能，但存在结构复杂、点边类型混淆等问题。这些问题在"图式模型设计与结构分析整合版.md"中已经被识别，并提出了相应的优化方案。

在实际应用中，这种实现方式需要大量的点和边（几百个）来表示相对简单的算法，增加了构建和维护的复杂性。优化后的版本将解决这些问题，提供更清晰、更高效的实现。
