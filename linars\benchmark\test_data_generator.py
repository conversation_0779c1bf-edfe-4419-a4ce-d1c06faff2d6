
from neo4j import GraphDatabase
import random
from faker import Faker

class TestDataGenerator:
    def __init__(self, uri, user, password):
        self.driver = GraphDatabase.driver(uri, auth=(user, password))
        self.fake = Faker()
        
    def generate_nodes(self, count):
        """生成测试节点"""
        with self.driver.session() as session:
            for i in range(count):
                name = self.fake.name()
                session.run(
                    "CREATE (n:TestNode {id: $id, name: $name, value: $value})",
                    id=i, name=name, value=random.random()
                )
                
    def generate_relationships(self, count):
        """生成测试关系"""
        with self.driver.session() as session:
            for _ in range(count):
                session.run("""
                    MATCH (a:TestNode), (b:TestNode)
                    WHERE a <> b
                    WITH a, b, rand() AS r
                    ORDER BY r
                    LIMIT 1
                    CREATE (a)-[r:TEST_REL {weight: $w}]->(b)
                    RETURN type(r)
                    """, w=random.random()
                )
    
    def generate_standard_dataset(self):
        """生成标准测试数据集"""
        print("生成10000个测试节点...")
        self.generate_nodes(10000)
        print("生成50000个测试关系...")
        self.generate_relationships(50000)

if __name__ == "__main__":
    generator = TestDataGenerator("bolt://localhost:7687", "neo4j", "password")
    generator.generate_standard_dataset()
