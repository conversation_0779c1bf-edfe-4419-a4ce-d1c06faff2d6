"""
Knowledge Graph Manager Controller

This module provides a controller for managing knowledge graphs.
"""
import os
import json
import uuid
from flask import Blueprint, request, jsonify, render_template, current_app, send_from_directory, send_file
from werkzeug.utils import secure_filename

from linars.com.warmer.kgmaker.config.web_app_config import WebAppConfig
from linars.com.warmer.kgmaker.entity.qa_entity_item import QAEntityItem
from linars.com.warmer.kgmaker.utils.response import R
from linars.com.warmer.kgmaker.utils.csv_util import CSVUtil
from linars.com.warmer.kgmaker.utils.date_util import DateUtil
from linars.com.warmer.kgmaker.utils.graph_page_record import GraphPageRecord

# 创建蓝图
kg_blueprint = Blueprint('kg', __name__, url_prefix='/')

# 全局变量
neo4j_util = None
kg_graph_service = None
config = None

# 初始化函数
def init_controller(app_neo4j_util, app_kg_graph_service, app_config):
    """
    初始化控制器

    Args:
        app_neo4j_util: Neo4j工具实例
        app_kg_graph_service: 图服务实例
        app_config: 配置实例
    """
    global neo4j_util, kg_graph_service, config
    neo4j_util = app_neo4j_util
    kg_graph_service = app_kg_graph_service
    config = app_config

# 首页路由
@kg_blueprint.route('/')
def home():
    """首页路由"""
    # 重定向到静态文件
    return render_template('kg/home.html')

@kg_blueprint.route('/2d')
def index_2d():
    """2D视图路由"""
    # 重定向到静态文件
    return render_template('kg/home.html')

@kg_blueprint.route('/3d')
def index_3d():
    """3D视图路由"""
    # 重定向到静态文件
    return render_template('kg/index.html')

# 即时通讯相关的端点
@kg_blueprint.route('/webim/getMessage', methods=['GET'])
def get_message():
    """即时通讯消息获取端点
    返回空数据，因为我们不需要即时通讯功能
    """
    return jsonify({"code": 0, "msg": "success", "data": None})

# 获取图谱数据
@kg_blueprint.route('/getgraph', methods=['GET', 'POST'])
def get_graph():
    """获取图谱数据"""
    result = R()
    result_record = GraphPageRecord()

    try:
        # 获取请求参数
        # 从POST或GET参数中获取domain
        domain = ''
        if request.method == 'POST':
            domain = request.form.get('domain', '')
        if not domain:  # 如果POST中没有，尝试从GET参数获取
            domain = request.args.get('domain', '')

        page_index = int(request.args.get('pageIndex', 1))
        page_size = int(request.args.get('pageSize', 10))

        print(f"获取图谱数据: domain={domain}, page_index={page_index}, page_size={page_size}")
        print(f"请求方法: {request.method}, 请求参数: {request.args}, 表单数据: {request.form}")

        # 创建查询对象
        class GraphQuery:
            def __init__(self):
                self.domain = domain
                self.page_index = page_index
                self.page_size = page_size
                self.node_name = None
                self.limit = None

        query_item = GraphQuery()

        # 从Neo4j获取领域列表
        cypher_sql = "CALL db.labels()"
        label_list = neo4j_util.execute_cypher_sql(cypher_sql, "GetGraphItem")

        domain_list = []
        if label_list:
            for item in label_list:
                if 'label' in item:
                    domain_list.append({
                        'name': item['label'],
                        'nodeCount': 0,  # 这些值可以通过额外查询获取
                        'shipCount': 0,
                        'status': 1
                    })

        print(f"获取到的领域列表: {domain_list}")

        # 设置分页信息
        result_record.set_page_index(page_index)
        result_record.set_page_size(page_size)
        result_record.set_total_count(len(domain_list))
        result_record.set_node_list(domain_list)

        # 获取可视化配置
        vis_config = {}
        get_config(vis_config)
        result_record.set_visbles(vis_config)

        # 设置结果
        result.code = 200
        result.set_data(result_record.to_dict())
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")
        print(f"获取图谱数据错误: {str(e)}")

    return jsonify(result.to_dict())

# 获取领域图谱
@kg_blueprint.route('/getdomaingraph', methods=['GET', 'POST'])
def get_domain_graph():
    """获取领域图谱"""
    result = R()

    try:
        # 获取请求参数
        # 从POST或GET参数中获取domain
        domain = ''
        if request.method == 'POST':
            domain = request.form.get('domain', '')
            # 如果是JSON请求，尝试从JSON中获取
            if not domain and request.is_json:
                domain = request.json.get('domain', '')

        if not domain:  # 如果POST中没有，尝试从GET参数获取
            domain = request.args.get('domain', '')

        node_name = ''
        if request.method == 'POST':
            node_name = request.form.get('nodename', '')
            if not node_name and request.is_json:
                node_name = request.json.get('nodename', '')
        if not node_name:
            node_name = request.args.get('nodeName', '')
            if not node_name:
                node_name = request.args.get('nodename', '')

        limit = request.args.get('limit', 100)
        if request.method == 'POST':
            limit_str = request.form.get('pageSize', '')
            if limit_str:
                limit = limit_str

        print(f"获取领域图谱: domain={domain}, node_name={node_name}, limit={limit}")
        print(f"请求方法: {request.method}, 请求参数: {request.args}, 表单数据: {request.form}")

        # 检查领域是否为空
        if not domain:
            print("领域为空，返回空结果")
            result.code = 200
            result.data = {"nodes": [], "relationships": []}
            return jsonify(result.to_dict())

        # 创建查询对象
        class GraphQuery:
            def __init__(self):
                self.domain = domain
                self.node_name = node_name
                self.limit = limit
                self.page_index = 1
                self.page_size = 10

        query = GraphQuery()

        # 获取图谱数据
        graph_data = kg_graph_service.get_domain_graph(query)

        # 设置结果
        result.code = 200
        result.data = graph_data
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")
        print(f"获取领域图谱错误: {str(e)}")

    return jsonify(result.to_dict())

# 执行Cypher查询
@kg_blueprint.route('/getcypherresult', methods=['GET', 'POST'])
def get_cypher_result():
    """执行Cypher查询"""
    result = R()
    error = ""

    try:
        # 获取请求参数
        cypher = request.args.get('cypher', '')

        # 执行查询
        graph_data = neo4j_util.execute_cypher_sql(cypher, "GetGraphNodeAndShip")[0]

        # 设置结果
        result.code = 200
        result.data = graph_data
    except Exception as e:
        result.code = 500
        error = str(e)
        result.set_msg(f"服务器错误: {error}")

    if error:
        result.code = 500
        result.set_msg(error)

    return jsonify(result.to_dict())

# 获取关联节点数量
@kg_blueprint.route('/getrelationnodecount', methods=['GET', 'POST'])
def get_relation_node_count():
    """获取关联节点数量"""
    result = R()

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        node_id = request.args.get('nodeid', 0)

        if domain:
            # 获取关联节点数量
            total_count = kg_graph_service.get_relation_node_count(domain, node_id)

            # 设置结果
            result.code = 200
            result.set_data(str(total_count))
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 创建领域
@kg_blueprint.route('/createdomain', methods=['GET', 'POST'])
def create_domain():
    """创建领域"""
    result = R()

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')

        if domain:
            print(f"创建领域: {domain}")
            # 检查领域是否已存在
            cypher_sql = f"MATCH (n:{domain}) RETURN count(n) as count"
            count_result = neo4j_util.execute_cypher_sql(cypher_sql, "GetGraphItem")
            domain_exists = count_result and count_result[0].get('count', 0) > 0

            if domain_exists:
                result.code = 300
                result.set_msg("领域已存在")
                print(f"领域 {domain} 已存在")
            else:
                # 保存到图数据库
                kg_graph_service.create_domain(domain)
                print(f"领域 {domain} 创建成功")

                # 设置结果
                result.code = 200
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")
        print(f"创建领域错误: {str(e)}")

    return jsonify(result.to_dict())

# 保存领域到Neo4j
def save_my_domain(domain):
    """
    保存领域到Neo4j

    Args:
        domain: 领域名称
    """
    print(f"创建领域: {domain}")
    # 直接在Neo4j中创建领域
    kg_graph_service.create_domain(domain)

# 获取标签列表
@kg_blueprint.route('/getlabellist', methods=['GET', 'POST'])
def get_label_list():
    """获取标签列表"""
    result = R()

    try:
        # 构建Cypher查询
        cypher_sql = "CALL db.labels()"
        print(f"执行查询: {cypher_sql}")

        # 执行查询
        label_list = neo4j_util.execute_cypher_sql(cypher_sql, "GetGraphItem")

        if label_list:
            # 设置结果
            result.code = 200

            # 从Neo4j直接获取标签列表
            labels = []
            for item in label_list:
                if 'label' in item:
                    labels.append(item['label'])

            # 设置数据
            result.data = labels
            print(f"获取到的标签列表: {labels}")
        else:
            result.code = 500
            result.set_msg("服务器错误")
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 获取更多关联节点
@kg_blueprint.route('/getmorerelationnode', methods=['GET', 'POST'])
def get_more_relation_node():
    """获取更多关联节点"""
    result = R()

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        node_id = request.args.get('nodeid', '')

        if domain:
            # 获取更多关联节点
            graph_model = kg_graph_service.get_more_relation_node(domain, node_id)

            if graph_model:
                # 设置结果
                result.code = 200
                result.set_data(graph_model)
                return jsonify(result.to_dict())
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 更新节点名称
@kg_blueprint.route('/updatenodename', methods=['GET', 'POST'])
def update_node_name():
    """更新节点名称"""
    result = R()
    graph_node_list = {}

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        node_id = request.args.get('nodeid', '')
        node_name = request.args.get('nodename', '')

        if domain:
            # 更新节点名称
            graph_node_list = kg_graph_service.update_node_name(domain, node_id, node_name)

            if graph_node_list:
                # 设置结果
                result.code = 200
                result.set_data(graph_node_list)
                return jsonify(result.to_dict())
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 更新节点坐标
@kg_blueprint.route('/updateCorrdOfNode', methods=['GET', 'POST'])
def update_corrd_of_node():
    """更新节点坐标"""
    result = R()

    try:
        # 获取请求参数
        # 从POST或GET参数中获取domain
        domain = ''
        if request.method == 'POST':
            domain = request.form.get('domain', '')
            # 如果是JSON请求，尝试从JSON中获取
            if not domain and request.is_json:
                domain = request.json.get('domain', '')

        if not domain:  # 如果POST中没有，尝试从GET参数获取
            domain = request.args.get('domain', '')

        node_id = request.args.get('id', '')
        fx = float(request.args.get('fx', 0))
        fy = float(request.args.get('fy', 0))

        print(f"更新节点坐标: domain={domain}, node_id={node_id}, fx={fx}, fy={fy}")
        print(f"请求方法: {request.method}, 请求参数: {request.args}, 表单数据: {request.form}")

        # 检查domain是否为空
        if not domain:
            result.code = 400
            result.set_msg("领域名称不能为空")
            print("领域名称为空，无法更新节点坐标")
            return jsonify(result.to_dict())

        # 更新节点坐标
        kg_graph_service.update_corrd_of_node(domain, node_id, fx, fy)

        # 设置结果
        result.code = 200
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")
        print(f"更新节点坐标错误: {str(e)}")

    return jsonify(result.to_dict())

# 创建节点
@kg_blueprint.route('/createnode', methods=['GET', 'POST'])
def create_node():
    """创建节点"""
    result = R()
    graph_node = {}

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        name = request.args.get('name', '')
        entity_type = int(request.args.get('entitytype', 0))

        # 创建实体
        entity = QAEntityItem(name=name, entity_type=entity_type)

        if not name:
            entity.name = "00"

        if not domain:
            domain = "test"

        # 创建节点
        graph_node = kg_graph_service.create_node(domain, entity)

        if graph_node:
            # 设置结果
            result.code = 200
            result.set_data(graph_node)
            return jsonify(result.to_dict())
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 更新节点
@kg_blueprint.route('/updatenode', methods=['GET', 'POST'])
def update_node():
    """更新节点"""
    result = R()
    graph_node = {}

    try:
        # 获取请求参数
        node_id = request.args.get('id', '')
        attrs = request.args.to_dict(flat=False)

        # 更新节点
        graph_node = kg_graph_service.update_node(node_id, attrs)

        if graph_node:
            # 设置结果
            result.code = 200
            result.set_data(graph_node)
            return jsonify(result.to_dict())
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 可视化配置目录
dir_path = "config"
save_file_path = os.path.join(dir_path, "visbles.txt")

# 设置可视化配置
@kg_blueprint.route('/makevisible', methods=['GET', 'POST'])
def make_visible():
    """设置可视化配置"""
    result = R()
    vis_config = {}

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        color = request.args.get('color', '#ff4555')
        r = request.args.get('r', '30')

        if not domain:
            domain = "test"

        # 获取配置
        get_config(vis_config)

        # 确保目录存在
        if not os.path.exists(dir_path):
            os.makedirs(dir_path)

        # 写入配置文件
        with open(save_file_path, 'w', encoding='utf-8') as writer:
            is_exist = False

            # 更新现有配置
            for key in vis_config:
                if key == domain:
                    writer.write(f"{key}_{color}_{r}\n")
                    is_exist = True
                else:
                    vis = vis_config[key]
                    writer.write(f"{key}_{vis['color']}_{vis['r']}\n")

            # 添加新配置
            if not is_exist:
                writer.write(f"{domain}_{color}_{r}\n")

        # 设置结果
        result.code = 200
        result.set_data(vis_config)
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 获取可视化配置
def get_config(vis_config):
    """
    获取可视化配置

    Args:
        vis_config: 可视化配置字典
    """
    try:
        # 确保文件存在
        if not os.path.exists(save_file_path):
            # 确保目录存在
            if not os.path.exists(dir_path):
                os.makedirs(dir_path)

            # 创建文件
            with open(save_file_path, 'w', encoding='utf-8') as f:
                pass
        else:
            # 读取配置
            with open(save_file_path, 'r', encoding='utf-8') as reader:
                for line in reader:
                    line = line.strip()
                    if line:
                        parts = line.split('_')
                        if len(parts) >= 3:
                            domain = {}
                            domain['color'] = parts[1]
                            domain['r'] = int(parts[2])
                            vis_config[parts[0]] = domain
    except Exception as e:
        print(f"Error getting config: {e}")

# 批量创建节点
@kg_blueprint.route('/batchcreatenode', methods=['GET', 'POST'])
def batch_create_node():
    """批量创建节点"""
    result = R()
    rss = {}

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        source_name = request.args.get('sourcename', '')
        target_names = request.args.getlist('targetnames[]')
        relation = request.args.get('relation', '')

        # 批量创建节点
        rss = kg_graph_service.batch_create_node(domain, source_name, relation, target_names)

        # 设置结果
        result.code = 200
        result.set_data(rss)
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 批量创建子节点
@kg_blueprint.route('/batchcreatechildnode', methods=['GET', 'POST'])
def batch_create_child_node():
    """批量创建子节点"""
    result = R()
    rss = {}

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        source_id = request.args.get('sourceid', '')
        entity_type = int(request.args.get('entitytype', 0))
        target_names = request.args.getlist('targetnames[]')
        relation = request.args.get('relation', '')

        # 批量创建子节点
        rss = kg_graph_service.batch_create_child_node(domain, source_id, entity_type, target_names, relation)

        # 设置结果
        result.code = 200
        result.set_data(rss)
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 批量创建相同节点
@kg_blueprint.route('/batchcreatesamenode', methods=['GET', 'POST'])
def batch_create_same_node():
    """批量创建相同节点"""
    result = R()
    rss = []

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        entity_type = int(request.args.get('entitytype', 0))
        source_names = request.args.getlist('sourcenames[]')

        # 批量创建相同节点
        rss = kg_graph_service.batch_create_same_node(domain, entity_type, source_names)

        # 设置结果
        result.code = 200
        result.set_data(rss)
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 创建链接
@kg_blueprint.route('/createlink', methods=['GET', 'POST'])
def create_link():
    """创建链接"""
    result = R()

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        source_id = request.args.get('sourceid', 0)
        target_id = request.args.get('targetid', 0)
        ship = request.args.get('ship', '')

        # 创建链接
        cypher_result = kg_graph_service.create_link(domain, source_id, target_id, ship)

        # 设置结果
        result.code = 200
        result.set_data(cypher_result)
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 更新链接
@kg_blueprint.route('/updatelink', methods=['GET', 'POST'])
def update_link():
    """更新链接"""
    result = R()
    cypher_result = {}

    try:
        # 获取请求参数
        ship_id = request.args.get('id', '')
        attrs = request.args.to_dict(flat=False)

        # 更新链接
        cypher_result = kg_graph_service.update_link(ship_id, attrs)

        # 设置结果
        result.code = 200
        result.set_data(cypher_result)
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 更改链接
@kg_blueprint.route('/changelink', methods=['GET', 'POST'])
def change_link():
    """更改链接"""
    result = R()
    cypher_result = {}

    try:
        # 获取请求参数
        type_name = request.args.get('type', '')
        ship_id = int(request.args.get('id', 0))

        # 更改链接
        cypher_result = kg_graph_service.change_link(type_name, ship_id)

        # 设置结果
        result.code = 200
        result.set_data(cypher_result)
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 删除节点
@kg_blueprint.route('/deletenode', methods=['GET', 'POST'])
def delete_node():
    """删除节点"""
    result = R()

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        node_id = request.args.get('nodeid', 0)

        # 删除节点
        kg_graph_service.delete_node(domain, node_id)

        # 设置结果
        result.code = 200
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 删除领域
@kg_blueprint.route('/deletedomain', methods=['GET', 'POST'])
def delete_domain():
    """删除领域"""
    result = R()

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        print(f"删除领域: {domain}")

        # 删除图数据库中的领域
        kg_graph_service.delete_kg_domain(domain)
        print(f"领域 {domain} 删除成功")

        # 设置结果
        result.code = 200
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")
        print(f"删除领域错误: {str(e)}")

    return jsonify(result.to_dict())

# 删除链接
@kg_blueprint.route('/deletelink', methods=['GET', 'POST'])
def delete_link():
    """删除链接"""
    result = R()

    try:
        # 获取请求参数
        domain = request.args.get('domain', '')
        ship_id = request.args.get('shipid', 0)

        # 删除链接
        kg_graph_service.delete_link(domain, ship_id)

        # 设置结果
        result.code = 200
    except Exception as e:
        result.code = 500
        result.set_msg(f"服务器错误: {str(e)}")

    return jsonify(result.to_dict())

# 导入图谱
@kg_blueprint.route('/importgraph', methods=['POST'])
def import_graph():
    """导入图谱"""
    result = {}

    try:
        # 获取文件
        file = request.files.get('file')
        if not file:
            result['code'] = 500
            result['msg'] = "请先选择有效的文件"
            return jsonify(result)

        # 获取领域
        domain = request.form.get('domain', '')
        if not domain:
            result['code'] = 500
            result['msg'] = "请先选择领域"
            return jsonify(result)

        # 获取数据
        data_list = get_format_data(file)

        # 创建 CSV 文件
        data = []
        for item in data_list:
            row = [
                item.get('sourcenode', ''),
                item.get('targetnode', ''),
                item.get('relationship', '')
            ]
            data.append(row)

        # 保存 CSV 文件
        save_path = WebAppConfig.get_location()
        filename = f"tc{DateUtil.get_date_now_str()}.csv"
        CSVUtil.create_csv_file(data, save_path, filename)

        # 构建 CSV URL
        server_url = request.host
        csv_url = f"http://{server_url}/kg/download/{filename}"

        # 批量插入
        kg_graph_service.batch_insert_by_csv(domain, csv_url, 0)

        # 设置结果
        result['code'] = 200
        result['message'] = "success!"
    except Exception as e:
        result['code'] = 500
        result['message'] = f"服务器错误: {str(e)}"

    return jsonify(result)

# 获取格式化数据
def get_format_data(file):
    """
    获取格式化数据

    Args:
        file: 文件

    Returns:
        格式化数据
    """
    from linars.com.warmer.kgmaker.utils.excel_util import ExcelUtil
    from linars.com.warmer.kgmaker.utils.csv_util import CSVUtil

    map_list = []

    try:
        # 获取文件名
        filename = file.filename

        # 处理 Excel 文件
        if not filename.endswith('.csv'):
            # 读取 Excel 文件
            data = ExcelUtil.read_excel_file(file)

            # 处理数据
            for i in range(1, len(data)):
                row = data[i]
                if len(row) < 3:
                    continue

                source_node = str(row[0])
                target_node = str(row[1])
                relationship = str(row[2])

                if not source_node or not target_node or not relationship:
                    continue

                map_list.append({
                    'sourcenode': source_node,
                    'targetnode': target_node,
                    'relationship': relationship
                })
        # 处理 CSV 文件
        else:
            # 读取 CSV 文件
            data = CSVUtil.read_csv_file(file)

            # 处理数据
            for i in range(len(data)):
                row = data[i]
                if len(row) < 3:
                    continue

                source_node = row[0]
                target_node = row[1]
                relationship = row[2]

                if not source_node or not target_node or not relationship:
                    continue

                map_list.append({
                    'sourcenode': source_node,
                    'targetnode': target_node,
                    'relationship': relationship
                })
    except Exception as e:
        print(f"Error getting format data: {e}")

    return map_list

# 导出图谱
@kg_blueprint.route('/exportgraph', methods=['GET', 'POST'])
def export_graph():
    """导出图谱"""
    result = {}

    try:
        # 获取领域
        domain = request.args.get('domain', '')

        # 构建文件路径
        file_path = WebAppConfig.get_location()
        filename = f"{uuid.uuid4()}.csv"
        file_url = os.path.join(file_path, filename)

        # 构建 Cypher 查询
        if not domain:
            cypher = "MATCH (n) -[r]->(m) return n.name as source, m.name as target, type(r) as relation"
        else:
            cypher = f"MATCH (n:{domain}) -[r]->(m:{domain}) return n.name as source, m.name as target, type(r) as relation"

        # 执行查询
        data = neo4j_util.execute_cypher_sql(cypher, "GetGraphItem")

        # 创建 CSV 文件
        csv_data = [["source", "target", "relation"]]
        for item in data:
            row = [
                item.get('source', '').replace('"', ''),
                item.get('target', '').replace('"', ''),
                item.get('relation', '').replace('"', '')
            ]
            csv_data.append(row)

        # 保存 CSV 文件
        CSVUtil.create_csv_file(csv_data, file_path, filename)

        # 构建 CSV URL
        server_url = request.host
        csv_url = f"{server_url}/kg/download/{filename}"

        # 设置结果
        result['code'] = 200
        result['csvurl'] = csv_url
        result['message'] = "success!"
    except Exception as e:
        result['code'] = 500
        result['message'] = f"服务器错误: {str(e)}"

    return jsonify(result)

# 文件下载
@kg_blueprint.route('/download/<filename>', methods=['GET'])
def download(filename):
    """文件下载"""
    # 构建文件路径
    file_path = WebAppConfig.get_location()

    # 返回文件
    return send_from_directory(file_path, filename, as_attachment=True, attachment_filename=f"{filename}.csv")
