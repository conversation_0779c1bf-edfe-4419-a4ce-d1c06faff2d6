"""
Knowledge Graph Service Interface

This module provides an interface for the Knowledge Graph Service.
"""
from abc import ABC, abstractmethod

class KGGraphService(ABC):
    """Knowledge Graph Service interface."""
    
    @abstractmethod
    def get_page_domain(self, query_item):
        """
        Get a page of domains.
        
        Args:
            query_item: The query parameters
            
        Returns:
            A GraphPageRecord containing the domains
        """
        pass
    
    @abstractmethod
    def delete_kg_domain(self, domain):
        """
        Delete a domain.
        
        Args:
            domain: The domain to delete
        """
        pass
    
    @abstractmethod
    def get_domain_graph(self, query):
        """
        Get the graph for a domain.
        
        Args:
            query: The query parameters
            
        Returns:
            A dictionary containing the graph data
        """
        pass
    
    @abstractmethod
    def get_domain_nodes(self, domain, page_index, page_size):
        """
        Get the nodes for a domain.
        
        Args:
            domain: The domain
            page_index: The page index
            page_size: The page size
            
        Returns:
            A dictionary containing the nodes
        """
        pass
    
    @abstractmethod
    def get_relation_node_count(self, domain, node_id):
        """
        Get the count of related nodes.
        
        Args:
            domain: The domain
            node_id: The node ID
            
        Returns:
            The count of related nodes
        """
        pass
    
    @abstractmethod
    def create_domain(self, domain):
        """
        Create a domain.
        
        Args:
            domain: The domain to create
        """
        pass
    
    @abstractmethod
    def get_more_relation_node(self, domain, node_id):
        """
        Get more related nodes.
        
        Args:
            domain: The domain
            node_id: The node ID
            
        Returns:
            A dictionary containing the related nodes
        """
        pass
    
    @abstractmethod
    def update_node_name(self, domain, node_id, node_name):
        """
        Update a node's name.
        
        Args:
            domain: The domain
            node_id: The node ID
            node_name: The new node name
            
        Returns:
            A dictionary containing the updated node
        """
        pass
    
    @abstractmethod
    def create_node(self, domain, entity):
        """
        Create a node.
        
        Args:
            domain: The domain
            entity: The entity to create
            
        Returns:
            A dictionary containing the created node
        """
        pass
    
    @abstractmethod
    def batch_create_node(self, domain, source_name, relation, target_names):
        """
        Batch create nodes.
        
        Args:
            domain: The domain
            source_name: The source node name
            relation: The relation
            target_names: The target node names
            
        Returns:
            A dictionary containing the created nodes
        """
        pass
    
    @abstractmethod
    def batch_create_child_node(self, domain, source_id, entity_type, target_names, relation):
        """
        Batch create child nodes.
        
        Args:
            domain: The domain
            source_id: The source node ID
            entity_type: The entity type
            target_names: The target node names
            relation: The relation
            
        Returns:
            A dictionary containing the created nodes
        """
        pass
    
    @abstractmethod
    def batch_create_same_node(self, domain, entity_type, source_names):
        """
        Batch create same nodes.
        
        Args:
            domain: The domain
            entity_type: The entity type
            source_names: The source node names
            
        Returns:
            A list of dictionaries containing the created nodes
        """
        pass
    
    @abstractmethod
    def create_link(self, domain, source_id, target_id, ship):
        """
        Create a link.
        
        Args:
            domain: The domain
            source_id: The source node ID
            target_id: The target node ID
            ship: The relationship
            
        Returns:
            A dictionary containing the created link
        """
        pass
    
    @abstractmethod
    def update_link(self, ship_id, attrs):
        """
        Update a link.
        
        Args:
            ship_id: The link ID
            attrs: The attributes to update
            
        Returns:
            A dictionary containing the updated link
        """
        pass
    
    @abstractmethod
    def update_node(self, node_id, attrs):
        """
        Update a node.
        
        Args:
            node_id: The node ID
            attrs: The attributes to update
            
        Returns:
            A dictionary containing the updated node
        """
        pass
    
    @abstractmethod
    def change_link(self, domain, ship_id):
        """
        Change a link.
        
        Args:
            domain: The domain
            ship_id: The link ID
            
        Returns:
            A dictionary containing the changed link
        """
        pass
    
    @abstractmethod
    def delete_node(self, domain, node_id):
        """
        Delete a node.
        
        Args:
            domain: The domain
            node_id: The node ID
        """
        pass
    
    @abstractmethod
    def delete_link(self, domain, ship_id):
        """
        Delete a link.
        
        Args:
            domain: The domain
            ship_id: The link ID
        """
        pass
    
    @abstractmethod
    def batch_insert_by_csv(self, domain, csv_url, skip_count):
        """
        Batch insert by CSV.
        
        Args:
            domain: The domain
            csv_url: The CSV URL
            skip_count: The number of rows to skip
        """
        pass
    
    @abstractmethod
    def update_node_file_status(self, domain, node_id, file_status):
        """
        Update a node's file status.
        
        Args:
            domain: The domain
            node_id: The node ID
            file_status: The file status
        """
        pass
    
    @abstractmethod
    def update_corrd_of_node(self, domain, node_id, fx, fy):
        """
        Update a node's coordinates.
        
        Args:
            domain: The domain
            node_id: The node ID
            fx: The x coordinate
            fy: The y coordinate
        """
        pass
