# 多位数加法可执行图式 - 优化图谱导入形式

## 一、概述

本文档基于"多位数加法可执行图式-图谱导入形式.md"，进一步优化图谱导入形式，使其更加符合图数据库的导入要求。主要优化包括：

1. 详细罗列点的属性，包括ID、名称、数据类型、值等
2. 不再使用独立的类型关系元组，直接在点的属性中指定类型
3. 将所有原始句形态转为点组合形式，如(处理位循环)变为（*，处理，位循环）
4. 简化关系表示，去除非必要的中间节点

## 二、点的详细定义

以下是多位数加法可执行图式中所有点的详细定义：

```
// 数据点
操作数1(id=1, name="操作数1", type="数据", data_type="string", value="123")
操作数2(id=2, name="操作数2", type="数据", data_type="string", value="456")
结果(id=3, name="结果", type="数据", data_type="string", value="")
进位(id=4, name="进位", type="数据", data_type="int", value=0)
当前位索引(id=5, name="当前位索引", type="数据", data_type="int", value=0)
位1(id=6, name="位1", type="数据", data_type="int", value=0)
位2(id=7, name="位2", type="数据", data_type="int", value=0)
当前位结果(id=8, name="当前位结果", type="数据", data_type="int", value=0)
和(id=9, name="和", type="数据", data_type="int", value=0)

// 句结构点
(id=10, name="加法计算", type="句结构", predicate="计算", args=["加法"])
(id=11, name="初始化", type="句结构", predicate="初始化", args=[])
(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"])
(id=13, name="处理最终进位", type="句结构", predicate="处理", args=["最终进位"])
(id=14, name="返回结果", type="句结构", predicate="返回", args=["结果"])

// 子操作句结构点
(id=15, name="检查操作数有效性", type="句结构", predicate="检查", args=["操作数有效性"])
(id=16, name="创建空结果字符串", type="句结构", predicate="创建", args=["空结果字符串"])
(id=17, name="设置进位为0", type="句结构", predicate="设置", args=["进位", "0"])
(id=18, name="计算初始位索引", type="句结构", predicate="计算", args=["初始位索引"])
(id=19, name="检查循环条件", type="句结构", predicate="检查", args=["循环条件"])
(id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"])
(id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"])
(id=22, name="更新结果", type="句结构", predicate="更新", args=["结果"])
(id=23, name="移动到下一位", type="句结构", predicate="移动", args=["下一位"])
(id=24, name="获取当前位索引", type="句结构", predicate="获取", args=["当前位索引"])
(id=25, name="比较与零", type="句结构", predicate="比较", args=["与零"])
(id=26, name="检查进位是否大于零", type="句结构", predicate="检查", args=["进位是否大于零"])
(id=27, name="添加进位", type="句结构", predicate="添加", args=["进位"])
(id=28, name="跳过进位", type="句结构", predicate="跳过", args=["进位"])
(id=29, name="获取结果值", type="句结构", predicate="获取", args=["结果值"])
(id=30, name="设置返回值", type="句结构", predicate="设置", args=["返回值"])

// API引用点
(id=31, name="validation/basic/check_operands_validity", type="API", category="validation/basic")
(id=32, name="string/basic/create_empty", type="API", category="string/basic")
(id=33, name="variable/basic/set_zero", type="API", category="variable/basic")
(id=34, name="math/basic/min_length_minus_one", type="API", category="math/basic")
(id=35, name="comparison/basic/greater_or_equal_than_zero", type="API", category="comparison/basic")
(id=36, name="variable/basic/get_value", type="API", category="variable/basic")
(id=37, name="comparison/basic/greater_than_zero", type="API", category="comparison/basic")
(id=38, name="string/basic/prepend_int_to_string", type="API", category="string/basic")
(id=39, name="control/basic/no_operation", type="API", category="control/basic")
(id=40, name="variable/basic/set_return_value", type="API", category="variable/basic")
```

## 三、边的定义

以下是多位数加法可执行图式中的边的定义（不再详细罗列每条边，只列出边的类型和含义）：

```
// 边的类型
时序 - 表示包含关系，连接上下文节点和子操作节点
顺承 - 表示执行顺序，连接两个操作节点
判断首 - 表示条件分支的真分支，连接条件节点和真分支节点
判断 - 表示条件分支的假分支，连接条件节点和假分支节点
循环条件 - 表示循环条件，连接循环体末尾节点和循环条件节点
数据流 - 表示数据流动，连接数据节点和操作节点
API引用 - 表示操作节点引用的API
```

## 四、多位数加法可执行图式的优化图谱导入形式

### 1. 主要结构关系

```
// 时序关系（包含关系）
(id=10, name="加法计算", type="句结构", predicate="计算", args=["加法"]) -[时序]-> (id=11, name="初始化", type="句结构", predicate="初始化", args=[])
(id=10, name="加法计算", type="句结构", predicate="计算", args=["加法"]) -[时序]-> (id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"])
(id=10, name="加法计算", type="句结构", predicate="计算", args=["加法"]) -[时序]-> (id=13, name="处理最终进位", type="句结构", predicate="处理", args=["最终进位"])
(id=10, name="加法计算", type="句结构", predicate="计算", args=["加法"]) -[时序]-> (id=14, name="返回结果", type="句结构", predicate="返回", args=["结果"])

// 顺承关系（执行顺序）
(id=11, name="初始化", type="句结构", predicate="初始化", args=[]) -[顺承]-> (id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"])
(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"]) -[顺承]-> (id=13, name="处理最终进位", type="句结构", predicate="处理", args=["最终进位"])
(id=13, name="处理最终进位", type="句结构", predicate="处理", args=["最终进位"]) -[顺承]-> (id=14, name="返回结果", type="句结构", predicate="返回", args=["结果"])
```

### 2. 初始化操作的优化图谱导入形式

```
// 时序关系（包含关系）
(id=11, name="初始化", type="句结构", predicate="初始化", args=[]) -[时序]-> (id=15, name="检查操作数有效性", type="句结构", predicate="检查", args=["操作数有效性"])
(id=11, name="初始化", type="句结构", predicate="初始化", args=[]) -[时序]-> (id=16, name="创建空结果字符串", type="句结构", predicate="创建", args=["空结果字符串"])
(id=11, name="初始化", type="句结构", predicate="初始化", args=[]) -[时序]-> (id=17, name="设置进位为0", type="句结构", predicate="设置", args=["进位", "0"])
(id=11, name="初始化", type="句结构", predicate="初始化", args=[]) -[时序]-> (id=18, name="计算初始位索引", type="句结构", predicate="计算", args=["初始位索引"])

// 顺承关系（执行顺序）
(id=15, name="检查操作数有效性", type="句结构", predicate="检查", args=["操作数有效性"]) -[顺承]-> (id=16, name="创建空结果字符串", type="句结构", predicate="创建", args=["空结果字符串"])
(id=16, name="创建空结果字符串", type="句结构", predicate="创建", args=["空结果字符串"]) -[顺承]-> (id=17, name="设置进位为0", type="句结构", predicate="设置", args=["进位", "0"])
(id=17, name="设置进位为0", type="句结构", predicate="设置", args=["进位", "0"]) -[顺承]-> (id=18, name="计算初始位索引", type="句结构", predicate="计算", args=["初始位索引"])

// API引用关系
(id=15, name="检查操作数有效性", type="句结构", predicate="检查", args=["操作数有效性"]) -[API引用]-> (id=31, name="validation/basic/check_operands_validity", type="API", category="validation/basic")
(id=16, name="创建空结果字符串", type="句结构", predicate="创建", args=["空结果字符串"]) -[API引用]-> (id=32, name="string/basic/create_empty", type="API", category="string/basic")
(id=17, name="设置进位为0", type="句结构", predicate="设置", args=["进位", "0"]) -[API引用]-> (id=33, name="variable/basic/set_zero", type="API", category="variable/basic")
(id=18, name="计算初始位索引", type="句结构", predicate="计算", args=["初始位索引"]) -[API引用]-> (id=34, name="math/basic/min_length_minus_one", type="API", category="math/basic")

// 数据流关系
(id=1, name="操作数1", type="数据", data_type="string", value="123") -[数据流]-> (id=15, name="检查操作数有效性", type="句结构", predicate="检查", args=["操作数有效性"])
(id=2, name="操作数2", type="数据", data_type="string", value="456") -[数据流]-> (id=15, name="检查操作数有效性", type="句结构", predicate="检查", args=["操作数有效性"])
(id=16, name="创建空结果字符串", type="句结构", predicate="创建", args=["空结果字符串"]) -[数据流]-> (id=3, name="结果", type="数据", data_type="string", value="")
(id=17, name="设置进位为0", type="句结构", predicate="设置", args=["进位", "0"]) -[数据流]-> (id=4, name="进位", type="数据", data_type="int", value=0)
(id=1, name="操作数1", type="数据", data_type="string", value="123") -[数据流]-> (id=18, name="计算初始位索引", type="句结构", predicate="计算", args=["初始位索引"])
(id=2, name="操作数2", type="数据", data_type="string", value="456") -[数据流]-> (id=18, name="计算初始位索引", type="句结构", predicate="计算", args=["初始位索引"])
(id=18, name="计算初始位索引", type="句结构", predicate="计算", args=["初始位索引"]) -[数据流]-> (id=5, name="当前位索引", type="数据", data_type="int", value=0)
```

### 3. 处理位循环的优化图谱导入形式

```
// 时序关系（包含关系）
(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"]) -[时序]-> (id=19, name="检查循环条件", type="句结构", predicate="检查", args=["循环条件"])
(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"]) -[时序]-> (id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"])
(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"]) -[时序]-> (id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"])
(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"]) -[时序]-> (id=22, name="更新结果", type="句结构", predicate="更新", args=["结果"])
(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"]) -[时序]-> (id=23, name="移动到下一位", type="句结构", predicate="移动", args=["下一位"])

// 检查循环条件的子图式
(id=19, name="检查循环条件", type="句结构", predicate="检查", args=["循环条件"]) -[时序]-> (id=24, name="获取当前位索引", type="句结构", predicate="获取", args=["当前位索引"])
(id=19, name="检查循环条件", type="句结构", predicate="检查", args=["循环条件"]) -[时序]-> (id=25, name="比较与零", type="句结构", predicate="比较", args=["与零"])

// 顺承关系（执行顺序）
(id=19, name="检查循环条件", type="句结构", predicate="检查", args=["循环条件"]) -[顺承]-> (id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"])
(id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"]) -[顺承]-> (id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"])
(id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"]) -[顺承]-> (id=22, name="更新结果", type="句结构", predicate="更新", args=["结果"])
(id=22, name="更新结果", type="句结构", predicate="更新", args=["结果"]) -[顺承]-> (id=23, name="移动到下一位", type="句结构", predicate="移动", args=["下一位"])
(id=24, name="获取当前位索引", type="句结构", predicate="获取", args=["当前位索引"]) -[顺承]-> (id=25, name="比较与零", type="句结构", predicate="比较", args=["与零"])

// 循环关系
(id=23, name="移动到下一位", type="句结构", predicate="移动", args=["下一位"]) -[循环条件]-> (id=19, name="检查循环条件", type="句结构", predicate="检查", args=["循环条件"])

// API引用关系
(id=24, name="获取当前位索引", type="句结构", predicate="获取", args=["当前位索引"]) -[API引用]-> (id=36, name="variable/basic/get_value", type="API", category="variable/basic")
(id=25, name="比较与零", type="句结构", predicate="比较", args=["与零"]) -[API引用]-> (id=35, name="comparison/basic/greater_or_equal_than_zero", type="API", category="comparison/basic")

// 数据流关系
(id=5, name="当前位索引", type="数据", data_type="int", value=0) -[数据流]-> (id=24, name="获取当前位索引", type="句结构", predicate="获取", args=["当前位索引"])
(id=24, name="获取当前位索引", type="句结构", predicate="获取", args=["当前位索引"]) -[数据流]-> (id=25, name="比较与零", type="句结构", predicate="比较", args=["与零"])
(id=1, name="操作数1", type="数据", data_type="string", value="123") -[数据流]-> (id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"])
(id=2, name="操作数2", type="数据", data_type="string", value="456") -[数据流]-> (id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"])
(id=5, name="当前位索引", type="数据", data_type="int", value=0) -[数据流]-> (id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"])
(id=20, name="获取当前位", type="句结构", predicate="获取", args=["当前位"]) -[数据流]-> (id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"])
(id=4, name="进位", type="数据", data_type="int", value=0) -[数据流]-> (id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"])
(id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"]) -[数据流]-> (id=4, name="进位", type="数据", data_type="int", value=0)
(id=21, name="计算当前位和进位", type="句结构", predicate="计算", args=["当前位", "进位"]) -[数据流]-> (id=22, name="更新结果", type="句结构", predicate="更新", args=["结果"])
(id=3, name="结果", type="数据", data_type="string", value="") -[数据流]-> (id=22, name="更新结果", type="句结构", predicate="更新", args=["结果"])
(id=22, name="更新结果", type="句结构", predicate="更新", args=["结果"]) -[数据流]-> (id=3, name="结果", type="数据", data_type="string", value="")
(id=5, name="当前位索引", type="数据", data_type="int", value=0) -[数据流]-> (id=23, name="移动到下一位", type="句结构", predicate="移动", args=["下一位"])
(id=23, name="移动到下一位", type="句结构", predicate="移动", args=["下一位"]) -[数据流]-> (id=5, name="当前位索引", type="数据", data_type="int", value=0)
```

### 4. 处理最终进位的优化图谱导入形式

```
// 时序关系（包含关系）
(id=13, name="处理最终进位", type="句结构", predicate="处理", args=["最终进位"]) -[时序]-> (id=26, name="检查进位是否大于零", type="句结构", predicate="检查", args=["进位是否大于零"])
(id=13, name="处理最终进位", type="句结构", predicate="处理", args=["最终进位"]) -[时序]-> (id=27, name="添加进位", type="句结构", predicate="添加", args=["进位"])
(id=13, name="处理最终进位", type="句结构", predicate="处理", args=["最终进位"]) -[时序]-> (id=28, name="跳过进位", type="句结构", predicate="跳过", args=["进位"])

// 判断关系
(id=26, name="检查进位是否大于零", type="句结构", predicate="检查", args=["进位是否大于零"]) -[判断首]-> (id=27, name="添加进位", type="句结构", predicate="添加", args=["进位"])
(id=26, name="检查进位是否大于零", type="句结构", predicate="检查", args=["进位是否大于零"]) -[判断]-> (id=28, name="跳过进位", type="句结构", predicate="跳过", args=["进位"])

// API引用关系
(id=26, name="检查进位是否大于零", type="句结构", predicate="检查", args=["进位是否大于零"]) -[API引用]-> (id=37, name="comparison/basic/greater_than_zero", type="API", category="comparison/basic")
(id=27, name="添加进位", type="句结构", predicate="添加", args=["进位"]) -[API引用]-> (id=38, name="string/basic/prepend_int_to_string", type="API", category="string/basic")
(id=28, name="跳过进位", type="句结构", predicate="跳过", args=["进位"]) -[API引用]-> (id=39, name="control/basic/no_operation", type="API", category="control/basic")

// 数据流关系
(id=4, name="进位", type="数据", data_type="int", value=0) -[数据流]-> (id=26, name="检查进位是否大于零", type="句结构", predicate="检查", args=["进位是否大于零"])
(id=4, name="进位", type="数据", data_type="int", value=0) -[数据流]-> (id=27, name="添加进位", type="句结构", predicate="添加", args=["进位"])
(id=3, name="结果", type="数据", data_type="string", value="") -[数据流]-> (id=27, name="添加进位", type="句结构", predicate="添加", args=["进位"])
(id=27, name="添加进位", type="句结构", predicate="添加", args=["进位"]) -[数据流]-> (id=3, name="结果", type="数据", data_type="string", value="")
```

### 5. 返回结果的优化图谱导入形式

```
// 时序关系（包含关系）
(id=14, name="返回结果", type="句结构", predicate="返回", args=["结果"]) -[时序]-> (id=29, name="获取结果值", type="句结构", predicate="获取", args=["结果值"])
(id=14, name="返回结果", type="句结构", predicate="返回", args=["结果"]) -[时序]-> (id=30, name="设置返回值", type="句结构", predicate="设置", args=["返回值"])

// 顺承关系（执行顺序）
(id=29, name="获取结果值", type="句结构", predicate="获取", args=["结果值"]) -[顺承]-> (id=30, name="设置返回值", type="句结构", predicate="设置", args=["返回值"])

// API引用关系
(id=29, name="获取结果值", type="句结构", predicate="获取", args=["结果值"]) -[API引用]-> (id=36, name="variable/basic/get_value", type="API", category="variable/basic")
(id=30, name="设置返回值", type="句结构", predicate="设置", args=["返回值"]) -[API引用]-> (id=40, name="variable/basic/set_return_value", type="API", category="variable/basic")

// 数据流关系
(id=3, name="结果", type="数据", data_type="string", value="") -[数据流]-> (id=29, name="获取结果值", type="句结构", predicate="获取", args=["结果值"])
(id=29, name="获取结果值", type="句结构", predicate="获取", args=["结果值"]) -[数据流]-> (id=30, name="设置返回值", type="句结构", predicate="设置", args=["返回值"])
```

## 五、总结

本文档对多位数加法可执行图式的图谱导入形式进行了优化，主要包括：

1. **详细的点属性**：为每个点添加了详细的属性，包括ID、名称、类型、数据类型、值等，使得点的信息更加完整。

2. **句结构点表示**：将原始句形态转为点组合形式，如(处理位循环)变为(id=12, name="处理位循环", type="句结构", predicate="处理", args=["位循环"])，使得句结构更加清晰。

3. **简化关系表示**：去除了非必要的中间节点，直接使用边连接相关节点，使得图谱结构更加简洁。

4. **统一的API引用**：使用API引用边直接连接操作节点和API节点，使得API调用关系更加清晰。

这种优化后的图谱导入形式更加符合图数据库的导入要求，可以直接导入到图数据库中进行存储、查询和分析。同时，这种形式也更加符合图谱的表示习惯，使得图谱结构更加清晰、直观。
