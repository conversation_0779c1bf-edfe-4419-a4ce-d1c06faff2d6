# 图式激活扩散机制优化方案 - 第二部分：任务优先级管理

> 注：本文档是图式激活扩散机制优化方案的第二部分，主要关注任务优先级管理。相关内容请参考[第一部分：深度与广度控制](./图式激活扩散优化-第一部分.md)和[第三部分：任务队列与资源分配优化](./图式激活扩散优化-第三部分.md)。

## 一、当前任务优先级管理分析

### 1. 现有实现机制

通过代码分析，当前系统的任务优先级管理主要体现在以下几个方面：

1. Bag队列实现：
```java
public class Bag1<V extends Item<K>, K> implements Serializable, Iterable<V> {
    public final MinMaxPriorityQueue<V> queue;
    public final Map<String,V> theMap;

    public Bag1(int maxSize) {
        this.maxSize = maxSize;
        theMap = new HashMap<>();
        // 优先级队列，默认升序
        queue = MinMaxPriorityQueue
                .orderedBy(Comparator.comparing(V::getPriority))
                .create();
    }

    public V putIn(V item) {
        synchronized (queue) {
            // 加到队列尾部
            queue.add(item);
            theMap.put(item.name().toString(), item);
            return null;
        }
    }
}
```

2. 任务提交机制：
```java
public void submitTask(ActivationTask task) {
    activeTaskCount.incrementAndGet();
    executorService.execute(() -> {
        try {
            // 执行任务
            task.run();
        } finally {
            activeTaskCount.decrementAndGet();
        }
    });
}
```

3. 简单的优先级计算：
```java
// 创建激活任务
ActivationTask task = new ActivationTask(
        node,
        null,
        initialEnergy,
        100, // 初始词的优先级最高
        taskManager
);
```

### 2. 存在的问题

当前任务优先级管理存在以下问题：

1. 优先级粒度粗糙：大多数任务使用固定优先级，没有细致的区分
2. 先进先出问题：虽然使用优先级队列，但实际上很多任务优先级相同，导致先进先出
3. 缺乏动态调整：任务优先级一旦设定就不再变化，无法根据系统状态动态调整
4. 资源竞争：重要任务可能被大量低优先级任务阻塞
5. 饥饿问题：低优先级但重要的任务可能长时间得不到执行

## 二、优化目标

针对上述问题，我们的优化目标是：

1. 实现细粒度的任务优先级计算，考虑多种因素
2. 引入动态优先级调整机制，根据系统状态和等待时间调整优先级
3. 实现任务分类和分组，确保各类任务都能得到合理的资源分配
4. 防止任务饥饿，确保低优先级任务最终能够执行
5. 优化队列管理，提高任务调度效率

## 三、优先级计算优化

### 1. 多因素优先级计算

实现考虑多种因素的细粒度优先级计算：

```java
public int calculateTaskPriority(Node node, Link link, String from, double energy) {
    // 基础优先级 (0-100)
    int basePriority = 50;

    // 1. 节点因素 (±20)
    basePriority += calculateNodePriorityFactor(node);

    // 2. 连接因素 (±15)
    if (link != null) {
        basePriority += calculateLinkPriorityFactor(link);
    }

    // 3. 来源因素 (±20)
    basePriority += calculateSourcePriorityFactor(from);

    // 4. 能量因素 (±10)
    basePriority += calculateEnergyPriorityFactor(energy);

    // 5. 上下文因素 (±15)
    basePriority += calculateContextPriorityFactor(node, from);

    // 确保优先级在有效范围内 (0-100)
    return Math.max(0, Math.min(100, basePriority));
}
```

### 2. 节点优先级因素

根据节点类型和属性计算优先级调整：

```java
private int calculateNodePriorityFactor(Node node) {
    int factor = 0;

    // 节点类型因素
    String nodeType = getNodeType(node);
    switch (nodeType) {
        case "场景":
            factor += 15;  // 场景节点优先级高
            break;
        case "动作":
            factor += 10;  // 动作节点优先级较高
            break;
        case "概念":
            factor += 5;   // 概念节点优先级中等
            break;
    }

    // 节点激活因素
    double activation = node.getActivation();
    factor += (int)(activation * 10);  // 激活值越高优先级越高

    // 节点重要性因素
    if (isImportantNode(node)) {
        factor += 10;  // 重要节点优先级提升
    }

    // 节点频率因素
    if (isFrequentlyUsedNode(node)) {
        factor += 5;   // 常用节点优先级提升
    }

    return factor;
}
```

### 3. 连接优先级因素

根据连接类型和属性计算优先级调整：

```java
private int calculateLinkPriorityFactor(Link link) {
    int factor = 0;

    // 连接类型因素
    String linkType = link.getCategory().getName();
    switch (linkType) {
        case "顺承":
        case "时序":
            factor += 12;  // 时序关系优先级高
            break;
        case "arg0":
        case "arg1":
            factor += 10;  // 核心参数关系优先级较高
            break;
        case "语序":
        case "顺接":
            factor += 8;   // 语法关系优先级中等
            break;
        case "相似":
        case "对等":
            factor += 5;   // 语义关系优先级较低
            break;
    }

    // 连接权重因素
    double weight = link.getWeight();
    factor += (int)(weight * 5);  // 权重越高优先级越高

    // 连接新鲜度因素
    if (isRecentlyCreatedLink(link)) {
        factor += 3;  // 新创建的连接优先级提升
    }

    return factor;
}
```

### 4. 来源优先级因素

根据激活来源计算优先级调整：

```java
private int calculateSourcePriorityFactor(String from) {
    int factor = 0;

    // 来源类型因素
    switch (from) {
        case "listen":
        case "see":
            factor += 15;  // 感知输入优先级高
            break;
        case "varwantplan":
            factor += 18;  // 计划相关优先级最高
            break;
        case "varmindplan":
            factor += 12;  // 思维计划优先级较高
            break;
        case "pam":
            factor += 8;   // 一般激活优先级中等
            break;
        case "nar":
        case "think":
            factor += 5;   // 思考激活优先级较低
            break;
    }

    // 当前系统状态因素
    if (isInFocusedMode() && (from.equals("listen") || from.equals("see"))) {
        factor += 5;  // 专注模式下感知输入优先级进一步提升
    }

    return factor;
}
```

### 5. 能量优先级因素

根据激活能量计算优先级调整：

```java
private int calculateEnergyPriorityFactor(double energy) {
    // 能量值归一化到0-10范围
    double normalizedEnergy = Math.min(1.0, energy / MAX_ENERGY) * 10;

    // 能量值越高优先级越高
    return (int)normalizedEnergy;
}
```

### 6. 上下文优先级因素

根据当前系统上下文计算优先级调整：

```java
private int calculateContextPriorityFactor(Node node, String from) {
    int factor = 0;

    // 与当前任务相关性因素
    if (isRelatedToCurrentTask(node)) {
        factor += 10;  // 与当前任务相关的节点优先级提升
    }

    // 与当前对话主题相关性因素
    if (isRelatedToCurrentTopic(node)) {
        factor += 5;   // 与当前对话主题相关的节点优先级提升
    }

    // 系统负载因素
    if (isSystemOverloaded() && !isHighPrioritySource(from)) {
        factor -= 5;   // 系统过载时非高优先级来源的任务优先级降低
    }

    return factor;
}
```

## 四、动态优先级调整

### 1. 等待时间补偿

实现基于等待时间的优先级补偿，防止低优先级任务饥饿：

```java
public class DynamicPriorityManager {
    // 任务等待时间映射
    private final Map<String, Long> taskWaitingTimes = new ConcurrentHashMap<>();
    // 最大等待时间补偿
    private final int MAX_WAITING_COMPENSATION = 30;
    // 等待时间单位（毫秒）
    private final long WAITING_TIME_UNIT = 1000;

    public void recordTaskCreation(String taskId) {
        taskWaitingTimes.put(taskId, System.currentTimeMillis());
    }

    public void recordTaskCompletion(String taskId) {
        taskWaitingTimes.remove(taskId);
    }

    public int calculateWaitingCompensation(String taskId) {
        Long creationTime = taskWaitingTimes.get(taskId);
        if (creationTime == null) {
            return 0;
        }

        // 计算等待时间（秒）
        long waitingTime = (System.currentTimeMillis() - creationTime) / WAITING_TIME_UNIT;

        // 计算补偿值（最大30）
        return (int)Math.min(MAX_WAITING_COMPENSATION, waitingTime);
    }

    public int adjustPriorityForWaiting(int basePriority, String taskId) {
        int compensation = calculateWaitingCompensation(taskId);
        return Math.min(100, basePriority + compensation);
    }
}
```

### 2. 系统负载调整

根据系统负载动态调整优先级策略：

```java
public class SystemLoadManager {
    // 系统负载阈值
    private final double HIGH_LOAD_THRESHOLD = 0.8;
    private final double MEDIUM_LOAD_THRESHOLD = 0.5;

    // 当前活跃任务数
    private final AtomicInteger activeTaskCount = new AtomicInteger(0);
    // 最大任务容量
    private final int maxTaskCapacity;

    public SystemLoadManager(int maxTaskCapacity) {
        this.maxTaskCapacity = maxTaskCapacity;
    }

    public void incrementActiveTaskCount() {
        activeTaskCount.incrementAndGet();
    }

    public void decrementActiveTaskCount() {
        activeTaskCount.decrementAndGet();
    }

    public double getSystemLoad() {
        return (double)activeTaskCount.get() / maxTaskCapacity;
    }

    public boolean isSystemOverloaded() {
        return getSystemLoad() > HIGH_LOAD_THRESHOLD;
    }

    public int getLoadAdjustmentFactor() {
        double load = getSystemLoad();
        if (load > HIGH_LOAD_THRESHOLD) {
            return 15;  // 高负载时优先级差异加大
        } else if (load > MEDIUM_LOAD_THRESHOLD) {
            return 10;  // 中等负载时优先级差异适中
        } else {
            return 5;   // 低负载时优先级差异减小
        }
    }

    public int adjustPriorityForLoad(int basePriority, boolean isHighPriority) {
        int adjustmentFactor = getLoadAdjustmentFactor();
        if (isHighPriority) {
            return Math.min(100, basePriority + adjustmentFactor);
        } else {
            return Math.max(0, basePriority - adjustmentFactor);
        }
    }
}
```

### 3. 优先级衰减机制

实现优先级随时间衰减的机制，确保新任务能够得到处理：

```java
public class PriorityDecayManager {
    // 优先级衰减间隔（毫秒）
    private final long DECAY_INTERVAL = 5000;
    // 优先级衰减率
    private final double DECAY_RATE = 0.95;

    // 任务优先级映射
    private final Map<String, Double> taskPriorities = new ConcurrentHashMap<>();
    // 上次衰减时间
    private long lastDecayTime = System.currentTimeMillis();

    public void recordTaskPriority(String taskId, int priority) {
        taskPriorities.put(taskId, (double)priority);
    }

    public void removeTask(String taskId) {
        taskPriorities.remove(taskId);
    }

    public void decayPriorities() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastDecayTime < DECAY_INTERVAL) {
            return;  // 未到衰减时间
        }

        // 对所有任务优先级进行衰减
        for (Map.Entry<String, Double> entry : taskPriorities.entrySet()) {
            double newPriority = entry.getValue() * DECAY_RATE;
            taskPriorities.put(entry.getKey(), newPriority);
        }

        lastDecayTime = currentTime;
    }

    public int getDecayedPriority(String taskId, int originalPriority) {
        Double currentPriority = taskPriorities.get(taskId);
        if (currentPriority == null) {
            return originalPriority;
        }
        return (int)Math.round(currentPriority);
    }
}
```

## 五、任务分类与分组

### 1. 任务分类机制

实现任务分类，为不同类型的任务分配资源：

```java
public class TaskClassifier {
    // 任务类型枚举
    public enum TaskType {
        PERCEPTION,      // 感知任务
        PLANNING,        // 计划任务
        REASONING,       // 推理任务
        LANGUAGE,        // 语言处理任务
        MAINTENANCE      // 维护任务
    }

    public TaskType classifyTask(Node node, String from) {
        // 根据来源分类
        switch (from) {
            case "listen":
            case "see":
            case "touch":
            case "smell":
            case "taste":
            case "feel":
                return TaskType.PERCEPTION;

            case "varwantplan":
            case "varmindplan":
                return TaskType.PLANNING;

            case "nar":
            case "think":
                return TaskType.REASONING;
        }

        // 根据节点类型分类
        String nodeType = getNodeType(node);
        if (nodeType.equals("场景")) {
            return TaskType.PLANNING;
        } else if (nodeType.equals("语法") || nodeType.equals("语义")) {
            return TaskType.LANGUAGE;
        }

        // 默认为维护任务
        return TaskType.MAINTENANCE;
    }
}
```

### 2. 资源分配策略

为不同类型的任务分配资源配额：

```java
public class ResourceAllocationManager {
    // 任务类型资源配额（百分比）
    private final Map<TaskClassifier.TaskType, Integer> resourceQuotas = new HashMap<>();
    // 任务类型当前使用资源
    private final Map<TaskClassifier.TaskType, AtomicInteger> resourceUsage = new HashMap<>();
    // 总资源容量
    private final int totalCapacity;

    public ResourceAllocationManager(int totalCapacity) {
        this.totalCapacity = totalCapacity;

        // 设置默认资源配额
        resourceQuotas.put(TaskClassifier.TaskType.PERCEPTION, 30);    // 30%
        resourceQuotas.put(TaskClassifier.TaskType.PLANNING, 25);      // 25%
        resourceQuotas.put(TaskClassifier.TaskType.REASONING, 20);     // 20%
        resourceQuotas.put(TaskClassifier.TaskType.LANGUAGE, 15);      // 15%
        resourceQuotas.put(TaskClassifier.TaskType.MAINTENANCE, 10);   // 10%

        // 初始化资源使用计数
        for (TaskClassifier.TaskType type : TaskClassifier.TaskType.values()) {
            resourceUsage.put(type, new AtomicInteger(0));
        }
    }

    public boolean canAllocateResource(TaskClassifier.TaskType taskType) {
        // 计算当前类型的资源配额
        int quota = (int)(totalCapacity * resourceQuotas.get(taskType) / 100.0);

        // 检查是否超过配额
        return resourceUsage.get(taskType).get() < quota;
    }

    public void allocateResource(TaskClassifier.TaskType taskType) {
        resourceUsage.get(taskType).incrementAndGet();
    }

    public void releaseResource(TaskClassifier.TaskType taskType) {
        resourceUsage.get(taskType).decrementAndGet();
    }

    public void adjustQuotas(Map<TaskClassifier.TaskType, Integer> newQuotas) {
        // 验证配额总和为100%
        int sum = newQuotas.values().stream().mapToInt(Integer::intValue).sum();
        if (sum != 100) {
            throw new IllegalArgumentException("Resource quotas must sum to 100%");
        }

        // 更新配额
        resourceQuotas.putAll(newQuotas);
    }
}
```

### 3. 任务组管理

实现任务组管理，确保相关任务一起执行：

```java
public class TaskGroupManager {
    // 任务组映射
    private final Map<String, TaskGroup> taskGroups = new ConcurrentHashMap<>();

    public TaskGroup getOrCreateTaskGroup(String groupId) {
        return taskGroups.computeIfAbsent(groupId, id -> new TaskGroup(id));
    }

    public TaskGroup getTaskGroup(String groupId) {
        return taskGroups.get(groupId);
    }

    public void removeTaskGroup(String groupId) {
        taskGroups.remove(groupId);
    }

    public List<TaskGroup> getAllTaskGroups() {
        return new ArrayList<>(taskGroups.values());
    }
}

public class TaskGroup {
    private final String groupId;
    private final List<ActivationTask> tasks = new ArrayList<>();
    private int groupPriority = 0;

    public TaskGroup(String groupId) {
        this.groupId = groupId;
    }

    public void addTask(ActivationTask task) {
        tasks.add(task);
        // 更新组优先级为最高任务优先级
        groupPriority = Math.max(groupPriority, task.getPriority());
    }

    public List<ActivationTask> getTasks() {
        return new ArrayList<>(tasks);
    }

    public int getGroupPriority() {
        return groupPriority;
    }

    public void setGroupPriority(int priority) {
        this.groupPriority = priority;
    }

    public String getGroupId() {
        return groupId;
    }
}
```

## 六、实现建议

### 1. 改进的任务提交方法

整合上述优化，实现改进的任务提交方法：

```java
public void submitActivationTask(Node node, Link link, double energy, String from) {
    // 生成任务ID
    String taskId = generateTaskId(node, link);

    // 计算基础优先级
    int basePriority = calculateTaskPriority(node, link, from, energy);

    // 分类任务
    TaskClassifier.TaskType taskType = taskClassifier.classifyTask(node, from);

    // 检查资源配额
    if (!resourceAllocationManager.canAllocateResource(taskType)) {
        // 资源不足，将任务放入等待队列
        waitingTaskQueue.add(new WaitingTask(node, link, energy, from, taskId, basePriority, taskType));
        return;
    }

    // 动态调整优先级
    int adjustedPriority = basePriority;

    // 1. 等待时间补偿
    adjustedPriority = dynamicPriorityManager.adjustPriorityForWaiting(adjustedPriority, taskId);

    // 2. 系统负载调整
    boolean isHighPriority = basePriority > 70;
    adjustedPriority = systemLoadManager.adjustPriorityForLoad(adjustedPriority, isHighPriority);

    // 3. 优先级衰减
    adjustedPriority = priorityDecayManager.getDecayedPriority(taskId, adjustedPriority);

    // 创建激活任务
    ActivationTask task = new ActivationTask(
            node,
            link,
            energy,
            adjustedPriority,
            this,
            taskId,
            taskType
    );

    // 记录任务信息
    dynamicPriorityManager.recordTaskCreation(taskId);
    priorityDecayManager.recordTaskPriority(taskId, adjustedPriority);
    systemLoadManager.incrementActiveTaskCount();
    resourceAllocationManager.allocateResource(taskType);

    // 确定任务组
    String groupId = determineTaskGroup(node, from);
    TaskGroup taskGroup = taskGroupManager.getOrCreateTaskGroup(groupId);
    taskGroup.addTask(task);

    // 提交任务到执行器
    executorService.execute(() -> {
        try {
            // 执行任务
            task.run();
        } finally {
            // 任务完成后清理
            dynamicPriorityManager.recordTaskCompletion(taskId);
            priorityDecayManager.removeTask(taskId);
            systemLoadManager.decrementActiveTaskCount();
            resourceAllocationManager.releaseResource(taskType);

            // 处理等待队列
            processWaitingTasks();
        }
    });
}
```

### 2. 等待队列处理

实现等待队列处理，确保资源可用时执行等待的任务：

```java
private void processWaitingTasks() {
    // 遍历所有任务类型
    for (TaskClassifier.TaskType taskType : TaskClassifier.TaskType.values()) {
        // 检查资源是否可用
        if (!resourceAllocationManager.canAllocateResource(taskType)) {
            continue;  // 资源不足，跳过此类型
        }

        // 获取此类型的等待任务
        List<WaitingTask> typeTasks = waitingTaskQueue.stream()
                .filter(task -> task.getTaskType() == taskType)
                .collect(Collectors.toList());

        if (typeTasks.isEmpty()) {
            continue;  // 无等待任务，跳过
        }

        // 按优先级排序
        typeTasks.sort((a, b) -> Integer.compare(b.getPriority(), a.getPriority()));

        // 取出最高优先级任务
        WaitingTask highestPriorityTask = typeTasks.get(0);
        waitingTaskQueue.remove(highestPriorityTask);

        // 重新提交任务
        submitActivationTask(
                highestPriorityTask.getNode(),
                highestPriorityTask.getLink(),
                highestPriorityTask.getEnergy(),
                highestPriorityTask.getFrom()
        );
    }
}
```

### 3. 优先级监控与调整

实现优先级监控和动态调整机制：

```java
public class PriorityMonitor {
    // 任务类型执行统计
    private final Map<TaskClassifier.TaskType, Integer> executionCounts = new HashMap<>();
    // 上次调整时间
    private long lastAdjustmentTime = System.currentTimeMillis();
    // 调整间隔（毫秒）
    private final long ADJUSTMENT_INTERVAL = 10000;  // 10秒

    public void recordTaskExecution(TaskClassifier.TaskType taskType) {
        executionCounts.compute(taskType, (k, v) -> (v == null ? 0 : v) + 1);
    }

    public void adjustResourceQuotas(ResourceAllocationManager resourceManager) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastAdjustmentTime < ADJUSTMENT_INTERVAL) {
            return;  // 未到调整时间
        }

        // 计算总执行次数
        int totalExecutions = executionCounts.values().stream().mapToInt(Integer::intValue).sum();
        if (totalExecutions == 0) {
            return;  // 无执行记录，不调整
        }

        // 计算新配额
        Map<TaskClassifier.TaskType, Integer> newQuotas = new HashMap<>();
        int remainingQuota = 100;

        // 确保每种类型至少有5%的配额
        for (TaskClassifier.TaskType type : TaskClassifier.TaskType.values()) {
            newQuotas.put(type, 5);
            remainingQuota -= 5;
        }

        // 根据执行比例分配剩余配额
        for (Map.Entry<TaskClassifier.TaskType, Integer> entry : executionCounts.entrySet()) {
            double proportion = (double)entry.getValue() / totalExecutions;
            int additionalQuota = (int)(remainingQuota * proportion);
            newQuotas.compute(entry.getKey(), (k, v) -> v + additionalQuota);
        }

        // 确保总和为100%
        int sum = newQuotas.values().stream().mapToInt(Integer::intValue).sum();
        if (sum != 100) {
            // 调整最大配额类型
            TaskClassifier.TaskType maxType = Collections.max(
                    newQuotas.entrySet(),
                    Map.Entry.comparingByValue()
            ).getKey();
            newQuotas.compute(maxType, (k, v) -> v + (100 - sum));
        }

        // 更新资源配额
        resourceManager.adjustQuotas(newQuotas);

        // 重置统计和时间
        executionCounts.clear();
        lastAdjustmentTime = currentTime;
    }
}
```

## 七、总结

本优化方案针对图式激活扩散机制的任务优先级管理提出了一系列改进措施：

1. 多因素优先级计算：考虑节点、连接、来源、能量和上下文等多种因素
2. 动态优先级调整：基于等待时间、系统负载和时间衰减动态调整优先级
3. 任务分类与分组：将任务分类并分配资源配额，确保各类任务都能得到处理
4. 等待队列管理：实现等待队列处理，确保资源可用时执行等待的任务
5. 优先级监控与调整：监控任务执行情况，动态调整资源配额

这些优化措施将显著提高任务调度的效率和公平性，确保重要任务能够及时执行，同时防止低优先级任务长时间饥饿，从而提升系统的整体性能和响应能力。
