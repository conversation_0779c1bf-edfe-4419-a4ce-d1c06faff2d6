# 多位数加法可执行图式 - 综合优化版本

## 一、概述

本文档描述了多位数加法可执行图式的综合优化版本，结合了当前系统实现的树状结构优势和"图式模型设计与结构分析整合版.md"中建议的点边类型定义优势。这种综合方案既保留了查询和管理的便利性，又增强了语义表达和扩展能力。

## 二、综合优化的图式结构设计

### 1. 设计原则

综合优化版本基于以下设计原则：

1. **保留树状结构**：保留时序主题节点统筹整个时序图式的树状结构，便于查询和管理
2. **引入明确点类型**：引入明确的点类型定义，增强语义表达
3. **显式表示数据流**：显式表示数据流关系，便于理解和优化
4. **节点归属明确**：通过树状结构明确节点归属，解决节点复用问题
5. **兼容现有实现**：与当前实现兼容，便于平滑过渡

### 2. 点类型定义

综合优化版本定义了以下点类型，同时保留了场景节点的标签：

- **上下文节点（ContextNode）**：表示执行上下文，如加法计算的整体环境，对应场景节点中的时序主题
- **数据节点（DataNode）**：表示数据或变量，如操作数、结果、进位等
- **操作节点（OperationNode）**：表示具体操作，如获取当前位、计算和、更新结果等
- **控制节点（ControlNode）**：表示控制结构，如循环、条件判断等

### 3. 边类型定义

综合优化版本保留了当前系统的边类型，并增加了数据流边：

- **时序边（SequenceEdge）**：连接上下文节点和步骤节点，表示"包含步骤"的关系
- **顺承边（SuccessionEdge）**：连接步骤节点，表示执行顺序
- **判断边（ConditionalEdge）**：表示条件分支
- **循环边（LoopEdge）**：表示循环结构
- **变量边（VariableEdge）**：连接变量节点和其值
- **数据流边（DataFlowEdge）**：表示数据传递，如参数传递和结果返回（新增）

### 4. 点边结构表示

多位数加法的综合优化点边结构如下：

```
// 上下文节点（保留树状结构）
加法计算(类型=ContextNode) -[时序首]-> 初始化(类型=OperationNode)
加法计算(类型=ContextNode) -[时序]-> 处理位循环(类型=ControlNode)
加法计算(类型=ContextNode) -[时序]-> 处理最终进位(类型=ControlNode)
加法计算(类型=ContextNode) -[时序]-> 返回结果(类型=OperationNode)

// 执行顺序（保留链式结构）
初始化 -[顺承]-> 处理位循环
处理位循环 -[顺承]-> 处理最终进位
处理最终进位 -[顺承]-> 返回结果

// 初始化操作的子图式结构（细化到底层API）
初始化(类型=OperationNode) -[时序首]-> 检查操作数有效性(类型=OperationNode)
初始化 -[时序]-> 创建空结果字符串(类型=OperationNode)
初始化 -[时序]-> 设置进位为0(类型=OperationNode)
初始化 -[时序]-> 计算初始位索引(类型=OperationNode)

// 初始化内部执行顺序
检查操作数有效性 -[顺承]-> 创建空结果字符串
创建空结果字符串 -[顺承]-> 设置进位为0
设置进位为0 -[顺承]-> 计算初始位索引

// 初始化内部操作与底层API关联
检查操作数有效性.setApiReference("validation/basic/check_operands_validity")
创建空结果字符串.setApiReference("string/basic/create_empty_string")
设置进位为0.setApiReference("variable/basic/set_zero")
计算初始位索引.setApiReference("math/basic/min_length_minus_one")

// 初始化内部数据流
操作数1 -[数据流]-> 检查操作数有效性
操作数2 -[数据流]-> 检查操作数有效性
创建空结果字符串 -[数据流]-> 结果
设置进位为0 -[数据流]-> 进位
操作数1 -[数据流]-> 计算初始位索引
操作数2 -[数据流]-> 计算初始位索引
计算初始位索引 -[数据流]-> 当前位索引

// 处理位循环的子图式结构（细化到底层API）
处理位循环(类型=ControlNode) -[时序首]-> 检查循环条件(类型=OperationNode)
处理位循环 -[时序]-> 获取当前位(类型=OperationNode)
处理位循环 -[时序]-> 计算当前位和进位(类型=OperationNode)
处理位循环 -[时序]-> 更新结果(类型=OperationNode)
处理位循环 -[时序]-> 移动到下一位(类型=OperationNode)

// 处理位循环内部执行顺序
检查循环条件 -[顺承]-> 获取当前位
获取当前位 -[顺承]-> 计算当前位和进位
计算当前位和进位 -[顺承]-> 更新结果
更新结果 -[顺承]-> 移动到下一位
移动到下一位 -[顺承]-> 检查循环条件

// 处理位循环内部操作与底层API关联
检查循环条件.setApiReference("comparison/basic/greater_or_equal_than_zero")

// 检查循环条件的子图式
检查循环条件 -[时序首]-> 获取当前位索引(类型=OperationNode)
检查循环条件 -[时序]-> 比较与零(类型=OperationNode)
获取当前位索引 -[顺承]-> 比较与零
获取当前位索引.setApiReference("variable/basic/get_value")
比较与零.setApiReference("comparison/basic/greater_or_equal_than_zero")
当前位索引 -[数据流]-> 获取当前位索引
获取当前位索引 -[数据流]-> 比较与零

// 处理最终进位的子图式结构（细化到底层API）
处理最终进位(类型=ControlNode) -[时序首]-> 检查进位是否大于零(类型=OperationNode)
处理最终进位 -[时序]-> 添加进位(类型=OperationNode)
处理最终进位 -[时序]-> 跳过进位(类型=OperationNode)

// 处理最终进位内部执行顺序
检查进位是否大于零 -[判断首]-> 添加进位  // 如果进位>0
检查进位是否大于零 -[判断]-> 跳过进位  // 如果进位=0

// 处理最终进位内部操作与底层API关联
检查进位是否大于零.setApiReference("comparison/basic/greater_than_zero")
添加进位.setApiReference("string/basic/prepend_int_to_string")
跳过进位.setApiReference("control/basic/no_operation")

// 处理最终进位内部数据流
进位 -[数据流]-> 检查进位是否大于零
进位 -[数据流]-> 添加进位
结果 -[数据流]-> 添加进位
添加进位 -[数据流]-> 结果

// 返回结果的子图式结构（细化到底层API）
返回结果(类型=OperationNode) -[时序首]-> 获取结果值(类型=OperationNode)
返回结果 -[时序]-> 设置返回值(类型=OperationNode)

// 返回结果内部执行顺序
获取结果值 -[顺承]-> 设置返回值

// 返回结果内部操作与底层API关联
获取结果值.setApiReference("variable/basic/get_value")
设置返回值.setApiReference("variable/basic/set_return_value")

// 返回结果内部数据流
结果 -[数据流]-> 获取结果值
获取结果值 -[数据流]-> 设置返回值

// 循环内部结构（保留树状结构）
处理位循环 -[时序]-> 获取当前位(类型=OperationNode)
处理位循环 -[时序]-> 计算当前位和进位(类型=OperationNode)
处理位循环 -[时序]-> 更新结果(类型=OperationNode)
处理位循环 -[时序]-> 移动到下一位(类型=OperationNode)

// 循环内部执行顺序（保留链式结构）
获取当前位 -[顺承]-> 计算当前位和进位
计算当前位和进位 -[顺承]-> 更新结果
更新结果 -[顺承]-> 移动到下一位
移动到下一位 -[循环条件]-> 处理位循环  // 如果还有位需要处理，继续循环

// 获取当前位的子图式结构（细化到底层API）
获取当前位(类型=OperationNode) -[时序首]-> 检查缓存(类型=OperationNode)
获取当前位 -[时序]-> 检查缓存结果(类型=ControlNode)
获取当前位 -[时序]-> 获取操作数1当前位(类型=OperationNode)
获取当前位 -[时序]-> 获取操作数2当前位(类型=OperationNode)
获取当前位 -[时序]-> 缓存结果(类型=OperationNode)

// 获取当前位内部执行顺序
检查缓存 -[顺承]-> 检查缓存结果
检查缓存结果 -[判断首]-> 使用缓存结果(类型=OperationNode)  // 如果有缓存
检查缓存结果 -[判断]-> 获取操作数1当前位  // 如果没有缓存
获取操作数1当前位 -[顺承]-> 获取操作数2当前位
获取操作数2当前位 -[顺承]-> 缓存结果
使用缓存结果 -[顺承]-> 计算当前位和进位  // 跳过到下一个操作
缓存结果 -[顺承]-> 计算当前位和进位  // 跳过到下一个操作

// 获取当前位内部操作与底层API关联
检查缓存.setApiReference("memory/search/check_cache")
检查缓存结果.setApiReference("control/basic/check_condition")
使用缓存结果.setApiReference("memory/search/use_cached_result")

// 获取操作数1当前位的子图式
获取操作数1当前位 -[时序首]-> 检查操作数1长度(类型=OperationNode)
获取操作数1当前位 -[时序]-> 比较索引与长度(类型=ControlNode)
获取操作数1当前位 -[时序]-> 从操作数1获取字符(类型=OperationNode)
获取操作数1当前位 -[时序]-> 转换为数字(类型=OperationNode)
获取操作数1当前位 -[时序]-> 设置位1为0(类型=OperationNode)

// 获取操作数1当前位内部执行顺序
检查操作数1长度 -[顺承]-> 比较索引与长度
比较索引与长度 -[判断首]-> 从操作数1获取字符  // 如果索引<长度
比较索引与长度 -[判断]-> 设置位1为0  // 如果索引>=长度
从操作数1获取字符 -[顺承]-> 转换为数字
转换为数字 -[顺承]-> 设置位1变量(类型=OperationNode)
设置位1为0 -[顺承]-> 设置位1变量

// 获取操作数1当前位内部操作与底层API关联
检查操作数1长度.setApiReference("string/basic/get_length")
比较索引与长度.setApiReference("comparison/basic/less_than")
从操作数1获取字符.setApiReference("string/basic/get_char_at_reverse_index")
转换为数字.setApiReference("conversion/basic/char_to_digit")
设置位1为0.setApiReference("variable/basic/set_zero")
设置位1变量.setApiReference("variable/basic/set_value")

// 获取操作数2当前位的子图式
获取操作数2当前位 -[时序首]-> 检查操作数2长度(类型=OperationNode)
获取操作数2当前位 -[时序]-> 比较索引与长度2(类型=ControlNode)
获取操作数2当前位 -[时序]-> 从操作数2获取字符(类型=OperationNode)
获取操作数2当前位 -[时序]-> 转换为数字2(类型=OperationNode)
获取操作数2当前位 -[时序]-> 设置位2为0(类型=OperationNode)

// 获取操作数2当前位内部执行顺序
检查操作数2长度 -[顺承]-> 比较索引与长度2
比较索引与长度2 -[判断首]-> 从操作数2获取字符  // 如果索引<长度
比较索引与长度2 -[判断]-> 设置位2为0  // 如果索引>=长度
从操作数2获取字符 -[顺承]-> 转换为数字2
转换为数字2 -[顺承]-> 设置位2变量(类型=OperationNode)
设置位2为0 -[顺承]-> 设置位2变量

// 获取操作数2当前位内部操作与底层API关联
检查操作数2长度.setApiReference("string/basic/get_length")
比较索引与长度2.setApiReference("comparison/basic/less_than")
从操作数2获取字符.setApiReference("string/basic/get_char_at_reverse_index")
转换为数字2.setApiReference("conversion/basic/char_to_digit")
设置位2为0.setApiReference("variable/basic/set_zero")
设置位2变量.setApiReference("variable/basic/set_value")

// 获取操作数2当前位内部数据流
操作数2 -[数据流]-> 检查操作数2长度
当前位索引 -[数据流]-> 比较索引与长度2
操作数2 -[数据流]-> 从操作数2获取字符
当前位索引 -[数据流]-> 从操作数2获取字符
从操作数2获取字符 -[数据流]-> 转换为数字2
转换为数字2 -[数据流]-> 设置位2变量
设置位2变量 -[数据流]-> 位2
设置位2为0 -[数据流]-> 设置位2变量

// 缓存结果的子图式
缓存结果 -[时序首]-> 创建缓存键(类型=OperationNode)
缓存结果 -[时序]-> 创建结果映射(类型=OperationNode)
缓存结果 -[时序]-> 存储到缓存(类型=OperationNode)

// 缓存结果内部执行顺序
创建缓存键 -[顺承]-> 创建结果映射
创建结果映射 -[顺承]-> 存储到缓存

// 缓存结果内部操作与底层API关联
创建缓存键.setApiReference("string/basic/create_cache_key")
创建结果映射.setApiReference("collection/basic/create_map")
存储到缓存.setApiReference("memory/cache/store_result")

// 获取当前位内部数据流
操作数1 -[数据流]-> 检查缓存
操作数2 -[数据流]-> 检查缓存
当前位索引 -[数据流]-> 检查缓存
检查缓存 -[数据流]-> 检查缓存结果
使用缓存结果 -[数据流]-> 位1
使用缓存结果 -[数据流]-> 位2
操作数1 -[数据流]-> 检查操作数1长度
当前位索引 -[数据流]-> 比较索引与长度
操作数1 -[数据流]-> 从操作数1获取字符
当前位索引 -[数据流]-> 从操作数1获取字符
从操作数1获取字符 -[数据流]-> 转换为数字
转换为数字 -[数据流]-> 设置位1变量
设置位1变量 -[数据流]-> 位1
设置位1为0 -[数据流]-> 设置位1变量
操作数1 -[数据流]-> 创建缓存键
操作数2 -[数据流]-> 创建缓存键
当前位索引 -[数据流]-> 创建缓存键
位1 -[数据流]-> 创建结果映射
位2 -[数据流]-> 创建结果映射
创建缓存键 -[数据流]-> 存储到缓存
创建结果映射 -[数据流]-> 存储到缓存

// 计算当前位和进位的子图式结构（细化到底层API）
计算当前位和进位(类型=OperationNode) -[时序首]-> 检查加法缓存(类型=OperationNode)
计算当前位和进位 -[时序]-> 检查缓存结果(类型=ControlNode)
计算当前位和进位 -[时序]-> 执行加法(类型=OperationNode)
计算当前位和进位 -[时序]-> 计算当前位结果(类型=OperationNode)
计算当前位和进位 -[时序]-> 计算新进位(类型=OperationNode)
计算当前位和进位 -[时序]-> 缓存加法结果(类型=OperationNode)

// 计算当前位和进位内部执行顺序
检查加法缓存 -[顺承]-> 检查缓存结果
检查缓存结果 -[判断首]-> 使用缓存结果(类型=OperationNode)  // 如果有缓存
检查缓存结果 -[判断]-> 执行加法  // 如果没有缓存
执行加法 -[顺承]-> 计算当前位结果
计算当前位结果 -[顺承]-> 计算新进位
计算新进位 -[顺承]-> 缓存加法结果
使用缓存结果 -[顺承]-> 更新结果  // 跳过到下一个操作
缓存加法结果 -[顺承]-> 更新结果  // 跳过到下一个操作

// 计算当前位和进位内部操作与底层API关联
检查加法缓存.setApiReference("memory/search/check_addition_cache")
检查缓存结果.setApiReference("control/basic/check_condition")
使用缓存结果.setApiReference("memory/search/use_cached_addition")

// 执行加法的子图式
执行加法 -[时序首]-> 获取位1值(类型=OperationNode)
执行加法 -[时序]-> 获取位2值(类型=OperationNode)
执行加法 -[时序]-> 获取进位值(类型=OperationNode)
执行加法 -[时序]-> 执行位1加位2(类型=OperationNode)
执行加法 -[时序]-> 执行中间结果加进位(类型=OperationNode)

// 执行加法内部执行顺序
获取位1值 -[顺承]-> 获取位2值
获取位2值 -[顺承]-> 获取进位值
获取进位值 -[顺承]-> 执行位1加位2
执行位1加位2 -[顺承]-> 执行中间结果加进位

// 执行加法内部操作与底层API关联
获取位1值.setApiReference("variable/basic/get_value")
获取位2值.setApiReference("variable/basic/get_value")
获取进位值.setApiReference("variable/basic/get_value")
执行位1加位2.setApiReference("math/basic/add")
执行中间结果加进位.setApiReference("math/basic/add")

// 计算当前位结果的子图式
计算当前位结果 -[时序首]-> 获取总和(类型=OperationNode)
计算当前位结果 -[时序]-> 执行模10取模(类型=OperationNode)
计算当前位结果 -[时序]-> 设置当前位结果(类型=OperationNode)

// 计算当前位结果内部执行顺序
获取总和 -[顺承]-> 执行模10取模
执行模10取模 -[顺承]-> 设置当前位结果

// 计算当前位结果内部操作与底层API关联
获取总和.setApiReference("variable/basic/get_value")
执行模10取模.setApiReference("math/basic/mod_10")
设置当前位结果.setApiReference("variable/basic/set_value")

// 计算新进位的子图式
计算新进位 -[时序首]-> 获取总和值(类型=OperationNode)
计算新进位 -[时序]-> 执行除以10(类型=OperationNode)
计算新进位 -[时序]-> 设置新进位(类型=OperationNode)

// 计算新进位内部执行顺序
获取总和值 -[顺承]-> 执行除以10
执行除以10 -[顺承]-> 设置新进位

// 计算新进位内部操作与底层API关联
获取总和值.setApiReference("variable/basic/get_value")
执行除以10.setApiReference("math/basic/divide_10")
设置新进位.setApiReference("variable/basic/set_value")

// 缓存加法结果的子图式
缓存加法结果 -[时序首]-> 创建加法缓存键(类型=OperationNode)
缓存加法结果 -[时序]-> 创建加法结果映射(类型=OperationNode)
缓存加法结果 -[时序]-> 存储到加法缓存(类型=OperationNode)

// 缓存加法结果内部执行顺序
创建加法缓存键 -[顺承]-> 创建加法结果映射
创建加法结果映射 -[顺承]-> 存储到加法缓存

// 缓存加法结果内部操作与底层API关联
创建加法缓存键.setApiReference("string/basic/create_addition_cache_key")
创建加法结果映射.setApiReference("collection/basic/create_map")
存储到加法缓存.setApiReference("memory/cache/store_addition_result")

// 计算当前位和进位内部数据流
位1 -[数据流]-> 检查加法缓存
位2 -[数据流]-> 检查加法缓存
进位 -[数据流]-> 检查加法缓存
检查加法缓存 -[数据流]-> 检查缓存结果
使用缓存结果 -[数据流]-> 当前位结果
使用缓存结果 -[数据流]-> 进位
位1 -[数据流]-> 获取位1值
位2 -[数据流]-> 获取位2值
进位 -[数据流]-> 获取进位值
获取位1值 -[数据流]-> 执行位1加位2
获取位2值 -[数据流]-> 执行位1加位2
执行位1加位2 -[数据流]-> 执行中间结果加进位
获取进位值 -[数据流]-> 执行中间结果加进位
执行中间结果加进位 -[数据流]-> 和(类型=DataNode)
和 -[数据流]-> 获取总和
获取总和 -[数据流]-> 执行模10取模
执行模10取模 -[数据流]-> 设置当前位结果
设置当前位结果 -[数据流]-> 当前位结果
和 -[数据流]-> 获取总和值
获取总和值 -[数据流]-> 执行除以10
执行除以10 -[数据流]-> 设置新进位
设置新进位 -[数据流]-> 进位
位1 -[数据流]-> 创建加法缓存键
位2 -[数据流]-> 创建加法缓存键
进位 -[数据流]-> 创建加法缓存键
当前位结果 -[数据流]-> 创建加法结果映射
进位 -[数据流]-> 创建加法结果映射
创建加法缓存键 -[数据流]-> 存储到加法缓存
创建加法结果映射 -[数据流]-> 存储到加法缓存

// 条件结构（保留树状结构）
处理最终进位 -[判断首]-> 添加进位(类型=OperationNode)  // 如果有最终进位，添加到结果
处理最终进位 -[判断]-> 跳过进位(类型=OperationNode)  // 如果没有最终进位，跳过

// 条件结构执行顺序
添加进位 -[顺承]-> 返回结果
跳过进位 -[顺承]-> 返回结果

// 更新结果的子图式结构（细化到底层API）
更新结果(类型=OperationNode) -[时序首]-> 获取当前结果值(类型=OperationNode)
更新结果 -[时序]-> 获取当前位结果值(类型=OperationNode)
更新结果 -[时序]-> 将当前位结果转为字符(类型=OperationNode)
更新结果 -[时序]-> 在结果前添加当前位(类型=OperationNode)
更新结果 -[时序]-> 设置新结果值(类型=OperationNode)

// 更新结果内部执行顺序
获取当前结果值 -[顺承]-> 获取当前位结果值
获取当前位结果值 -[顺承]-> 将当前位结果转为字符
将当前位结果转为字符 -[顺承]-> 在结果前添加当前位
在结果前添加当前位 -[顺承]-> 设置新结果值

// 更新结果内部操作与底层API关联
获取当前结果值.setApiReference("variable/basic/get_value")
获取当前位结果值.setApiReference("variable/basic/get_value")
将当前位结果转为字符.setApiReference("conversion/basic/int_to_string")
在结果前添加当前位.setApiReference("string/basic/prepend")
设置新结果值.setApiReference("variable/basic/set_value")

// 更新结果内部数据流
结果 -[数据流]-> 获取当前结果值
当前位结果 -[数据流]-> 获取当前位结果值
获取当前位结果值 -[数据流]-> 将当前位结果转为字符
将当前位结果转为字符 -[数据流]-> 在结果前添加当前位
获取当前结果值 -[数据流]-> 在结果前添加当前位
在结果前添加当前位 -[数据流]-> 设置新结果值
设置新结果值 -[数据流]-> 结果

// 移动到下一位的子图式结构（细化到底层API）
移动到下一位(类型=OperationNode) -[时序首]-> 获取当前位索引值(类型=OperationNode)
移动到下一位 -[时序]-> 执行索引减1(类型=OperationNode)
移动到下一位 -[时序]-> 设置新位索引值(类型=OperationNode)
移动到下一位 -[时序]-> 检查循环结束条件(类型=OperationNode)

// 移动到下一位内部执行顺序
获取当前位索引值 -[顺承]-> 执行索引减1
执行索引减1 -[顺承]-> 设置新位索引值
设置新位索引值 -[顺承]-> 检查循环结束条件

// 移动到下一位内部操作与底层API关联
获取当前位索引值.setApiReference("variable/basic/get_value")
执行索引减1.setApiReference("math/basic/decrement")
设置新位索引值.setApiReference("variable/basic/set_value")
检查循环结束条件.setApiReference("comparison/basic/greater_or_equal_than_zero")

// 移动到下一位内部数据流
当前位索引 -[数据流]-> 获取当前位索引值
获取当前位索引值 -[数据流]-> 执行索引减1
执行索引减1 -[数据流]-> 设置新位索引值
设置新位索引值 -[数据流]-> 当前位索引
当前位索引 -[数据流]-> 检查循环结束条件

// 数据节点和变量绑定（保留当前实现）
操作数1(类型=DataNode) -[变量]-> 加法计算
操作数2(类型=DataNode) -[变量]-> 加法计算
结果(类型=DataNode) -[变量]-> 加法计算
进位(类型=DataNode) -[变量]-> 加法计算
当前位索引(类型=DataNode) -[变量]-> 加法计算

// 数据流关系（新增）
操作数1 -[数据流]-> 获取当前位
操作数2 -[数据流]-> 获取当前位
当前位索引 -[数据流]-> 获取当前位
获取当前位 -[数据流]-> 计算当前位和进位  // 输出位1和位2
进位 -[数据流]-> 计算当前位和进位
计算当前位和进位 -[数据流]-> 进位  // 更新进位
计算当前位和进位 -[数据流]-> 更新结果  // 输出当前位结果
结果 -[数据流]-> 更新结果
更新结果 -[数据流]-> 结果  // 更新结果
当前位索引 -[数据流]-> 移动到下一位
移动到下一位 -[数据流]-> 当前位索引  // 更新当前位索引
进位 -[数据流]-> 添加进位
结果 -[数据流]-> 添加进位
添加进位 -[数据流]-> 结果  // 更新结果
结果 -[数据流]-> 返回结果
```

### 5. 图数据库查询表示

在综合优化版本中，图数据库查询既可以利用树状结构快速查询整个时序图式，又可以通过点类型和边类型进行精确查询：

```java
// 查询整个时序图式（利用树状结构）
String query = "match (n:场景)-[r:时序]->(m) where n.name = '" + 加法计算.getName() + "' return m";

// 查询特定类型的节点（利用点类型）
query = "match (n:场景) where n.node_type = 'OperationNode' return n";

// 查询特定操作的数据流（利用数据流边）
query = "match (n)-[r:数据流]->(m) where n.name = '" + 计算当前位和进位.getName() + "' return m";

// 查询循环条件（保留当前实现）
query = "match (n)-[r:循环条件]->(m) where id(m) = " + 处理位循环.getNodeId() + " return r";

// 查询判断结构（保留当前实现）
query = "match (m:场景)-[r:判断首]->(i:场景) where m.name = '" + 处理最终进位.getName() + "' return r";

// 查询变量绑定（保留当前实现）
candidateLinks = NeoUtil.getSomeLinks(加法计算, null, null, null, "变量");
```

## 三、底层API分类与实现

为了支持可执行图式的执行，系统需要提供一系列底层API。这些API按功能分类，提供了从基本数据操作到复杂控制流的各种能力。以下是完整的底层API分类与实现：

### 1. 验证操作 (validation/basic)

验证操作用于检查输入数据的有效性，确保操作可以安全执行。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| validation/basic/is_null | 检查变量是否为空 | 变量 | 布尔值 |
| validation/basic/is_empty_string | 检查字符串是否为空 | 字符串 | 布尔值 |
| validation/basic/is_digit | 检查字符是否为数字字符 | 字符 | 布尔值 |
| validation/basic/is_numeric_string | 检查字符串是否只包含数字 | 字符串 | 布尔值 |
| validation/basic/is_in_range | 检查数值是否在指定范围内 | 数值, 最小值, 最大值 | 布尔值 |
| validation/basic/is_positive | 检查数值是否为正数 | 数值 | 布尔值 |
| validation/basic/is_negative | 检查数值是否为负数 | 数值 | 布尔值 |
| validation/basic/is_zero | 检查数值是否为零 | 数值 | 布尔值 |
| validation/basic/has_length | 检查字符串长度是否符合要求 | 字符串, 期望长度 | 布尔值 |
| validation/basic/length_greater_than | 检查字符串长度是否大于指定值 | 字符串, 长度 | 布尔值 |
| validation/basic/length_less_than | 检查字符串长度是否小于指定值 | 字符串, 长度 | 布尔值 |

### 2. 字符串操作 (string/basic)

字符串操作提供了对字符串的基本处理能力，包括创建、修改、查询等。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| string/basic/create_empty | 创建空字符串 | 无 | 空字符串 |
| string/basic/create_from_char | 从单个字符创建字符串 | 字符 | 字符串 |
| string/basic/length | 获取字符串长度 | 字符串 | 整数 |
| string/basic/get_char_at | 获取指定位置的字符 | 字符串, 索引 | 字符 |
| string/basic/get_char_at_reverse | 从字符串末尾获取指定位置的字符 | 字符串, 索引 | 字符 |
| string/basic/concat | 连接两个字符串 | 字符串1, 字符串2 | 连接后的字符串 |
| string/basic/prepend | 在字符串前添加字符串 | 原字符串, 添加字符串 | 新字符串 |
| string/basic/append | 在字符串后添加字符串 | 原字符串, 添加字符串 | 新字符串 |
| string/basic/prepend_char | 在字符串前添加字符 | 原字符串, 字符 | 新字符串 |
| string/basic/append_char | 在字符串后添加字符 | 原字符串, 字符 | 新字符串 |
| string/basic/substring | 获取子字符串 | 字符串, 起始位置, 长度 | 子字符串 |
| string/basic/substring_from | 从指定位置到结尾的子字符串 | 字符串, 起始位置 | 子字符串 |
| string/basic/substring_to | 从开头到指定位置的子字符串 | 字符串, 结束位置 | 子字符串 |
| string/basic/trim | 去除字符串首尾空白字符 | 字符串 | 处理后的字符串 |
| string/basic/equals | 比较两个字符串是否相等 | 字符串1, 字符串2 | 布尔值 |
| string/basic/starts_with | 检查字符串是否以指定字符串开头 | 字符串, 前缀 | 布尔值 |
| string/basic/ends_with | 检查字符串是否以指定字符串结尾 | 字符串, 后缀 | 布尔值 |
| string/basic/index_of | 查找子字符串在字符串中的位置 | 字符串, 子字符串 | 索引或-1 |
| string/basic/last_index_of | 查找子字符串在字符串中的最后位置 | 字符串, 子字符串 | 索引或-1 |
| string/basic/replace | 替换字符串中的子字符串 | 原字符串, 目标子串, 替换子串 | 新字符串 |
| string/basic/to_upper | 将字符串转换为大写 | 字符串 | 大写字符串 |
| string/basic/to_lower | 将字符串转换为小写 | 字符串 | 小写字符串 |

### 3. 变量操作 (variable/basic)

变量操作提供了对变量的基本操作，包括设置、获取、修改等。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| variable/basic/create | 创建新变量 | 变量名, 初始值(可选) | 变量引用 |
| variable/basic/set | 设置变量值 | 变量引用, 值 | 无 |
| variable/basic/get | 获取变量值 | 变量引用 | 变量值 |
| variable/basic/set_int | 设置整数变量值 | 变量引用, 整数值 | 无 |
| variable/basic/set_boolean | 设置布尔变量值 | 变量引用, 布尔值 | 无 |
| variable/basic/set_string | 设置字符串变量值 | 变量引用, 字符串值 | 无 |
| variable/basic/set_zero | 将变量设置为0 | 变量引用 | 无 |
| variable/basic/set_one | 将变量设置为1 | 变量引用 | 无 |
| variable/basic/set_true | 将变量设置为true | 变量引用 | 无 |
| variable/basic/set_false | 将变量设置为false | 变量引用 | 无 |
| variable/basic/set_empty_string | 将变量设置为空字符串 | 变量引用 | 无 |
| variable/basic/get_int | 获取整数变量值 | 变量引用 | 整数值 |
| variable/basic/get_boolean | 获取布尔变量值 | 变量引用 | 布尔值 |
| variable/basic/get_string | 获取字符串变量值 | 变量引用 | 字符串值 |
| variable/basic/exists | 检查变量是否存在 | 变量名 | 布尔值 |
| variable/basic/copy | 复制变量值 | 源变量引用, 目标变量引用 | 无 |
| variable/basic/swap | 交换两个变量的值 | 变量引用1, 变量引用2 | 无 |
| variable/basic/set_return_value | 设置返回值 | 值 | 无 |
| variable/basic/get_return_value | 获取返回值 | 无 | 返回值 |

### 4. 数学操作 (math/basic)

数学操作提供了基本的算术运算能力。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| math/basic/add | 两数相加 | 加数a, 加数b | 和 |
| math/basic/subtract | 两数相减 | 被减数, 减数 | 差 |
| math/basic/multiply | 两数相乘 | 乘数a, 乘数b | 积 |
| math/basic/divide | 两数相除 | 被除数, 除数 | 商 |
| math/basic/modulo | 取模运算 | 被除数, 除数 | 余数 |
| math/basic/increment | 将数值加1 | 数值 | 结果 |
| math/basic/decrement | 将数值减1 | 数值 | 结果 |
| math/basic/abs | 计算绝对值 | 数值 | 绝对值 |
| math/basic/negate | 取负值 | 数值 | 负值 |
| math/basic/power | 幂运算 | 底数, 指数 | 结果 |
| math/basic/sqrt | 开平方 | 数值 | 平方根 |
| math/basic/min | 返回两数中的最小值 | 数值a, 数值b | 最小值 |
| math/basic/max | 返回两数中的最大值 | 数值a, 数值b | 最大值 |
| math/basic/floor | 向下取整 | 浮点数 | 整数 |
| math/basic/ceiling | 向上取整 | 浮点数 | 整数 |
| math/basic/round | 四舍五入取整 | 浮点数 | 整数 |
| math/basic/mod_10 | 对10取模 | 整数 | 余数(0-9) |
| math/basic/divide_10 | 除以10 | 整数 | 商 |
| math/basic/add_with_carry | 带进位的加法 | 加数a, 加数b, 进位 | [结果, 新进位] |
| math/basic/subtract_with_borrow | 带借位的减法 | 被减数, 减数, 借位 | [结果, 新借位] |
| math/basic/is_even | 检查数值是否为偶数 | 数值 | 布尔值 |
| math/basic/is_odd | 检查数值是否为奇数 | 数值 | 布尔值 |
| math/basic/min_length_minus_one | 计算两个字符串长度的最小值减1 | 字符串1, 字符串2 | 整数 |

### 5. 比较操作 (comparison/basic)

比较操作提供了对数值和其他数据类型的比较能力。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| comparison/basic/equals | 检查两个值是否相等 | 值1, 值2 | 布尔值 |
| comparison/basic/not_equals | 检查两个值是否不相等 | 值1, 值2 | 布尔值 |
| comparison/basic/greater_than | 检查第一个数是否大于第二个数 | 数值1, 数值2 | 布尔值 |
| comparison/basic/less_than | 检查第一个数是否小于第二个数 | 数值1, 数值2 | 布尔值 |
| comparison/basic/greater_or_equal | 检查第一个数是否大于等于第二个数 | 数值1, 数值2 | 布尔值 |
| comparison/basic/less_or_equal | 检查第一个数是否小于等于第二个数 | 数值1, 数值2 | 布尔值 |
| comparison/basic/equals_zero | 检查数值是否等于0 | 数值 | 布尔值 |
| comparison/basic/not_equals_zero | 检查数值是否不等于0 | 数值 | 布尔值 |
| comparison/basic/greater_than_zero | 检查数值是否大于0 | 数值 | 布尔值 |
| comparison/basic/less_than_zero | 检查数值是否小于0 | 数值 | 布尔值 |
| comparison/basic/greater_or_equal_than_zero | 检查数值是否大于等于0 | 数值 | 布尔值 |
| comparison/basic/less_or_equal_than_zero | 检查数值是否小于等于0 | 数值 | 布尔值 |
| comparison/basic/compare | 比较两个值并返回比较结果 | 值1, 值2 | 整数(-1, 0, 1) |
| comparison/basic/compare_to_zero | 将数值与0比较并返回比较结果 | 数值 | 整数(-1, 0, 1) |
| comparison/basic/between | 检查数值是否在指定范围内 | 数值, 下限, 上限 | 布尔值 |

### 6. 控制操作 (control/basic)

控制操作提供了程序流程控制的能力。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| control/basic/no_operation | 不执行任何操作 | 无 | 无 |
| control/basic/evaluate_condition | 计算条件表达式 | 条件表达式 | 布尔值 |
| control/basic/if | 条件分支（只有真分支） | 条件, 真分支操作 | 执行结果或空 |
| control/basic/if_else | 条件分支（有真和假分支） | 条件, 真分支操作, 假分支操作 | 执行结果 |
| control/basic/while | 当条件为真时循环执行 | 条件, 循环体操作 | 最终结果 |
| control/basic/do_while | 先执行一次再检查条件循环 | 循环体操作, 条件 | 最终结果 |
| control/basic/for | 带初始化、条件和步进的循环 | 初始化操作, 条件, 步进操作, 循环体操作 | 最终结果 |
| control/basic/break | 跳出当前循环 | 无 | 无 |
| control/basic/continue | 跳过当前循环的剩余部分，进入下一次循环 | 无 | 无 |
| control/basic/return | 返回值并结束当前函数 | 返回值(可选) | 无 |
| control/basic/sequence | 按顺序执行一系列操作 | 操作列表 | 最后一个操作的结果 |
| control/basic/switch | 多路分支 | 表达式, 分支映射 | 执行结果 |
| control/basic/try_catch | 异常处理 | 尝试操作, 异常处理操作 | 执行结果 |
| control/basic/throw | 抛出异常 | 异常信息 | 无 |

### 7. 记忆操作 (memory/basic, memory/cache, memory/search)

记忆操作提供了对系统记忆的访问和管理能力。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| memory/basic/create | 创建新的记忆项 | 键, 值 | 记忆项引用 |
| memory/basic/get | 获取记忆项的值 | 键 | 值或空 |
| memory/basic/set | 设置记忆项的值 | 键, 值 | 无 |
| memory/basic/exists | 检查记忆项是否存在 | 键 | 布尔值 |
| memory/basic/remove | 移除记忆项 | 键 | 无 |
| memory/basic/clear | 清除所有记忆项 | 无 | 无 |
| memory/basic/keys | 获取所有记忆项的键 | 无 | 键列表 |
| memory/basic/values | 获取所有记忆项的值 | 无 | 值列表 |
| memory/basic/size | 获取记忆项数量 | 无 | 整数 |
| memory/cache/create_key | 创建缓存键 | 参数列表 | 缓存键字符串 |
| memory/cache/has | 检查缓存中是否有结果 | 缓存键 | 布尔值 |
| memory/cache/get | 从缓存中获取结果 | 缓存键 | 缓存值或空 |
| memory/cache/put | 将结果存储到缓存 | 缓存键, 结果 | 无 |
| memory/cache/remove | 从缓存中移除结果 | 缓存键 | 无 |
| memory/cache/clear | 清除缓存 | 无 | 无 |
| memory/cache/create_addition_key | 创建加法缓存键 | 位1, 位2, 进位 | 缓存键字符串 |
| memory/cache/put_addition_result | 存储加法结果到缓存 | 缓存键, 结果位, 新进位 | 无 |
| memory/search/find_by_key | 根据键搜索记忆项 | 键模式 | 匹配的记忆项列表 |
| memory/search/find_by_value | 根据值搜索记忆项 | 值模式 | 匹配的记忆项列表 |
| memory/search/find_digit_at_position | 搜索特定位置的数字 | 操作数, 位置 | 数字 |
| memory/search/find_addition_result | 搜索加法结果 | 位1, 位2, 进位 | [结果位, 新进位] |

### 8. 转换操作 (conversion/basic)

转换操作提供了不同数据类型之间的转换能力。

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| conversion/basic/char_to_digit | 将字符转换为数字 | 字符 | 整数 |
| conversion/basic/digit_to_char | 将数字转换为字符 | 数字(0-9) | 字符 |
| conversion/basic/char_to_string | 将字符转换为字符串 | 字符 | 字符串 |
| conversion/basic/int_to_string | 将整数转换为字符串 | 整数 | 字符串 |
| conversion/basic/string_to_int | 将字符串转换为整数 | 字符串 | 整数 |
| conversion/basic/float_to_string | 将浮点数转换为字符串 | 浮点数, 精度(可选) | 字符串 |
| conversion/basic/string_to_float | 将字符串转换为浮点数 | 字符串 | 浮点数 |
| conversion/basic/boolean_to_string | 将布尔值转换为字符串 | 布尔值 | 字符串("true"或"false") |
| conversion/basic/string_to_boolean | 将字符串转换为布尔值 | 字符串 | 布尔值 |
| conversion/basic/boolean_to_int | 将布尔值转换为整数 | 布尔值 | 整数(0或1) |
| conversion/basic/int_to_boolean | 将整数转换为布尔值 | 整数 | 布尔值(0为false，非0为true) |
| conversion/basic/int_to_float | 将整数转换为浮点数 | 整数 | 浮点数 |
| conversion/basic/float_to_int | 将浮点数转换为整数 | 浮点数, 转换模式(可选) | 整数 |
| conversion/basic/array_to_string | 将数组转换为字符串 | 数组, 分隔符(可选) | 字符串 |
| conversion/basic/string_to_array | 将字符串转换为数组 | 字符串, 分隔符 | 字符串数组 |
| conversion/basic/map_to_string | 将映射转换为字符串 | 映射, 格式(可选) | 字符串 |
| conversion/basic/string_to_map | 将字符串转换为映射 | 字符串, 格式 | 映射 |

### 9. 集合操作 (collection/basic)

集合操作提供了对集合数据类型的操作能力。

#### 9.1 列表操作

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| collection/list/create | 创建空列表 | 无 | 列表 |
| collection/list/create_with_values | 创建并初始化列表 | 初始值列表 | 列表 |
| collection/list/size | 获取列表大小 | 列表 | 整数 |
| collection/list/is_empty | 检查列表是否为空 | 列表 | 布尔值 |
| collection/list/get | 获取列表中指定位置的元素 | 列表, 索引 | 元素 |
| collection/list/set | 设置列表中指定位置的元素 | 列表, 索引, 元素 | 无 |
| collection/list/add | 在列表末尾添加元素 | 列表, 元素 | 无 |
| collection/list/add_at | 在列表指定位置添加元素 | 列表, 索引, 元素 | 无 |
| collection/list/remove | 移除列表中的元素 | 列表, 元素 | 布尔值 |
| collection/list/remove_at | 移除列表中指定位置的元素 | 列表, 索引 | 被移除的元素 |
| collection/list/clear | 清空列表 | 列表 | 无 |
| collection/list/contains | 检查列表是否包含指定元素 | 列表, 元素 | 布尔值 |
| collection/list/index_of | 查找元素在列表中的位置 | 列表, 元素 | 索引或-1 |
| collection/list/sublist | 获取列表的子列表 | 列表, 起始索引, 结束索引 | 子列表 |
| collection/list/sort | 对列表进行排序 | 列表, 比较器(可选) | 排序后的列表 |
| collection/list/reverse | 反转列表 | 列表 | 反转后的列表 |

#### 9.2 映射操作

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| collection/map/create | 创建空映射 | 无 | 映射 |
| collection/map/create_with_entries | 创建并初始化映射 | 键列表, 值列表 | 映射 |
| collection/map/size | 获取映射大小 | 映射 | 整数 |
| collection/map/is_empty | 检查映射是否为空 | 映射 | 布尔值 |
| collection/map/get | 从映射中获取值 | 映射, 键 | 值或空 |
| collection/map/put | 向映射中添加键值对 | 映射, 键, 值 | 无 |
| collection/map/remove | 从映射中移除键值对 | 映射, 键 | 被移除的值或空 |
| collection/map/clear | 清空映射 | 映射 | 无 |
| collection/map/contains_key | 检查映射是否包含指定键 | 映射, 键 | 布尔值 |
| collection/map/contains_value | 检查映射是否包含指定值 | 映射, 值 | 布尔值 |
| collection/map/keys | 获取映射中的所有键 | 映射 | 键列表 |
| collection/map/values | 获取映射中的所有值 | 映射 | 值列表 |
| collection/map/entries | 获取映射中的所有键值对 | 映射 | 键值对列表 |

#### 9.3 集合操作

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| collection/set/create | 创建空集合 | 无 | 集合 |
| collection/set/create_with_values | 创建并初始化集合 | 初始值列表 | 集合 |
| collection/set/size | 获取集合大小 | 集合 | 整数 |
| collection/set/is_empty | 检查集合是否为空 | 集合 | 布尔值 |
| collection/set/add | 向集合中添加元素 | 集合, 元素 | 布尔值 |
| collection/set/remove | 从集合中移除元素 | 集合, 元素 | 布尔值 |
| collection/set/clear | 清空集合 | 集合 | 无 |
| collection/set/contains | 检查集合是否包含指定元素 | 集合, 元素 | 布尔值 |
| collection/set/to_array | 将集合转换为数组 | 集合 | 数组 |
| collection/set/union | 计算两个集合的并集 | 集合1, 集合2 | 并集 |
| collection/set/intersection | 计算两个集合的交集 | 集合1, 集合2 | 交集 |
| collection/set/difference | 计算两个集合的差集 | 集合1, 集合2 | 差集 |

### 10. 高级操作 (advanced)

高级操作是由基本操作组合而成的复杂操作，提供了更高层次的功能。

#### 10.1 高级数学操作

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| math/advanced/multi_digit_addition | 多位数加法 | 操作数1, 操作数2 | 结果 |
| math/advanced/multi_digit_subtraction | 多位数减法 | 被减数, 减数 | 结果 |
| math/advanced/multi_digit_multiplication | 多位数乘法 | 乘数1, 乘数2 | 结果 |
| math/advanced/multi_digit_division | 多位数除法 | 被除数, 除数 | [商, 余数] |
| math/advanced/gcd | 求最大公约数 | 数a, 数b | 最大公约数 |
| math/advanced/lcm | 求最小公倍数 | 数a, 数b | 最小公倍数 |
| math/advanced/factorial | 计算阶乘 | 数值 | 阶乘结果 |
| math/advanced/fibonacci | 计算斐波那契数 | 索引 | 斐波那契数 |
| math/advanced/prime_check | 检查是否为质数 | 数值 | 布尔值 |
| math/advanced/prime_factors | 分解质因数 | 数值 | 质因数列表 |

#### 10.2 高级字符串操作

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| string/advanced/format | 格式化字符串 | 模板, 参数列表 | 格式化后的字符串 |
| string/advanced/split | 分割字符串 | 字符串, 分隔符 | 字符串数组 |
| string/advanced/join | 连接字符串数组 | 字符串数组, 连接符 | 连接后的字符串 |
| string/advanced/regex_match | 正则表达式匹配 | 字符串, 正则表达式 | 布尔值 |
| string/advanced/regex_replace | 正则表达式替换 | 字符串, 正则表达式, 替换字符串 | 替换后的字符串 |
| string/advanced/regex_find | 正则表达式查找 | 字符串, 正则表达式 | 匹配结果列表 |

#### 10.3 高级集合操作

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| collection/advanced/filter | 过滤集合 | 集合, 过滤条件 | 过滤后的集合 |
| collection/advanced/map | 映射集合 | 集合, 映射函数 | 映射后的集合 |
| collection/advanced/reduce | 归约集合 | 集合, 归约函数, 初始值 | 归约结果 |
| collection/advanced/group_by | 分组集合 | 集合, 分组函数 | 分组结果映射 |
| collection/advanced/sort_by | 按指定属性排序 | 集合, 属性, 排序方式 | 排序后的集合 |

#### 10.4 高级控制操作

| API路径 | 功能描述 | 参数 | 返回值 |
|--------|---------|------|-------|
| control/advanced/retry | 重试操作 | 操作, 重试次数, 间隔 | 操作结果 |
| control/advanced/timeout | 超时操作 | 操作, 超时时间 | 操作结果或超时异常 |
| control/advanced/parallel | 并行执行 | 操作列表 | 结果列表 |
| control/advanced/debounce | 防抖操作 | 操作, 延迟时间 | 防抖后的操作 |
| control/advanced/throttle | 节流操作 | 操作, 间隔时间 | 节流后的操作 |

这些底层API构成了可执行图式的基础设施，通过组合这些API，可以实现从简单到复杂的各种计算和控制流程。在实际实现中，这些API可以根据需要进一步细化或扩展。

## 四、总结

本文档描述了多位数加法可执行图式的点边结构表示，并统一罗列了支持其执行的底层API。通过结合树状结构和点边类型定义，实现了更有表达力和扩展性的图式表示。同时，底层API的原子化设计使得可执行图式能够灵活组合，实现复杂的计算和控制流程。

这种设计方案不仅适用于多位数加法，还可以扩展到其他复杂的计算和控制流程。通过将复杂操作分解为原子操作的组合，可以实现更高的灵活性和可维护性。

```
