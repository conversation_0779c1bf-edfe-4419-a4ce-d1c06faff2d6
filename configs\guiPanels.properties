###############################################################################
# Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
# This program and the accompanying materials are made available 
# under the terms of the LIDA Software Framework Non-Commercial License v1.0 
# which accompanies this distribution, and is available at
# http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
###############################################################################

#name = panel title, class name, Position [A,B,C,FLOAT, TOOL], tab order, Refresh after load

# ____Tool Bar____
ToolBar=ToolBar,edu.memphis.ccrg.lida.Framework.gui.panels.ControlToolBarPanel,TOOL,1,Y,1,20

# ____A Section____
environ =AlifeEnvironment,edu.memphis.ccrg.lida.alifeagent.guipanels.ALifeGuiPanel,A,1,Y,configs/icons.properties,30

# ____B Section____
GW = GW Coalitions,edu.memphis.ccrg.lida.Framework.gui.panels.GlobalWorkspaceTablePanel,B,0,Y,GlobalWorkspace
pamTable = PAM Table,edu.memphis.ccrg.lida.Framework.gui.panels.NodeStructureTable,B,1,Y,Workspace.CurrentSituationalModel
nonGraph = nonGraph,edu.memphis.ccrg.lida.Framework.gui.panels.NodeStructurePanel,B,2,Y,Workspace.NonGraph
feelGraph = feelGraph,edu.memphis.ccrg.lida.Framework.gui.panels.NodeStructurePanel,B,3,Y,Workspace.FeelGraph
#sceneGraph = sceneGraph,edu.memphis.ccrg.lida.Framework.gui.panels.NodeStructurePanel,B,2,Y,Workspace.SceneGraph


# ____C Section____
LogPanel=Logging,edu.memphis.ccrg.lida.Framework.gui.panels.LoggingPanel,C,1,N, alifeagent, edu.memphis.ccrg.alife
taskQueue=Task Queue,edu.memphis.ccrg.lida.Framework.gui.panels.TaskQueuePanel,C,2,N


actionSelection=Action Selection,edu.memphis.ccrg.lida.Framework.gui.panels.ActionSelectionPanel0,C,3,Y
activationChart=Activation Chart,edu.memphis.ccrg.lida.Framework.gui.panels.ActivationChartPanel,C,4,Y,60,goodHealth


# ____D Section____
csmGraph = CSM,edu.memphis.ccrg.lida.Framework.gui.panels.NodeStructurePanel,D,0,Y,Workspace.CurrentSituationalModel
configFiles = Configuration Files,edu.memphis.ccrg.lida.Framework.gui.panels.ConfigurationFilesPanel,D,1,N
proceduralMemory=Procedural Memory,edu.memphis.ccrg.lida.Framework.gui.panels.ProceduralMemoryPanel,D,2,Y


# ____E Section____
runningTasks = Running Tasks,edu.memphis.ccrg.lida.Framework.gui.panels.FrameworkTaskPanel,E,0,Y, PerceptualAssociativeMemory
perceptualBufferGraph = Perceptual Buffer,edu.memphis.ccrg.lida.Framework.gui.panels.NodeStructurePanel,E,1,Y,Workspace.PerceptualBuffer

#chatBox = chatBox ,edu.memphis.ccrg.lida.Framework.gui.panels.ChatPanel,E,0,N

#pamGraph = PAM Graph,edu.memphis.ccrg.lida.Framework.gui.panels.NodeStructurePanel,B,1,Y,Workspace.CurrentSituationalModel