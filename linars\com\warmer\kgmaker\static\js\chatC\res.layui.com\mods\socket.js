﻿layui.define(['jquery', 'layer','contextMenu','form'], function (exports) {
    // var contextMenu = layui.contextMenu;
    var $ = layui.jquery;
    var layer = layui.layer;
    var form = layui.form;
    var layim = layui.layim;
    var cachedata = layim.cache();

    var reconnectInterval = null;

    var conf = {
        uid: 0,
        key: '',
        log:true,
        server:'ws://localhost:8666',
        token:'/webim/token',
        reconn:false
    };

    var cmd = {
        COMMAND_UNKNOW :0,
        COMMAND_HANDSHAKE_REQ :1,
        COMMAND_HANDSHAKE_RESP :2,
        COMMAND_AUTH_REQ :3,
        COMMAND_AUTH_RESP :4,
        COMMAND_LOGIN_REQ :5,
        COMMAND_LOGIN_RESP :6,
        COMMAND_JOIN_GROUP_REQ :7,
        COMMAND_JOIN_GROUP_RESP :8,
        COMMAND_JOIN_GROUP_NOTIFY_RESP :9,
        COMMAND_EXIT_GROUP_NOTIFY_RESP :10,
        COMMAND_CHAT_REQ :11,
        COMMAND_CHAT_RESP :12,
        COMMAND_HEARTBEAT_REQ :13,
        COMMAND_CLOSE_REQ :14,
        COMMAND_CANCEL_MSG_REQ :15,
        COMMAND_CANCEL_MSG_RESP :16,
        COMMAND_GET_USER_REQ :17,
        COMMAND_GET_USER_RESP :18,
        COMMAND_GET_MESSAGE_REQ :19,
        COMMAND_GET_MESSAGE_RESP :20
    };

    var msgType = {
        text : 0,
        image : 1,
        voice : 2,
        video : 3,
        music : 4,
        news : 5
    };

    var chatType = {
        chatFriend:2,
        chatGroup:1
    };

    var tool={
        ws:null,
        options :{},//选项属性，可当不定参数传进this的方法内

        wsUseful:function(){
            return !!window['WebSocket'];
        },
        log:function (msg) {
            this.options.log&&console.log(msg);
        },
        init:function (options) {
            this.options = options;
            this.log('加载配置:'+JSON.stringify(tool.options));
            this.connect();
        },
        token:function (callback) {
            $.get(tool.options.token,function (res) {
                if(res.code>0){
                    layer.msg("未登录");
                }else{
                    callback(res.data);
                }
            })
        },
        connect:function () {
            if(this.wsUseful()) {
                if (this.options.server) {

                    this.token(function (token) {

                        // tool.ws = new WebSocket(tool.options.server);
                        // tool.ws = new WebSocket(tool.options.server + '?user=' + layim.cache.mine);
                        // tool.ws = new WebSocket(tool.options.server + '?taken=' + encodeURIComponent(token));
                        tool.ws = new WebSocket(tool.options.server + '/' + encodeURIComponent(token));
                        // tool.ws = new WebSocket(tool.options.server + '/' + token);
                        // tool.ws = new WebSocket(tool.options.server);
                        tool.regWsEvent();

                    });

                }else{
                    layer.msg("server配置项无效");
                }
            }
        },
        regWsEvent:function () {
            if(this.ws){
                this.ws.onmessage = function (event) {
                    call.msg&&call.msg(event);
                };
                this.ws.onclose = function (event) {
                    call.close&&call.close(event);
                    tool.reconnect();
                };
                this.ws.onopen = function (event) {
                    call.open&&call.open(event);
                    if(reconnectInterval){
                        clearInterval(reconnectInterval);
                        reconnectInterval = null;
                    }
                };
                this.ws.onerror = function (event) {
                    call.error&&call.error(event);
                };
            }
        },
        reconnect:function () {
            console.log(reconnectInterval);
            if(tool.options.reconn) {
                if (reconnectInterval == null) {
                    reconnectInterval = setInterval(function () {
                        tool.log("正在尝试重连...");
                        tool.connect();
                    }, 2000);
                }
            }
        },
        check:function (data) {
            var ismsg = data['msgType'];
            if(!data || ismsg == undefined){
                layer.msg('消息格式不正确');
                return false;
            }
            return true;
        },
        send:function (data){
            if(this.check(data)) {
                // if (socket.readyState===1) {
                // this.ws.send(JSON.stringify(data));
                // }
            }
        }
    };

    // var WebIM = window.WebIM || {};
    // WebIM.connection = connection;
    // WebIM.message = Message;
    //
    // var conn = new WebIM.connection({
    //     isMultiLoginSessions: true,
    //     https: false,
    //     heartBeatWait: 4500,
    //     autoReconnectNumMax: 2,
    //     autoReconnectInterval: 2,
    //     isDebug: true, // 打开调试，会自动打印log，在控制台的console中查看log
    //     delivery: true, // 是否发送已读回执
    // });

    var call ={};
    var socket = {
        config: function (options) {
            if (!tool.wsUseful()) {
                layer.msg("该浏览器不支持websocket");
            }
            conf = $.extend(conf, options); //把layim继承出去，方便在register中使用
            this.register();
            tool.init(options);
            im.init(options.user,options.pwd);
        },
        msgType:msgType,
        chatType:chatType,
        cmd:cmd,
        send:function (data) {
            tool.send(data);
        },
        on:function (event,callback) {
            if(typeof callback === 'function'){
                (!call[event]) && (call[event] = callback);
                tool.log('注册事件：【'+event+'】');
            }
            return this;
        },
        register: function () {
            if (layim) {

                //监听签名修改
                // layim.on('sign', function (value) {
                //     $.post('../webim/change_sign', {sign: value}, function (data) {
                //         console.log('签名修改'+data);
                //     });
                // });

                //监听layim建立就绪
                layim.on('ready', function (res) {

                    // req.loading = false;
                    // req.get('/webim/apply-unread',{},function (res) {
                    //     res.data&&layim.msgbox(res.data);
                    // });
                    // console.log(layim.cache().friend);  //获取缓存里的数据

                    if (cachedata.mine.msgBox != 0) {
                        layim.msgbox(cachedata.mine.msgBox); //消息盒子有新消息
                    };                     
                    im.contextMenu();
                });

                $('body').on('click', '*[socket-event]', function(e){//自定义事件
                  var othis = $(this), methid = othis.attr('socket-event');
                  im[methid] ? im[methid].call(this, othis, e) : '';
                });                                         
                //监听聊天窗口的切换
                layim.on('chatChange', function (res) {

                    // var t = res.data.type=='friend';
                    // socket.send({
                    //     msgType:t? socket.msgType.checkIsOnline:socket.msgType.checkOnlineCount,
                    //     id:res.data.id
                    // });

                    im.closeAllGroupList();                   
                    var type = res.data.type;
                    if (type === 'friend') {
                        //模拟标注好友状态
                        im.userStatus({
                            id: res.data.id
                        });
                    } else if (type === 'group') {
                        var _time = (new Date()).valueOf();//当前时间
                        // if (parseInt(res.data.gagTime) > _time) {
                            im.setGag({
                                groupidx: res.data.id,
                                type: 'set',
                                user: cachedata.mine.id,
                                gagTime: ''
                            });
                        // }
                    }
                });
                layim.on('sendMessage', function (data) { //监听发送消息
                    data.to.cmd = 0;
                    if (data.to.type == 'friend') {
                        im.sendMsg(data);
                    }else{
                        var _time = (new Date()).valueOf();//当前时间
                        var gagTime = parseInt(layim.thisChat().data.gagTime);
                        gagTime = 1525845693777;
                        if (gagTime < _time) {
                            var group_id = data.to.id;
                            eval("data.group_id =" + group_id);
                            im.sendMsg(data);
                        }else{
                            im.popMsg(data,'当前为禁言状态，消息未发送成功！');
                            return false;
                        }      
                    }
                });
            }
        },

        
    };

    var im = {
        init: function (user,pwd) { 
            this.initListener(user,pwd);    //初始化事件监听
        },
        contextMenu : function(){//定义右键操作
            var my_spread = $('.layim-list-friend >li');
            my_spread.mousedown(function(e){
                var data = {
                    contextItem: "context-friend", // 添加class
                    target: function(ele) { // 当前元素
                        $(".context-friend").attr("data-id",ele[0].id.replace(/[^0-9]/ig,"")).attr("data-name",ele.find("span").html());
                        $(".context-friend").attr("data-img",ele.find("img").attr('src')).attr("data-type",'friend');
                    },
                    menu:[]
                };
                // data.menu.push(im.menuChat());   //单击即可闲聊，不用再右键
                // data.menu.push(im.menuInfo());
                data.menu.push(im.menuChatLog());
                // data.menu.push(im.menuNickName());
                var currentGroupidx = $(this).find('h5').data('groupidx');//当前分组id
                if(my_spread.length >= 2){ //当至少有两个分组时
                    var html = '<ul>';
                    for (var i = 0; i < my_spread.length; i++) {
                        var groupidx = my_spread.eq(i).find('h5').data('groupidx');
                        if (currentGroupidx != groupidx) {
                            var groupName = my_spread.eq(i).find('h5 span').html();
                            html += '<li class="ui-move-menu-item" data-groupidx="'+groupidx+'" data-groupName="'+groupName+'"><a href="javascript:void(0);"><span>'+groupName+'</span></a></li>'
                        };
                    };
                    html += '</ul>';
                    data.menu.push(im.menuMove(html));                
                }
                data.menu.push(im.menuRemove());
                $(".layim-list-friend >li > ul > li").contextMenu(data);//好友右键事件
            });

            $(".layim-list-friend >li > h5").mousedown(function(e){
                    var data = {
                        contextItem: "context-mygroup", // 添加class
                        target: function(ele) { // 当前元素
                            $(".context-mygroup").attr("data-id",ele.data('groupidx')).attr("data-name",ele.find("span").html());
                        },
                        menu: []                        
                    };             
                    data.menu.push(im.menuAddMyGroup());
                    data.menu.push(im.menuRename());
                    if ($(this).parent().find('ul li').data('index') !== 0) {data.menu.push(im.menuDelMyGroup()); };
                                             
                $(this).contextMenu(data);  //好友分组右键事件                                  
            });


            $(".layim-list-group > li").mousedown(function(e){
                    var data = {
                        contextItem: "context-group", // 添加class
                        target: function(ele) { // 当前元素
                            $(".context-group").attr("data-id",ele[0].id.replace(/[^0-9]/ig,"")).attr("data-name",ele.find("span").html())
                            .attr("data-img",ele.find("img").attr('src')).attr("data-type",'group')   
                        },
                        menu: []                        
                    };             
                    data.menu.push(im.menuChat());
                    // data.menu.push(im.menuInfo());
                    data.menu.push(im.menuChatLog());
                    // data.menu.push(im.menuLeaveGroupBySelf());
                                             
                $(this).contextMenu(data);  //面板群组右键事件                                 
            });


            $('.groupMembers > li').mousedown(function(e){//聊天页面群组右键事件
                var data = {
                    contextItem: "context-group-member", // 添加class
                    isfriend: $(".context-group-member").data("isfriend"), // 添加class
                    target: function(ele) { // 当前元素
                        $(".context-group-member").attr("data-id",ele[0].id.replace(/[^0-9]/ig,""));
                        $(".context-group-member").attr("data-img",ele.find("img").attr('src'));
                        $(".context-group-member").attr("data-name",ele.find("span").html());
                        $(".context-group-member").attr("data-isfriend",ele.attr('isfriend'));
                        $(".context-group-member").attr("data-manager",ele.attr('manager'));
                        $(".context-group-member").attr("data-groupidx",ele.parent().data('groupidx'));
                        $(".context-group-member").attr("data-type",'friend');
                    },
                    menu:[]
                    };    
                var _this = $(this);
                var groupInfo = layim.thisChat().data;
                var _time = (new Date()).valueOf();//当前时间
                var _gagTime = parseInt(_this.attr('gagTime'));//当前禁言时间                  
                if (cachedata.mine.id !== _this.attr('id')) {
                    data.menu.push(im.menuChat()); 
                    // data.menu.push(im.menuInfo());
                    if(3 == e.which && $(this).attr('isfriend') == 0 ){ //点击右键并且不是好友
                        data.menu.push(im.menuAddFriend())
                    }                                                   
                }else{
                    data.menu.push(im.menuEditGroupNickName());
                }                     
                if (groupInfo.manager == 1 && cachedata.mine.id !== _this.attr('id')) {//是群主且操作的对象不是自己
                    if (_this.attr('manager') == 2) {
                        data.menu.push(im.menuRemoveAdmin());
                    }else if (_this.attr('manager') == 3) {
                        data.menu.push(im.menuSetAdmin());
                    }    
                    data.menu.push(im.menuEditGroupNickName());
                    data.menu.push(im.menuLeaveGroup());
                    if (_gagTime < _time) {
                        data.menu.push(im.menuGroupMemberGag());
                    }else{
                        data.menu.push(im.menuLiftGroupMemberGag());
                    }    
                }//群主管理

                layui.each(cachedata.group, function(index, item){                
                    if (item.id == _this.parent().data('groupidx') && item.manager == 2 && _this.attr('manager') == 3 && cachedata.mine.id !== _this.attr('id')) {//管理员且操作的是群员
                        data.menu.push(im.menuEditGroupNickName());
                        data.menu.push(im.menuLeaveGroup());
                        if (_gagTime < _time) {
                            data.menu.push(im.menuGroupMemberGag());
                        }else{
                            data.menu.push(im.menuLiftGroupMemberGag());
                        }
                    }//管理员管理        
                })
                $(".groupMembers > li").contextMenu(data);    
            })


        },        
        initListener: function (user,pwd) { //初始化监听
            // console.log('注册服务连接监听事件');
            var options = {
              // apiUrl: WebIM.config.apiURL,
              user: user,
              pwd: pwd,
              // appKey: WebIM.config.appkey
            };
            // conn.open(options);
        },
        //自定义消息，把消息格式定义为layim的消息类型
        defineMessage: function (message,msgType) {
            var msg;
            switch (msgType) 
            {
                case 'Text': msg = message.data;break;
                case 'Picture': msg = 'img['+message.thumb+']';break;
                case 'Audio': msg = 'audio['+message.audio+']';break;
                case 'File': msg = 'file('+message.url+')['+message.filename+']';break;
                case 'Video': msg = 'video['+message.video+']';break;
            };
            if (message.ext.cmd) {//如果有命令参数
                
                switch (message.ext.cmd.cmdName) 
                {
                    case 'gag': //禁言
                        im.setGag({
                            groupidx: message.to,
                            type: 'set',
                            user: message.ext.cmd.id,
                            gagTime: message.data
                        });                          
                        break;
                    case 'liftGag': //取消禁言 
                        im.setGag({
                            groupidx: message.to,
                            type: 'lift',
                            user: message.ext.cmd.id,
                            gagTime: 0
                        });                          
                        break;
                    // case 'setGag': //禁言
                    // case 'joinGroup': //取消禁言
                    // case 'joinGroup': //加入群
                    // case 'leaveGroup': //退出群
                    // case 'setAdmin': //设置管理员                      
                    // case 'removeAdmin': //取消管理员                   
                    // break;
                    default:
                        layim.getMessage({
                          system: true //系统消息
                          ,id: message.to //聊天窗口ID
                          ,type: "group" //聊天窗口类型
                          ,content: msg
                        });                     
                };
            };
            if (message.type == 'chat') {
                var type = 'friend';
                var id = message.from;
            }else if(message.type == 'groupchat'){
                var type = 'group';
                var id = message.to;
            }               
            if (message.delay) {//离线消息获取不到本地cachedata用户名称需要从服务器获取
                var timestamp = Date.parse(new Date(message.delay));                   
            }else{
                var timestamp = (new Date()).valueOf();                
            }  
            var data = {mine: false,cid: 0,username:message.ext.username,avatar:"./uploads/person/"+message.from+".jpg",content:msg,id:id,fromid: message.from,timestamp:timestamp,type:type}
            if (!message.ext.cmd) {layim.getMessage(data); };
                           
        }, 
        sendMsg: function (data) {  //根据layim提供的data数据，进行解析

            //t是是与否，判断
            var t = data.to.type=='friend';

            if (data.to.id == data.mine.id && t) {
                layer.msg('不能给自己发送消息');
                return;
            }
            // if(!t){
            //     selfFlag = true;
            // }

            // socket.send({
            //     msgType:socket.msgType.text
            //     // msgType:'friend'
            //     ,content:data.mine.content
            //     ,from:data.mine.id
            //     // ,id:data.mine.id
            //     ,to:data.to.id
            //     ,group_id:data.group_id?data.group_id:null
            //     // ,type:'friend'
            //     ,cmd:socket.cmd.COMMAND_CHAT_REQ
            //     ,chatType:(t?socket.chatType.chatFriend:socket.chatType.chatGroup)
            // });

            im.saveChat(data);

            return;
        },
        saveChat: function (data) {

            var content0 = data.mine.content;
            var toId = data.to.id;
            var fromId = data.mine.id;
            var resData = "";

            if(data.to.type == 'group'){
                $.get('../webim/message/saveChat', {content:content0,toId:toId,from:fromId,type:"group",group_id:data.group_id}, function (res) {
                    resData = eval('(' + JSON.stringify(res) + ')');
                    console.log(resData);
                });
            }else if(data.to.type == 'friend'){
                $.get('../webim/message/saveChat', {content:content0,toId:toId,from:fromId,type:"friend"}, function (res) {
                    resData = eval('(' + JSON.stringify(res) + ')');
                    console.log(resData);
                });
            }

        },
        getChatLog: function (data){
            if(!cachedata.base.chatLog){
            return layer.msg('未开启更多聊天记录');
            }
            var index = layer.open({
            // return events.chatLog.index = layer.open({
                type: 2
                ,maxmin: true
                ,title: '与 '+ data.name +' 的聊天记录'
                ,area: ['400px', '93%']
                ,shade: false
                ,offset: 'rb'
                ,skin: 'layui-box'
                ,anim: 2
                ,id: 'layui-layim-chatlog'
                ,content: cachedata.base.chatLog + '?id=' + data.id + '&type=' + data.type
            });
        },
        removeFriends: function (friend_id,fgroupIdx) {
            // conn.removeRoster({
            //     to: username,
            //     success: function () {  // 删除成功
                    $.get('../webim/removeFriends', {friend_id: friend_id,userid:cachedata.mine.id,fgroupId:fgroupIdx}, function (res) {
                        var data = eval('(' + JSON.stringify(res) + ')');
                        if (data.code == 0) {
                            var index = layer.open();
                            layer.close(index);
                            layim.removeList({//从我的列表删除
                                type: 'friend' //或者group
                                ,id: friend_id //好友或者群组ID
                            });  
                            im.removeHistory({//从我的历史列表删除
                                type: 'friend' //或者group
                                ,id: friend_id //好友或者群组ID
                            });
                            parent.location.reload();
                        }else{
                            layer.msg(data.msg);
                        }
                    });
            //     },
            //     error: function () {
            //         console.log('removeFriends faild');
            //        // 删除失败
            //     }
            // });
        },  
        leaveGroupBySelf: function (to,username,roomId) {
            $.get('../webim/leaveGroup', {groupIdx:roomId,id:to}, function (res) {
                var data = eval('(' + JSON.stringify(res) + ')');
                if (data.code == 0) {
                    var option = {
                        to: to,
                        roomId: roomId,
                        success: function (res) {
                            im.sendMsg({//系统消息
                                mine:{
                                    content:username+' 已退出该群',
                                    timestamp:new Date().getTime()
                                },
                                to:{
                                    id:roomId,
                                    type:'group',
                                    cmd:{
                                        cmdName:'leaveGroup',
                                        cmdValue:username
                                    }
                                }
                            });                    
                            layim.removeList({
                                type: 'group' //或者group
                                ,id: roomId //好友或者群组ID
                            });
                            im.removeHistory({//从我的历史列表删除
                                type: 'group' //或者group
                                ,id: roomId //好友或者群组ID
                            });
                            var index = layer.open();
                            layer.close(index);
                            parent.location.reload();
                        },
                        error: function (res) {
                            console.log('Leave room faild');
                        }
                    };
                    // conn.leaveGroupBySelf(option);
                }else{
                    layer.msg(data.msg);
                }
            });                              
        }, 
        removeHistory: function(data){//删除好友或退出群后清除历史记录           
            var history = cachedata.local.history;
            delete history[data.type+data.id];
            cachedata.local.history = history;
            layui.data('layim', {
              key: cachedata.mine.id
              ,value: cachedata.local
            });
            $('#layim-history'+data.id).remove();
            var hisElem = $('.layui-layim').find('.layim-list-history');
            var none = '<li class="layim-null">暂无历史会话</li>'        
            if(hisElem.find('li').length === 0){
              hisElem.html(none);
            }        
        },
        IsExist: function (avatar){ //判断头像是否存在
            var ImgObj=new Image();
            ImgObj.src= avatar;
             if(ImgObj.fileSize > 0 || (ImgObj.width > 0 && ImgObj.height > 0))
             {
               return true;
             } else {
               return false;
            }
        },  
        audio:function(msg){//消息提示
            layim.msgbox(msg);
            var audio = document.createElement("audio");
            audio.src = layui.cache.dir+'css/modules/layim/voice/'+ cachedata.base.voice;
            audio.play(); //消息提示音              
        },
        addFriendGroup:function(othis,type){
            var li = othis.parents('li') || othis.parent()
                    , uid = li.data('uid') || li.data('id')
                    , approval = li.data('approval')
                    , infoid = li.data("infoid")
                    , name = li.data('name');

            if (uid == 'undifine' || !uid) {
                var uid = othis.parent().data('id'), name = othis.parent().data('name');
            }
            var avatar = './uploads/person/'+uid+'.jpg';
            var isAdd = false;
            if (type == 'friend') {
                var default_avatar = './uploads/person/empty2.jpg';
                if(cachedata.mine.id == uid){//添加的是自己
                    layer.msg('不能添加自己');
                    return false;
                }
                layui.each(cachedata.friend, function(index1, item1){
                    layui.each(item1.list, function(index, item){
                        if (item.id == uid) {
                            isAdd = true;
                            layer.msg('已经是好友');
                            return; //只是阻断本函数执行
                        }//是否已经是好友
                    });
                });
            }else{
                var default_avatar = './uploads/person/empty1.jpg';
                for (i in cachedata.group)//是否已经加群
                {
                    if (cachedata.group[i].id == uid) {isAdd = true;break;}
                }
            }
            parent.layui.layim.add({//弹出添加好友对话框
                isAdd: isAdd
                ,approval: approval
                ,username: name || []
                ,uid:uid
                ,avatar: im['IsExist'].call(this, avatar)?avatar:default_avatar
                ,group:  cachedata.friend || []
                ,type: type
                ,submit: function(group,remark,index){//确认发送添加请求
                    if (type == 'friend') {
                        $.get('../webim/apply-friend', {to: uid,msgType:1,remark:remark,fgroupId:group}, function (res) {
                            var data = eval('(' + JSON.stringify(res) + ')');
                            if (data.code == 0) {
                                layer.msg('你申请添加'+name+'为好友的消息已发送。请等待对方确认');
                                layer.close(layer.index);
                            }else{
                                layer.msg('你申请添加'+name+'为好友的消息发送失败。请刷新浏览器后重试');
                            }
                        });
                    }else{

                        // top.window.location.href="${ctx}/info/" + infoid;

                        // $.get('../webim/modify_msg', {fgroupId: uid,msgType:4}, function (res) {
                        //     var data = eval('(' + JSON.stringify(res) + ')');
                        //     if (data.code == 0) {
                        //         layer.msg('你已加入'+name+'，赶紧愉快地去吸猫吧！！');
                        //
                        //         im.sendMsg({//系统消息
                        //             mine:{
                        //                 content:username+' 已加入该群',
                        //                 timestamp:new Date().getTime()
                        //             },
                        //             to:{
                        //                 id:groupIdx,
                        //                 type:'group',
                        //                 cmd:{
                        //                     cmdName:'joinGroup',
                        //                     cmdValue:username
                        //                 }
                        //             }
                        //         });
                        //         im.contextMenu();//更新右键点击事件
                        //     }else{
                        //         layer.msg('你加入'+name+'的失败，请刷新重试哦，再不行就找猫老大');
                        //     }
                        // });


                    }
                }
            },function(){
                layer.close(index);
            });
        },
        receiveAddFriendGroup:function(othis,agree){//确认添加好友
            var li = othis.parents('li')
                    , type = li.data('type')
                    , uid = li.data('uid')
                    , username = li.data('name')
                    , signature = li.data('signature')
                    , msgIdx = li.data('id'); 
            if (type == 1) {
                type = 'friend';
                var avatar = './uploads/person/'+uid+'.jpg';                
                msgType = 2;
            }else{
                type = 'group';
                var groupIdx = li.data('groupidx');
                msgType = 4;  
            }
            var status = agree == 2?2:3; 
            if (agree == 2) {
                if (msgType == 2) {
                    var default_avatar = './uploads/person/empty2.jpg';
                    layim.setFriendGroup({
                        type: type
                        , username: username//用户名称或群组名称
                        , avatar: im['IsExist'].call(this, avatar)?avatar:default_avatar
                        , group: cachedata.friend || [] //获取好友分组数据
                        , submit: function (group, index) { 
                            $.get('../webim/modify_msg', {msgIdx: msgIdx,msgType:msgType,status:status,fgroupId:group,friendIdx:uid}, function (res) {
                                var data = eval('(' + JSON.stringify(res) + ')');
                                if (data.code == 0) {
                                    //将好友 追加到主面板
                                    layim.addList({
                                        type: 'friend'
                                        , avatar: im['IsExist'].call(this, avatar)?avatar:default_avatar //好友头像
                                        , username: username //好友昵称
                                        , groupid: group //所在的分组id
                                        , id: uid //好友ID
                                        , sign: signature //好友签名
                                    });
                                    // conn.subscribed({//同意添加后通知对方
                                    //   to: uid,
                                    //   message : 'Success'
                                    // });
                                    parent.layer.close(index);
                                    othis.parent().html('已同意');
                                    // parent.location.reload();
                                    im.contextMenu();//更新右键点击事件                          
                                }else{
                                    console.log('添加失败');
                                }
                            });                    
                            layer.close(index);
                        }
                    }); 
                }
            }else{              
                $.get('../webim/modify_msg', {msgIdx: msgIdx,msgType:msgType,status:status}, function (res) {
                    var data = eval('(' + JSON.stringify(res) + ')');
                    if (data.code == 0) {
                        othis.parent().html('<em>已拒绝</em>');                        
                    }
                    layer.close(layer.index);
                });
            }
        },
        createGroup: function(othis){   //创建群
            var index = layer.open({
                type: 2
                ,title: '创建群'
                ,shade: false
                ,maxmin: false
                ,area: ['550px', '400px']
                ,skin: 'layui-box layui-layer-border'
                ,resize: false
                ,content: cachedata.base.createGroup
            });
        },
        commitGroupInfo: function(othis,data){
            if (!data.groupName) {
                return false;
            }
            $.get('../webim/userMaxGroupNumber', {}, function(res){
                var resData = eval('(' + res + ')');
                if(resData.code == 0){
                    var options = {
                        data: {
                            groupname: data.groupName,
                            desc: data.des,
                            maxusers:data.number,
                            public: true,
                            approval: data.approval == '1'?true:false,
                            allowinvites: true
                        },
                        success: function (respData) {
                            if (respData.data.groupid) {
                                $.get('../webim/commitGroupInfo', {groupIdx:respData.data.groupid,groupName: data.groupName,des:data.des,number:data.number,approval:data.approval}, function(respdata){
                                    var res = eval('(' + respdata + ')');
                                    if(res.code == 0){
                                        //将群 追加到主面板
                                        var avatar = './uploads/person/'+respData.data.groupid+'.jpg'; 
                                        var default_avatar = './static/img/tel.jpg';
                                        layer.msg(res.msg);
                                        parent.layui.layim.addList({
                                            type: 'group'
                                            , avatar: im['IsExist'].call(this, avatar)?avatar:default_avatar //好友头像
                                            , groupname: data.groupName //群名称
                                            , id: respData.data.groupid //群id
                                        });
                                    }else{
                                        return layer.msg(res.msg);
                                    }
                                    layer.close(layer.index);
                                });   
                            }                  
                            
                        },
                        error: function () {}
                    };
                    // conn.createGroupNew(options);
                }else{
                    return layer.msg(resData.msg);
                }
                layer.close(layer.index);
            });
        },
        getMyInformation: function(){
            var index = layer.open({
                type: 2
                ,title: '我的资料'
                ,shade: false
                ,maxmin: false
                ,area: ['400px', '570px']
                ,skin: 'layui-box layui-layer-border'
                ,resize: true
                ,content: cachedata.base.Information+'?id='+cachedata.mine.id+'&type=friend'
            });           
        },
        getInformation: function(data){
           var id = data.id || {},type = data.type || {};
            var index = layer.open({
                type: 2
                ,title: type  == 'friend'?(cachedata.mine.id == id?'我的资料':'好友资料') :'群资料'
                ,shade: false
                ,maxmin: false
                // ,closeBtn: 0
                ,area: ['400px', '670px']
                ,skin: 'layui-box layui-layer-border'
                ,resize: true
                ,content: cachedata.base.Information+'?id='+id+'&type='+type
            });           
        },        
        userStatus: function(data){
            if (data.id) {
                $.get('../webim/userStatus', {id:data.id}, function (res) {
                    var data = eval('(' + JSON.stringify(res) + ')');
                    if (data.code == 0) {  
                        if (data.data == 'online') {
                            layim.setChatStatus('<span style="color:#FF5722;">在线</span>');
                        }else{
                            layim.setChatStatus('<span style="color:#444;">离线</span>');
                        }                                          
                    }else{
                        //没有该用户
                    }
                });                 
            }
        },
        groupMembers: function(othis, e){
            var othis = $(this);
            var icon = othis.find('.layui-icon'), hide = function(){
            icon.html('&#xe602;');
            $("#layui-layim-chat > ul:eq(1)").remove();
            $(".layui-layim-group-search").remove();
            othis.data('show', null);
            };
            if(othis.data('show')){
                hide();
            } else {
                icon.html('&#xe603;');
                othis.data('show', true);
                var members = cachedata.base.members || {},ul = $("#layui-layim-chat"), li = '', membersCache = {};
                var info = JSON.parse(decodeURIComponent(othis.parent().data('json')));
                members.data = $.extend(members.data, {
                  id: info.id
                });
                $.get(members, function(res){
                    var resp = eval('(' + res + ')');
                    var html = '<ul class="layui-unselect layim-group-list groupMembers" data-groupidx="'+info.id+'" style="height: 510px; display: block;right:-200px;padding-top: 10px;">';
                    layui.each(resp.data.list, function(index, item){
                        html += '<li  id="'+item.id+'" isfriend="'+item.friendship+'" manager="'+item.type+'" gagTime="'+item.gagTime+'"><img src="'+ item.avatar +'">';
                        item.type == 1?
                            (html += '<span style="color:#e24242">'+ item.username +'</span><i class="layui-icon" style="color:#e24242">&#xe612;</i>'):
                            (item.type == 2?
                                (html += '<span style="color:#de6039">'+ item.username +'</span><i class="layui-icon" style="color:#eaa48e">&#xe612;</i>'):
                                (html += '<span>'+ item.username +'</span>'));
                        html += '</li>';    
                        membersCache[item.id] = item;
                    });
                    html += '</ul>';
                    html += '<div class="layui-layim-group-search" socket-event="groupSearch"><input placeholder="搜索"></div>';
                    ul.append(html);
                    im.contextMenu();
                });
            }
        },
        closeAllGroupList: function(){
            var othis = $(".groupMembers");
            othis.remove();//关闭全部的群员列表
            $(".layui-layim-group-search").remove();
            var icon = $('.layim-tool-groupMembers').find('.layui-icon');
            $('.layim-tool-groupMembers').data('show', null);
            icon.html('&#xe602;'); 
        },
        groupSearch: function(othis){
          var search = $("#layui-layim-chat").find('.layui-layim-group-search');
          var main = $("#layui-layim-chat").find('.groupMembers');
          var input = search.find('input'), find = function(e){
            var val = input.val().replace(/\s/);
            var data = [];
            var group = $(".groupMembers li") || [], html = '';        
            if(val === ''){
              for(var j = 0; j < group.length; j++){
                  group.eq(j).css("display","block");
              }
            } else {
                for(var j = 0; j < group.length; j++){
                    name = group.eq(j).find('span').html();
                    if(name.indexOf(val) === -1){
                        group.eq(j).css("display","none");
                    }else{
                        group.eq(j).css("display","block"); 
                    }
                }
            }
          };
          if(!cachedata.base.isfriend && cachedata.base.isgroup){
            events.tab.index = 1;
          } else if(!cachedata.base.isfriend && !cachedata.base.isgroup){
            events.tab.index = 2;
          }          
          search.show();
          input.focus();
          input.off('keyup', find).on('keyup', find);
        },
        addMyGroup: function(){             //新增分组，后台可取到user
            $.get('../webim/addMyGroup', {}, function (res) {
                var data = eval('(' + JSON.stringify(res) + ')');
                if (data.code == 0) {
                    $('.layim-list-friend').append('<li><h5 layim-event="spread" lay-type="false" data-groupidx="'+data.data.id+'"><i class="layui-icon">&#xe602;</i><span>'+data.data.name+'</span><em>(<cite class="layim-count"> 0</cite>)</em></h5><ul class="layui-layim-list"><li class="layim-null">该分组下暂无好友</li></ul></li>');
                    im.contextMenu();
                    // location.reload();
                }else{
                    layer.msg(data.msg);
                }
            }); 
        },
        delMyGroup: function(groupidx){//删除分组
            $.get('../webim/delMyGroup', {fgroupId:groupidx}, function (res) {
                var data = eval('(' + JSON.stringify(res) + ')');
                if (data.code == 0) {
                    var group = $('.layim-list-friend li') || [];
                    for(var j = 0; j < group.length; j++){//遍历每一个分组
                        groupList = group.eq(j).find('h5').data('groupidx');
                        if(groupList === groupidx){//要删除的分组
                            if (group.eq(j).find('ul span').hasClass('layim-null')) {//删除的分组下没有好友
                                group.eq(j).remove();
                            }else{
                                // var html = group.eq(j).find('ul').html();//被删除分组的好友
                                var friend = group.eq(j).find('ul li');

                                //如果不是暂无好友则移动
                                var imnull = friend.attr("class");
                                // var imnull = friend.getAttribute("class");

                                if(imnull != "layim-null"){

                                    var number = friend.length;//被删除分组的好友个数
                                    for (var i = 0; i < number; i++) {
                                        var friend_id = friend.eq(i).attr('id').replace(/^layim-friend/g, '');//好友id
                                        var friend_name = friend.eq(i).find('span').html();//好友id
                                        var signature = friend.eq(i).find('p').html();//好友id
                                        var avatar = '../uploads/person/'+friend_id+'.jpg';
                                        var default_avatar = './uploads/person/empty2.jpg';
                                        layim.removeList({//将好友从之前分组除去
                                            type: 'friend'
                                            ,id: friend_id //好友ID
                                        });
                                        layim.addList({//将好友移动到新分组
                                            type: 'friend'
                                            , avatar: im['IsExist'].call(this, avatar)?avatar:default_avatar //好友头像
                                            , username: friend_name //好友昵称
                                            , groupid: data.data //将好友添加到默认分组
                                            , id: friend_id //好友ID
                                            , sign: signature //好友签名
                                        });
                                    };
                                }
                            }
                        }
                    }
                    layim.removeFList({
                        id:groupidx
                    });
                    im.contextMenu();                    
                    layer.close(layer.index);                   
                }else{
                    layer.msg(data.msg);
                }
            }); 
        },
        setAdmin: function(othis){
            var username = othis.data('id'),friend_avatar = othis.data('img'),
                isfriend = othis.data('isfriend'),name = othis.data('name'),            
                gagTime = othis.data('gagtime'),groupidx = othis.data('groupidx');           
            var options = {
                    groupId: groupidx,
                    username: username,
                    success: function(resp) {
                        $.get('../webim/setAdmin', {groupidx:groupidx,id:username,type:2}, function (res) {
                            var admin = eval('(' + res + ')');
                            if (admin.code == 0) { 
                                $("ul[data-groupidx="+groupidx+"] #"+username).remove();
                                var html = '<li id="'+username+'" isfriend="'+isfriend+'" manager="2" gagTime="'+gagTime+'"><img src="'+friend_avatar+'"><span style="color:#de6039">'+name+'</span><i class="layui-icon" style="color:#eaa48e"></i></li>'
                                $("ul[data-groupidx="+groupidx+"]").find('li').eq(0).after(html);
                                im.contextMenu();                                             
                            }
                            layer.msg(admin.msg); 
                        });                         
                    },
                    error: function(e){
                    }
                };
            // conn.setAdmin(options);
        },     
        removeAdmin: function(othis){
            var username = othis.data('id'),friend_avatar = othis.data('img'),
                isfriend = othis.data('isfriend'),name = othis.data('name').split('<'),
                gagTime = othis.data('gagtime'),groupidx = othis.data('groupidx');
            var options = {
                    groupId: groupidx,
                    username: username,
                    success: function(resp) {
                        $.get('../webim/setAdmin', {groupidx:groupidx,id:username,type:3}, function (res) {
                            var admin = eval('(' + res + ')');
                            if (admin.code == 0) { 
                                $("ul[data-groupidx="+groupidx+"] #"+username).remove();
                                var html = '<li id="'+username+'" isfriend="'+isfriend+'" manager="3" gagTime="'+gagTime+'"><img src="'+friend_avatar+'"><span>'+name[0]+'</span></li>'
                                $("ul[data-groupidx="+groupidx+"]").append(html);
                                im.contextMenu();                                              
                            }
                            layer.msg(admin.msg); 
                        });                         
                    },
                    error: function(e){
                    }
                };
            // conn.removeAdmin(options);
        },
        editGroupNickName: function(othis){
            var id = othis.data('id'),name = othis.data('name').split('('),groupIdx = othis.data('groupidx');
            layer.prompt({title: '请输入群名片，并确认', formType: 0,value: name[0]}, function(nickName, index){
                $.get('../webim/editGroupNickName',{nickName:nickName,id:id,groupIdx:groupIdx},function(res){
                    var data = eval('(' + JSON.stringify(res) + ')');
                    if (data.code == 0) {
                        $("ul[data-groupidx="+groupIdx+"] #"+id).find('span').html(nickName+'('+id+')');
                        layer.close(index);
                    }
                    layer.msg(data.msg);
                });
            });            
        },
        leaveGroup: function(groupIdx,list,username){//list为数组
            $.get('../webim/leaveGroup',{list:list,groupIdx:groupIdx},function(res){
                var data = eval('(' + JSON.stringify(res) + ')');
                if (data.code == 0) {
                    var options = {
                        roomId: groupIdx,
                        list: list,
                        success: function(resp){
                            console.log(resp);
                        },
                        error: function(e){
                            console.log(e);
                        }
                    };
                    // conn.leaveGroup(options);
                    $("ul[data-groupidx="+groupIdx+"] #"+data.data).remove(); 
                    im.sendMsg({//系统消息
                        mine:{
                            content:username+' 已被移出该群',
                            timestamp:new Date().getTime()
                        },
                        to:{
                            id:groupIdx,
                            type:'group',
                            cmd:{
                                cmdName:'leaveGroup',
                                cmdValue:username
                            }
                        }
                    });              
                    var index = layer.open();
                    layer.close(index);
                }
                layer.msg(data.msg);
            });   
        },      
        setGag: function(options){//设置禁言 取消禁言
            var _this_group = $('.layim-chat-list .layim-chatlist-group'+options.groupidx);//选择操作的群
            if (_this_group.find('span').html()) {
                var index = _this_group.index();//对应面板的index
                var cont =  _this_group.parent().parent().find('.layim-chat-box div').eq(index)
                var info = JSON.parse(decodeURIComponent(cont.find('.layim-chat-tool').data('json')));
                // info.manager = message.ext.cmd.cmdValue;第一种
                //禁言 两种方案 第一种是改变用户的状态 优点：只需要判断该参数就能禁言
                // 第二种是设置一个禁言时间点，当当前时间小于该设置的时间则为禁言，优点：自动改变用户禁言状态
                if (options.type == 'set' && (options.user == cachedata.mine.id || options.user == 'ALL')) {//设置禁言单人或全体
                    if (options.gagTime) {
                        info.gagTime = parseInt(options.gagTime);
                        cont.find('.layim-chat-tool').data('json',encodeURIComponent(JSON.stringify(info)));
                        layui.each(cachedata.group, function(index, item){
                            if (item.id === options.groupidx) {
                                cachedata.group[index].gagTime = info.gagTime;
                            }
                        });                         
                    };
                    cont.find('.layim-chat-gag').css('display','block');                       
                }else if(options.type == 'lift' && (options.user == cachedata.mine.id || options.user == 'ALL')){//取消禁言单人或全体
                    cont.find('.layim-chat-tool').data('json',encodeURIComponent(JSON.stringify(info)));
                    layui.each(cachedata.group, function(index, item){
                        if (item.id === options.groupidx) {
                            cachedata.group[index].gagTime = '0';
                        }
                    });
                    cont.find('.layim-chat-gag').css('display','none'); 
                }
                                      
            }else{
                if (options.type == 'set' && (options.user == cachedata.mine.id || options.user == 'ALL')) {//设置禁言单人或全体
                    if (options.gagTime) {
                        layui.each(cachedata.group, function(index, item){
                            if (item.id === options.groupidx) {
                                cachedata.group[index].gagTime = parseInt(options.gagTime);
                            }
                        });                         
                    };
                }else if(options.type == 'lift' && (options.user == cachedata.mine.id || options.user == 'ALL')){//取消禁言单人或全体
                    layui.each(cachedata.group, function(index, item){
                        if (item.id === options.groupidx) {
                            cachedata.group[index].gagTime = '0';
                        }
                    });
                }
            }           
        },       
        popMsg: function(data,msg){//删除本地最新一条发送失败的消息
            var logid = cachedata.local.chatlog[data.to.type+data.to.id];
                logid.pop();                 
            var timestamp = '.timestamp'+data.mine.timestamp;
            $(timestamp).html('<i class="layui-icon" style="color: #F44336;font-size: 20px;float: left;margin-top: 1px;">&#x1007;</i>'+msg);            
        },
        menuChat: function(){
            return data = {
                            text: "发送消息",
                            icon: "&#xe63a;",
                            callback: function(ele) {
                                var othis = ele.parent(),type = othis.data('type'),
                                    name = othis.data('name'),avatar = othis.data('img'),
                                    id = othis.data('id');
                                    // id = (new RegExp(substr).test('layim')?substr.replace(/[^0-9]/ig,""):substr);
                                layim.chat({
                                    name: name
                                    ,type: type
                                    ,avatar: avatar
                                    ,id: id
                                });
                            }
                        }       
        },        
        menuInfo: function(){
            return data =  {
                            text: "查看资料",
                            icon: "&#xe62a;",
                            callback: function(ele) {
                                var othis = ele.parent(),type = othis.data('type'),id = othis.data('id');
                                    // id = (new RegExp(substr).test('layim')?substr.replace(/[^0-9]/ig,""):substr);
                                im.getInformation({
                                    id: id,
                                    type:type
                                });                        
                            }
                        }         
        },
        menuChatLog: function(){
            return data =  {
                            text: "聊天记录",
                            icon: "&#xe60e;",
                            callback: function(ele) {
                                var othis = ele.parent(),type = othis.data('type'),name = othis.data('name'),
                                id = othis.data('id');
                                im.getChatLog({
                                    name: name,
                                    id: id,
                                    type:type
                                });    
                            }
                        }       
        },               
        menuNickName: function(){
            return data =  {
                            text: "修改好友备注",
                            icon: "&#xe6b2;",
                            callback: function(ele) {
                                var othis = ele.parent(),friend_id = othis.data('id'),friend_name = othis.data('name');
                                layer.prompt({title: '修改备注姓名', formType: 0,value: friend_name}, function(nickName, index){
                                    $.get('../webim/editNickName',{nickName:nickName,friend_id:friend_id},function(res){
                                        var data = eval('(' + JSON.stringify(res) + ')');
                                        if (data.code == 0) {                                              
                                            var friendName = $("#layim-friend"+friend_id).find('span');
                                            friendName.html(data.data);
                                            layer.close(index);
                                        }
                                        layer.msg(data.msg);
                                    });
                                });     

                            }
                        }     
        },        
        menuMove: function(html){
            return data = {
                            text: "移动联系人",
                            icon: "&#xe630;",
                            nav: "move",//子导航的样式
                            navIcon: "&#xe602;",//子导航的图标
                            navBody: html,//子导航html
                            callback: function(ele) {
                                var friend_id = ele.parent().data('id');//要移动的好友id
                                var friend_name = ele.parent().data('name');
                                var avatar = '../uploads/person/'+friend_id+'.jpg';
                                var default_avatar = './uploads/person/empty2.jpg';

                                var signature0 = $('.layim-list-friend').find('#layim-friend'+friend_id);
                                var signature = signature0.find('p').html();//获取签名

                                var fgroupIdx = $('.layim-friend' + friend_id).parent().parent().find('h5').data('groupidx');

                                var item = ele.find("ul li");
                                item.hover(function() {
                                    var _this = item.index(this);
                                    var groupidx = item.eq(_this).data('groupidx');//将好友移动到分组的id
                                    $.get('../webim/moveFriend',{friend_id:friend_id,groupidx:groupidx,fgroupIdx:fgroupIdx},function(res){
                                        var data = eval('(' + JSON.stringify(res) + ')');
                                        if (data.code == 0) {
                                            layim.removeList({//将好友从之前分组除去
                                                type: 'friend' 
                                                ,id: friend_id //好友ID
                                            });                                                          
                                            layim.addList({//将好友移动到新分组
                                                type: 'friend'
                                                , avatar: im['IsExist'].call(this, avatar)?avatar:default_avatar //好友头像
                                                , username: friend_name //好友昵称
                                                , groupid: groupidx //所在的分组id
                                                , id: friend_id //好友ID
                                                , sign: signature //好友签名
                                            }); 
                                        }
                                        layer.msg(data.msg);
                                    });                                                                                                                                        
                                });
                            }
                        }       
        },  
        menuRemove: function(){
            return data = {
                        text: "删除好友",
                        icon: "&#xe640;",
                        events: "removeFriends",
                        callback: function(ele) {
                            var othis = ele.parent(),friend_id = othis.data('id'),username,sign;

                            // var fgroupIdx = othis.parent().data('id');
                            // var h5ele0 =  $('#layim-friend'+friend_id);
                            // var h5ele00 =  h5ele0.parent();
                            //
                            // var h5ele000 = $('.layim-list-friend #layim-friend' + friend_id);
                            // var h5ele0000 = $('.layim-list-friend #layim-friend41');
                            // var h5ele00000 = $('.layui-layim-list');
                            //
                            // var h5ele1 = $('.layim-list-friend');
                            // var h5ele4 = $('.layim-list-friend .layim-friend41');
                            // var h5ele5 = $('.layim-list-friend .layim-friend' + friend_id);
                            // var h5ele2 = h5ele1.find('#layim-friend' + friend_id);
                            // var h5ele3 = h5ele1.find('#layim-friend41');
                            // var h5ele6 = h5ele1.find('.layim-friend' + friend_id);
                            //
                            // var hh5 = $('.layim-friend' + friend_id);
                            // var hh50 = hh5.parentNode;
                            // var hh500 = hh5.parent().prevObject;
                            // var hh5000 = hh5.parent().previousSibling;
                            // var hh500000 = hh5.previousSibling
                            //
                            // var h5e = h5ele1.prevObject;
                            // var h5e0 = h5ele1.children()[0].prevObject;
                            // var h5e001 = h5ele1.children();
                            // var h5e0000 = h5ele1.prev();
                            // var h5e000 = h5ele1.children()[0].prev();
                            //
                            // var fgroupIdx = h5e00.val('data-groupidx');
                            // var fgroupIdx = h5e00.getSetAttribute('data-groupidx');
                            // var fgroupIdx = h5e00.attr('data-groupidx');

                            // var h5ele1 = $('.layim-friend' + friend_id).parent().parent();
                            // var h5e00 = h5ele1.children()[0];
                            // var fgroupIdx = h5e00.getAttribute('data-groupidx');

                            var fgroupIdx = $('.layim-friend' + friend_id).parent().parent().find('h5').data('groupidx');

                            // var currentGroupidx = $(this).find('h5').data('groupidx');//当前分组id

                            layui.each(cachedata.friend, function(index1, item1){
                                layui.each(item1.list, function(index, item){
                                    if (item.id === friend_id) {
                                        username = item.username;
                                        sign = item.sign;
                                    }
                                });
                            });
                            layer.confirm('删除后对方将从你的好友列表消失，且以后不会再接收此人的会话消息。<div class="layui-layim-list"><li layim-event="chat" data-type="friend" data-index="0"><img src="./uploads/person/'+friend_id+'.jpg"><span>'+username+'</span><p>'+sign+'</p></li></div>', {
                                btn: ['确定','取消'], //按钮
                                title:['删除好友','background:#b4bdb8'],
                                shade: 0
                            }, function(){
                                im.removeFriends(friend_id,fgroupIdx);
                            }, function(){
                                var index = layer.open();
                                layer.close(index);
                            });                                                    
                        }
                    }         
        },
        menuAddMyGroup: function(){
            return  data =  { 
                            text: "添加分组",
                            icon: "&#xe654;",
                            callback: function(ele) {
                                im.addMyGroup();
                            }
                        }

        },        
        menuRename: function(){
            return  data =  {
                        text: "重命名",
                        icon: "&#xe642;",
                        callback: function(ele) {
                            var othis = ele.parent(),fgroupId = othis.data('id'),groupName = othis.data('name');
                            layer.prompt({title: '请输入分组名，并确认', formType: 0,value: groupName}, function(mygroupName, index){
                                if (mygroupName) {
                                    $.get('../webim/editGroupName',{mygroupName:mygroupName,fgroupId:fgroupId},function(res){
                                        var data = eval('(' + JSON.stringify(res) + ')');
                                        if (data.code == 0) {
                                            var friend_group = $(".layim-list-friend li");
                                            for(var j = 0; j < friend_group.length; j++){
                                                var groupidx = friend_group.eq(j).find('h5').data('groupidx');
                                                if(groupidx == fgroupId){//当前选择的分组
                                                    friend_group.eq(j).find('h5').find('span').html(mygroupName);
                                                }
                                            }
                                            im.contextMenu();            
                                            layer.close(index);
                                        }
                                        layer.msg(data.msg);
                                    });
                                }

                            });
                        }

                    }
        },        
        menuDelMyGroup: function(){
            return  data =  { 
                        text: "删除该组",
                        icon: "&#x1006;",
                        callback: function(ele) {
                            var othis = ele.parent(),fgroupId = othis.data('id');
                            layer.confirm('<div style="float: left;width: 17%;margin-top: 14px;"><i class="layui-icon" style="font-size: 48px;color:#cc4a4a">&#xe607;</i></div><div style="width: 83%;float: left;"> 选定的分组将被删除，组内联系人将会移至默认分组。</div>', {
                                btn: ['确定','取消'], //按钮
                                title:['删除分组','background:#b4bdb8'],
                                shade: 0
                            }, function(){
                                im.delMyGroup(fgroupId);
                            }, function(){
                                var index = layer.open();
                                layer.close(index);
                            });                      
                        }
                    }
        },        
        menuLeaveGroupBySelf: function(){
            return  data =  {
                        text: "退出该群",
                        icon: "&#xe613;",
                        callback: function(ele) {
                            var othis = ele.parent(),
                                group_id = othis.data('id'),  
                                groupname = othis.data('name');
                                avatar = othis.data('img');
                            layer.confirm('您真的要退出该群吗？退出后你将不会再接收此群的会话消息。<div class="layui-layim-list"><li layim-event="chat" data-type="friend" data-index="0"><img src="'+avatar+'"><span>'+groupname+'</span></li></div>', {
                                btn: ['确定','取消'], //按钮
                                title:['提示','background:#b4bdb8'],
                                shade: 0
                            }, function(){
                                var user = cachedata.mine.id;
                                var username = cachedata.mine.username;
                                im.leaveGroupBySelf(user,username,group_id);  
                            }, function(){
                                var index = layer.open();  
                                layer.close(index);
                            }); 
                        }
                    }
        },        
        menuAddFriend: function(){
            return  data =  { 
                text: "添加好友",
                icon: "&#xe654;",
                callback: function(ele) {
                    var othis = ele;
                    im.addFriendGroup(othis,'friend');
                }
            }
        },        
        menuEditGroupNickName: function(){
            return  data =  {
                                text: "修改群名片",
                                icon: "&#xe60a;",
                                callback: function(ele) {
                                    var othis = ele.parent();
                                    im.editGroupNickName(othis);
                                }
                            }
        },        
        menuRemoveAdmin: function(){
            return  data =  {
                                text: "取消管理员",
                                icon: "&#xe612;",
                                callback: function(ele) {
                                    var othis = ele.parent();
                                    im.removeAdmin(othis);                                      
                                }
                            }
        },        
        menuSetAdmin: function(){
            return  data =  {
                                text: "设置为管理员",
                                icon: "&#xe612;",
                                callback: function(ele) {
                                    var othis = ele.parent(),user = othis.data('id');
                                    im.setAdmin(othis);                                                 
                                }
                            }
        },        
        menuLeaveGroup: function(){
            return  data =  {
                                text: "踢出本群",
                                icon: "&#x1006;",
                                callback: function(ele) {
                                    var othis = ele.parent();
                                    var friend_id = ele.parent().data('id');//要禁言的id
                                    var username = ele.parent().data('name');
                                    var groupIdx = ele.parent().data('groupidx');
                                    var list = new Array();
                                    list[0] = friend_id;
                                    im.leaveGroup(groupIdx,list,username)                                                          
                                }
                            }
        },        
        menuGroupMemberGag: function(){
            return  data =  {
                                text: "禁言",
                                icon: "&#xe60f;",
                                nav: "gag",//子导航的样式
                                navIcon: "&#xe602;",//子导航的图标
                                navBody: '<ul><li class="ui-gag-menu-item" data-gag="10m"><a href="javascript:void(0);"><span>禁言10分钟</span></a></li><li class="ui-gag-menu-item" data-gag="1h"><a href="javascript:void(0);"><span>禁言1小时</span></a></li><li class="ui-gag-menu-item" data-gag="6h"><a href="javascript:void(0);"><span>禁言6小时</span></a></li><li class="ui-gag-menu-item" data-gag="12h"><a href="javascript:void(0);"><span>禁言12小时</span></a></li><li class="ui-gag-menu-item" data-gag="1d"><a href="javascript:void(0);"><span>禁言1天</span></a></li></ul>',//子导航html
                                callback: function(ele) {
                                    var friend_id = ele.parent().data('id');//要禁言的id
                                    friend_name = ele.parent().data('name');
                                    groupidx = ele.parent().data('groupidx');
                                    var item = ele.find("ul li");
                                    item.hover(function() {
                                        var _index = item.index(this),gagTime = item.eq(_index).data('gag');//禁言时间
                                        $.get('../webim/groupMemberGag',{gagTime:gagTime,groupidx:groupidx,friend_id:friend_id},function(resp){
                                            var data = eval('(' + resp + ')');
                                            if (data.code == 0) {
                                                var gagTime = data.data.gagTime;
                                                var res = {mine: {
                                                                content: gagTime+'',
                                                                timestamp: data.data.time
                                                            },
                                                            to: {
                                                                type: 'group',
                                                                id: groupidx+"",
                                                                cmd: {
                                                                    id: friend_id,
                                                                    cmdName:'gag',
                                                                    cmdValue:data.data.value
                                                                }
                                                            }}
                                                im.sendMsg(res);
                                                $("ul[data-groupidx="+groupidx+"] #"+friend_id).attr('gagtime',gagTime);
                                            }
                                            layer.msg(data.msg);
                                        });
                                    });                             
                                }
                            }
        },        
        menuLiftGroupMemberGag: function(){
            return  data =  {
                                text: "取消禁言",
                                icon: "&#xe60f;",
                                callback: function(ele) {
                                    var friend_id = ele.parent().data('id');//要禁言的id
                                    friend_name = ele.parent().data('name');
                                    groupidx = ele.parent().data('groupidx');
                                    $.get('../webim/liftGroupMemberGag',{groupidx:groupidx,friend_id:friend_id},function(resp){
                                        var data = eval('(' + resp + ')');
                                        if (data.code == 0) {
                                            var res = {mine: {
                                                            content: '0',
                                                            timestamp: data.data.time
                                                        },
                                                        to: {
                                                            type: 'group',
                                                            id: groupidx+"",
                                                            cmd: {
                                                                id: friend_id,
                                                                cmdName:'liftGag',
                                                                cmdValue:data.data.value
                                                            }
                                                        }}
                                            im.sendMsg(res);
                                            $("ul[data-groupidx="+groupidx+"] #"+friend_id).attr('gagtime',0);                                                
                                        }
                                        layer.msg(data.msg);
                                    });
                                }
                            }
        },

    };
    exports('im', im);

    //创建新socket对象，ws在config里定义，tool的ws
    exports('socket',socket);
});