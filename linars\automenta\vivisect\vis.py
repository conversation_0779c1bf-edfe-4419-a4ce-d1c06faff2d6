"""
Something that can be visualized, drawn, or otherwise represented graphically / visually.
"""
from abc import ABC, abstractmethod

class Vis(ABC):
    """
    Something that can be visualized, drawn, or otherwise represented graphically / visually.
    """
    
    @abstractmethod
    def draw(self, g) -> bool:
        """
        Draw the visualization
        
        Args:
            g: The graphics context
            
        Returns:
            bool: True if it should remain visible, False if it is to be removed
        """
        pass
    
    def on_visible(self, showing: bool):
        """
        Notifies this when visibility has changed
        
        Args:
            showing: Whether the visualization is showing
        """
        pass
    
    def init(self, p):
        """
        Initialize the visualization
        
        Args:
            p: The canvas
        """
        pass
