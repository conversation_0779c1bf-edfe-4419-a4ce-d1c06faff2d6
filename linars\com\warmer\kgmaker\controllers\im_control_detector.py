"""
Instant Messaging Control Detector

This module provides controllers for instant messaging functionality.
"""
import json
import logging
import urllib.parse
from typing import List, Dict, Any

from flask import Blueprint, request, jsonify

# Avoid circular import
# from linars.com.warmer.kgmaker.app import app
from linars.com.warmer.kgmaker.utils.aes_util import AESUtil
from linars.com.warmer.kgmaker.utils.neo4j_util import Neo4jUtil

# Import KnowledgeGraphService if it exists
try:
    from linars.com.warmer.kgmaker.service.knowledge_graph_service import KnowledgeGraphService
except ImportError:
    # Define a placeholder class
    class KnowledgeGraphService:
        pass

# Create blueprint
webim_blueprint = Blueprint('webim', __name__, url_prefix='/webim')

# Initialize services
neo4j_util = None
kg_service = None

# Global variables
message_data = None

# Initialize logger
logger = logging.getLogger(__name__)

def init_controller(app_neo4j_util, app_kg_service, config):
    """
    Initialize the controller with required services

    Args:
        app_neo4j_util: Neo4j utility instance
        app_kg_service: Knowledge graph service instance
        config: Application configuration
    """
    global neo4j_util, kg_service
    neo4j_util = app_neo4j_util
    kg_service = app_kg_service

@webim_blueprint.route('/base', methods=['GET'])
def base():
    """
    Base endpoint for instant messaging

    Returns:
        JSON response with base data
    """
    res = {"code": 0}
    return jsonify(res)

@webim_blueprint.route('/token', methods=['GET'])
def get_token():
    """
    Get authentication token

    Returns:
        JSON response with token
    """
    import time
    key = f"000_{int(time.time() * 1000)}"

    token = AESUtil.encrypt(key)
    res = {"token": token, "code": 0}
    return jsonify(res)

@webim_blueprint.route('/send', methods=['GET'])
def get_message():
    """
    Receive message from user

    Returns:
        JSON response with status
    """
    res = {"code": 0}

    message0 = request.query_string.decode('utf-8')
    print(f"收到主人信息：{message0}")

    messages = message0.split("=")
    if len(messages) > 5:
        message = urllib.parse.unquote(messages[5]).replace("&timestamp", "")

        mms = message.split("，")

        if message != "":
            # Store message in application context
            # We'll use a global variable instead of app.message
            global message_data
            message_data = mms

    return jsonify(res)

@webim_blueprint.route('/getMessage', methods=['GET'])
def send_message():
    """
    Send message to user

    Returns:
        JSON response with message data
    """
    print("发信息给主人啦啦啦！")

    res = {
        "content": "运算，25，加，8",
        "name": "mos",
        "username": "mos",
        "id": "100001",
        "type": "friend",
        "avatar": "/images/logo/logo-3.jpg",
        "code": 0,
        "data": None
    }

    return jsonify(res)
