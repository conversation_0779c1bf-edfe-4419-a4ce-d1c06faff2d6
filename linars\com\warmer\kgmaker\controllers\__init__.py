"""
Controllers package for the Knowledge Graph Maker application.
"""

# Import all controllers
from linars.com.warmer.kgmaker.controllers.base_controller import Base<PERSON>ontroller
from linars.com.warmer.kgmaker.controllers.file_controller import file_blueprint
from linars.com.warmer.kgmaker.controllers.kg_manager_controller import kg_blueprint, init_controller
from linars.com.warmer.kgmaker.controllers.im_control_detector import webim_blueprint
from linars.com.warmer.kgmaker.controllers.nlp_controller import nlp_blueprint
from linars.com.warmer.kgmaker.controllers.question_controller import question_blueprint
from linars.com.warmer.kgmaker.controllers.xr0_controller import xr0_blueprint
from linars.com.warmer.kgmaker.controllers.xr_controller import xr_blueprint

# Export blueprints
__all__ = [
    'BaseController',
    'file_blueprint',
    'kg_blueprint',
    'init_controller',
    'webim_blueprint',
    'nlp_blueprint',
    'question_blueprint',
    'xr0_blueprint',
    'xr_blueprint'
]
