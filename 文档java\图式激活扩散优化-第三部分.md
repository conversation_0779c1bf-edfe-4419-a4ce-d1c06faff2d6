# 图式激活扩散机制优化方案 - 第三部分：任务队列与资源分配优化

> 注：本文档是图式激活扩散机制优化方案的第三部分，主要关注任务队列与资源分配优化。相关内容请参考[第一部分：深度与广度控制](./图式激活扩散优化-第一部分.md)和[第二部分：任务优先级管理](./图式激活扩散优化-第二部分.md)。

## 一、任务队列与资源分配现状

在[图式激活扩散优化-第二部分](./图式激活扩散优化-第二部分.md)中，我们已经分析了任务优先级管理的问题。本文档将继续分析任务队列的实现和资源分配策略。

### 1. 资源分配现状

当前系统的资源分配存在以下问题：

1. **资源分配不均衡**：系统没有根据任务类型和重要性进行资源分配
2. **资源容量控制失效**：如Bag队列中注释了容量控制部分，导致队列可能无限增长
3. **资源利用率低**：系统没有根据负载动态调整资源分配
4. **缺乏资源回收机制**：没有有效的资源回收和清理机制，可能导致内存泄漏
5. **简单的优先级排序**：仅基于单一优先级属性排序
6. **缺乏分类和分组**：所有任务在同一个队列中竞争
7. **缺乏过期处理**：没有处理长时间未执行的任务

### 2. 任务提交和处理

当前的任务提交和处理机制如下：

```java
public void submitTask(ActivationTask task) {
    activeTaskCount.incrementAndGet();
    executorService.execute(() -> {
        try {
            // 根据任务优先级分配到相应的任务组
            TaskGroup taskGroup = taskGroupManager.getOrCreateTaskGroup(task.getPriority());
            taskGroup.addTask(task);

            // 执行任务
            task.run();
        } finally {
            activeTaskCount.decrementAndGet();
        }
    });
}
```

这种实现存在以下问题：

1. 直接执行：任务提交后直接执行，没有经过队列的调度
2. 资源控制不足：没有限制并发任务数量，可能导致系统资源耗尽
3. 缺乏反馈机制：没有处理任务执行结果的反馈
4. 缺乏错误处理：没有处理任务执行失败的情况

## 二、优化目标

针对上述问题，我们的优化目标是：

1. 实现高效的多级任务队列，支持任务分类和分组
2. 实现智能的容量控制和过期处理
3. 实现动态的资源分配和调度策略
4. 实现完善的错误处理和恢复机制
5. 实现有效的监控和反馈机制

## 三、多级任务队列设计

### 1. 队列层次结构

设计一个多级任务队列结构，支持任务的分类和分组：

```java
public class MultiLevelTaskQueue {
    // 任务类型队列映射
    private final Map<TaskClassifier.TaskType, TypeTaskQueue> typeQueues = new HashMap<>();
    // 任务组队列映射
    private final Map<String, GroupTaskQueue> groupQueues = new HashMap<>();
    // 任务ID映射
    private final Map<String, ActivationTask> taskMap = new ConcurrentHashMap<>();
    // 队列监控器
    private final QueueMonitor queueMonitor = new QueueMonitor();

    public MultiLevelTaskQueue() {
        // 初始化各类型队列
        for (TaskClassifier.TaskType type : TaskClassifier.TaskType.values()) {
            typeQueues.put(type, new TypeTaskQueue(type));
        }
    }

    public void addTask(ActivationTask task) {
        // 添加到任务映射
        taskMap.put(task.getTaskId(), task);

        // 添加到类型队列
        TypeTaskQueue typeQueue = typeQueues.get(task.getTaskType());
        typeQueue.addTask(task);

        // 添加到组队列
        String groupId = task.getGroupId();
        if (groupId != null) {
            GroupTaskQueue groupQueue = groupQueues.computeIfAbsent(
                    groupId, id -> new GroupTaskQueue(id));
            groupQueue.addTask(task);
        }

        // 更新监控统计
        queueMonitor.recordTaskAddition(task);
    }

    public ActivationTask getNextTask() {
        // 首先检查是否有高优先级组任务
        GroupTaskQueue highestPriorityGroup = getHighestPriorityGroup();
        if (highestPriorityGroup != null && highestPriorityGroup.getGroupPriority() > 80) {
            ActivationTask task = highestPriorityGroup.getNextTask();
            if (task != null) {
                return prepareTaskForExecution(task);
            }
        }

        // 根据类型配额选择任务
        TaskClassifier.TaskType selectedType = selectTaskTypeByQuota();
        if (selectedType != null) {
            TypeTaskQueue typeQueue = typeQueues.get(selectedType);
            ActivationTask task = typeQueue.getNextTask();
            if (task != null) {
                return prepareTaskForExecution(task);
            }
        }

        // 如果没有可用任务，尝试获取任意类型的任务
        for (TypeTaskQueue typeQueue : typeQueues.values()) {
            ActivationTask task = typeQueue.getNextTask();
            if (task != null) {
                return prepareTaskForExecution(task);
            }
        }

        return null;  // 没有可用任务
    }

    private ActivationTask prepareTaskForExecution(ActivationTask task) {
        // 从所有队列中移除任务
        removeTaskFromQueues(task);

        // 更新监控统计
        queueMonitor.recordTaskExecution(task);

        return task;
    }

    private void removeTaskFromQueues(ActivationTask task) {
        // 从任务映射中移除
        taskMap.remove(task.getTaskId());

        // 从类型队列中移除
        TypeTaskQueue typeQueue = typeQueues.get(task.getTaskType());
        typeQueue.removeTask(task);

        // 从组队列中移除
        String groupId = task.getGroupId();
        if (groupId != null) {
            GroupTaskQueue groupQueue = groupQueues.get(groupId);
            if (groupQueue != null) {
                groupQueue.removeTask(task);
                if (groupQueue.isEmpty()) {
                    groupQueues.remove(groupId);
                }
            }
        }
    }
}
```

### 2. 类型队列实现

每种任务类型的队列实现：

```java
public class TypeTaskQueue {
    private final TaskClassifier.TaskType type;
    private final PriorityQueue<ActivationTask> queue;
    private final AtomicInteger taskCount = new AtomicInteger(0);
    private final int maxCapacity;

    public TypeTaskQueue(TaskClassifier.TaskType type) {
        this.type = type;
        this.queue = new PriorityQueue<>(
                (a, b) -> Integer.compare(b.getPriority(), a.getPriority()));

        // 根据类型设置容量
        switch (type) {
            case PERCEPTION:
                this.maxCapacity = 1000;  // 感知任务容量大
                break;
            case PLANNING:
                this.maxCapacity = 500;   // 计划任务容量中
                break;
            case REASONING:
                this.maxCapacity = 300;   // 推理任务容量中
                break;
            case LANGUAGE:
                this.maxCapacity = 400;   // 语言任务容量中
                break;
            case MAINTENANCE:
                this.maxCapacity = 200;   // 维护任务容量小
                break;
            default:
                this.maxCapacity = 500;   // 默认容量
        }
    }

    public synchronized void addTask(ActivationTask task) {
        // 检查容量
        if (taskCount.get() >= maxCapacity) {
            // 如果队列已满，移除优先级最低的任务
            if (!queue.isEmpty() && task.getPriority() > queue.peek().getPriority()) {
                queue.poll();
                taskCount.decrementAndGet();
            } else {
                // 新任务优先级不够高，不添加
                return;
            }
        }

        // 添加任务
        queue.offer(task);
        taskCount.incrementAndGet();
    }

    public synchronized ActivationTask getNextTask() {
        if (queue.isEmpty()) {
            return null;
        }

        // 获取优先级最高的任务
        ActivationTask task = queue.poll();
        if (task != null) {
            taskCount.decrementAndGet();
        }

        return task;
    }

    public synchronized void removeTask(ActivationTask task) {
        if (queue.remove(task)) {
            taskCount.decrementAndGet();
        }
    }

    public int getTaskCount() {
        return taskCount.get();
    }

    public boolean isEmpty() {
        return taskCount.get() == 0;
    }

    public TaskClassifier.TaskType getType() {
        return type;
    }
}
```

### 3. 组队列实现

任务组队列实现：

```java
public class GroupTaskQueue {
    private final String groupId;
    private final Set<ActivationTask> tasks = new HashSet<>();
    private int groupPriority = 0;

    public GroupTaskQueue(String groupId) {
        this.groupId = groupId;
    }

    public synchronized void addTask(ActivationTask task) {
        tasks.add(task);
        updateGroupPriority();
    }

    public synchronized ActivationTask getNextTask() {
        if (tasks.isEmpty()) {
            return null;
        }

        // 获取优先级最高的任务
        ActivationTask highestPriorityTask = Collections.max(
                tasks, Comparator.comparingInt(ActivationTask::getPriority));

        return highestPriorityTask;
    }

    public synchronized void removeTask(ActivationTask task) {
        tasks.remove(task);
        updateGroupPriority();
    }

    private void updateGroupPriority() {
        if (tasks.isEmpty()) {
            groupPriority = 0;
            return;
        }

        // 组优先级为最高任务优先级
        groupPriority = tasks.stream()
                .mapToInt(ActivationTask::getPriority)
                .max()
                .orElse(0);
    }

    public int getGroupPriority() {
        return groupPriority;
    }

    public boolean isEmpty() {
        return tasks.isEmpty();
    }

    public String getGroupId() {
        return groupId;
    }

    public int getTaskCount() {
        return tasks.size();
    }
}
```

## 四、智能容量控制与过期处理

### 1. 自适应容量控制

实现自适应的容量控制机制，根据系统负载动态调整容量：

```java
public class AdaptiveCapacityManager {
    // 容量调整间隔（毫秒）
    private final long ADJUSTMENT_INTERVAL = 5000;  // 5秒
    // 上次调整时间
    private long lastAdjustmentTime = System.currentTimeMillis();
    // 类型队列容量映射
    private final Map<TaskClassifier.TaskType, Integer> typeCapacities = new HashMap<>();
    // 系统负载管理器
    private final SystemLoadManager systemLoadManager;

    public AdaptiveCapacityManager(SystemLoadManager systemLoadManager) {
        this.systemLoadManager = systemLoadManager;

        // 初始化默认容量
        typeCapacities.put(TaskClassifier.TaskType.PERCEPTION, 1000);
        typeCapacities.put(TaskClassifier.TaskType.PLANNING, 500);
        typeCapacities.put(TaskClassifier.TaskType.REASONING, 300);
        typeCapacities.put(TaskClassifier.TaskType.LANGUAGE, 400);
        typeCapacities.put(TaskClassifier.TaskType.MAINTENANCE, 200);
    }

    public int getCapacity(TaskClassifier.TaskType type) {
        return typeCapacities.getOrDefault(type, 500);
    }

    public void adjustCapacities(Map<TaskClassifier.TaskType, Integer> queueSizes) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastAdjustmentTime < ADJUSTMENT_INTERVAL) {
            return;  // 未到调整时间
        }

        // 获取系统负载
        double systemLoad = systemLoadManager.getSystemLoad();

        // 根据负载调整容量
        for (Map.Entry<TaskClassifier.TaskType, Integer> entry : typeCapacities.entrySet()) {
            TaskClassifier.TaskType type = entry.getKey();
            int currentCapacity = entry.getValue();
            int currentSize = queueSizes.getOrDefault(type, 0);

            // 计算新容量
            int newCapacity = calculateNewCapacity(
                    type, currentCapacity, currentSize, systemLoad);

            // 更新容量
            typeCapacities.put(type, newCapacity);
        }

        lastAdjustmentTime = currentTime;
    }

    private int calculateNewCapacity(TaskClassifier.TaskType type,
                                    int currentCapacity,
                                    int currentSize,
                                    double systemLoad) {
        // 基础容量调整因子
        double adjustmentFactor;

        if (systemLoad > 0.8) {
            // 高负载，减小容量
            adjustmentFactor = 0.8;
        } else if (systemLoad > 0.5) {
            // 中等负载，维持容量
            adjustmentFactor = 1.0;
        } else {
            // 低负载，增加容量
            adjustmentFactor = 1.2;
        }

        // 根据队列使用率调整
        double utilizationRatio = currentSize / (double)currentCapacity;
        if (utilizationRatio > 0.9) {
            // 队列快满了，增加容量
            adjustmentFactor *= 1.2;
        } else if (utilizationRatio < 0.3) {
            // 队列很空，减小容量
            adjustmentFactor *= 0.9;
        }

        // 根据任务类型调整
        switch (type) {
            case PERCEPTION:
                // 感知任务优先级高，保持较大容量
                adjustmentFactor *= 1.1;
                break;
            case PLANNING:
                // 计划任务优先级高，保持较大容量
                adjustmentFactor *= 1.05;
                break;
            case MAINTENANCE:
                // 维护任务优先级低，容量可以小一些
                adjustmentFactor *= 0.9;
                break;
        }

        // 计算新容量
        int newCapacity = (int)(currentCapacity * adjustmentFactor);

        // 确保容量在合理范围内
        int minCapacity = getMinCapacity(type);
        int maxCapacity = getMaxCapacity(type);
        return Math.max(minCapacity, Math.min(newCapacity, maxCapacity));
    }

    private int getMinCapacity(TaskClassifier.TaskType type) {
        // 根据类型返回最小容量
        switch (type) {
            case PERCEPTION: return 500;
            case PLANNING: return 200;
            case REASONING: return 100;
            case LANGUAGE: return 200;
            case MAINTENANCE: return 50;
            default: return 100;
        }
    }

    private int getMaxCapacity(TaskClassifier.TaskType type) {
        // 根据类型返回最大容量
        switch (type) {
            case PERCEPTION: return 2000;
            case PLANNING: return 1000;
            case REASONING: return 800;
            case LANGUAGE: return 1000;
            case MAINTENANCE: return 500;
            default: return 1000;
        }
    }
}
```

### 2. 任务过期处理

实现任务过期处理机制，清理长时间未执行的任务：

```java
public class TaskExpirationManager {
    // 过期检查间隔（毫秒）
    private final long EXPIRATION_CHECK_INTERVAL = 10000;  // 10秒
    // 上次检查时间
    private long lastCheckTime = System.currentTimeMillis();
    // 任务创建时间映射
    private final Map<String, Long> taskCreationTimes = new ConcurrentHashMap<>();
    // 任务过期时间（毫秒）
    private final Map<TaskClassifier.TaskType, Long> expirationTimes = new HashMap<>();

    public TaskExpirationManager() {
        // 初始化各类型任务的过期时间
        expirationTimes.put(TaskClassifier.TaskType.PERCEPTION, 5000L);    // 5秒
        expirationTimes.put(TaskClassifier.TaskType.PLANNING, 30000L);     // 30秒
        expirationTimes.put(TaskClassifier.TaskType.REASONING, 60000L);    // 60秒
        expirationTimes.put(TaskClassifier.TaskType.LANGUAGE, 20000L);     // 20秒
        expirationTimes.put(TaskClassifier.TaskType.MAINTENANCE, 120000L); // 120秒
    }

    public void recordTaskCreation(String taskId) {
        taskCreationTimes.put(taskId, System.currentTimeMillis());
    }

    public void removeTask(String taskId) {
        taskCreationTimes.remove(taskId);
    }

    public boolean isTaskExpired(String taskId, TaskClassifier.TaskType type) {
        Long creationTime = taskCreationTimes.get(taskId);
        if (creationTime == null) {
            return false;  // 任务不存在
        }

        long expirationTime = expirationTimes.getOrDefault(type, 30000L);
        return System.currentTimeMillis() - creationTime > expirationTime;
    }

    public List<String> checkExpiredTasks(MultiLevelTaskQueue taskQueue) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCheckTime < EXPIRATION_CHECK_INTERVAL) {
            return Collections.emptyList();  // 未到检查时间
        }

        List<String> expiredTaskIds = new ArrayList<>();

        // 检查所有任务
        for (Map.Entry<String, Long> entry : taskCreationTimes.entrySet()) {
            String taskId = entry.getKey();
            long creationTime = entry.getValue();

            // 获取任务
            ActivationTask task = taskQueue.getTaskById(taskId);
            if (task == null) {
                // 任务不存在，移除记录
                taskCreationTimes.remove(taskId);
                continue;
            }

            // 检查是否过期
            TaskClassifier.TaskType type = task.getTaskType();
            long expirationTime = expirationTimes.getOrDefault(type, 30000L);

            if (currentTime - creationTime > expirationTime) {
                expiredTaskIds.add(taskId);
            }
        }

        lastCheckTime = currentTime;
        return expiredTaskIds;
    }

    public void processExpiredTasks(MultiLevelTaskQueue taskQueue, List<String> expiredTaskIds) {
        for (String taskId : expiredTaskIds) {
            // 获取任务
            ActivationTask task = taskQueue.getTaskById(taskId);
            if (task == null) {
                continue;
            }

            // 处理过期任务
            handleExpiredTask(task, taskQueue);

            // 移除记录
            taskCreationTimes.remove(taskId);
        }
    }

    private void handleExpiredTask(ActivationTask task, MultiLevelTaskQueue taskQueue) {
        // 根据任务类型和优先级决定处理方式
        TaskClassifier.TaskType type = task.getTaskType();
        int priority = task.getPriority();

        if (priority > 80) {
            // 高优先级任务，尝试立即执行
            executeExpiredHighPriorityTask(task);
        } else if (priority > 50) {
            // 中等优先级任务，降低优先级并保留
            task.setPriority(task.getPriority() - 10);
            // 重新加入队列
            taskQueue.removeTask(task.getTaskId());
            taskQueue.addTask(task);
            // 更新创建时间
            recordTaskCreation(task.getTaskId());
        } else {
            // 低优先级任务，直接移除
            taskQueue.removeTask(task.getTaskId());
        }
    }

    private void executeExpiredHighPriorityTask(ActivationTask task) {
        // 创建一个新线程执行高优先级过期任务
        Thread executionThread = new Thread(() -> {
            try {
                task.run();
            } catch (Exception e) {
                // 记录错误
                System.err.println("Error executing expired high priority task: " + e.getMessage());
            }
        });
        executionThread.setDaemon(true);
        executionThread.start();
    }
}
```

## 五、资源分配与调度策略

### 1. 资源分配策略

实现智能的资源分配策略，确保各类任务都能获得合理的资源：

```java
public class ResourceAllocationStrategy {
    // 类型资源配额（百分比）
    private final Map<TaskClassifier.TaskType, Integer> typeQuotas = new HashMap<>();
    // 类型当前使用资源
    private final Map<TaskClassifier.TaskType, AtomicInteger> typeUsage = new HashMap<>();
    // 总资源容量
    private final int totalCapacity;
    // 当前使用资源
    private final AtomicInteger currentUsage = new AtomicInteger(0);
    // 资源调整间隔（毫秒）
    private final long ADJUSTMENT_INTERVAL = 5000;  // 5秒
    // 上次调整时间
    private long lastAdjustmentTime = System.currentTimeMillis();

    public ResourceAllocationStrategy(int totalCapacity) {
        this.totalCapacity = totalCapacity;

        // 初始化默认配额
        typeQuotas.put(TaskClassifier.TaskType.PERCEPTION, 30);    // 30%
        typeQuotas.put(TaskClassifier.TaskType.PLANNING, 25);      // 25%
        typeQuotas.put(TaskClassifier.TaskType.REASONING, 20);     // 20%
        typeQuotas.put(TaskClassifier.TaskType.LANGUAGE, 15);      // 15%
        typeQuotas.put(TaskClassifier.TaskType.MAINTENANCE, 10);   // 10%

        // 初始化资源使用计数
        for (TaskClassifier.TaskType type : TaskClassifier.TaskType.values()) {
            typeUsage.put(type, new AtomicInteger(0));
        }
    }

    public boolean canAllocateResource(TaskClassifier.TaskType taskType) {
        // 检查总资源
        if (currentUsage.get() >= totalCapacity) {
            return false;  // 总资源不足
        }

        // 计算当前类型的资源配额
        int quota = (int)(totalCapacity * typeQuotas.get(taskType) / 100.0);

        // 检查是否超过配额
        return typeUsage.get(taskType).get() < quota;
    }

    public void allocateResource(TaskClassifier.TaskType taskType) {
        typeUsage.get(taskType).incrementAndGet();
        currentUsage.incrementAndGet();
    }

    public void releaseResource(TaskClassifier.TaskType taskType) {
        typeUsage.get(taskType).decrementAndGet();
        currentUsage.decrementAndGet();
    }

    public void adjustQuotas(Map<TaskClassifier.TaskType, Integer> executionCounts) {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastAdjustmentTime < ADJUSTMENT_INTERVAL) {
            return;  // 未到调整时间
        }

        // 计算总执行次数
        int totalExecutions = executionCounts.values().stream().mapToInt(Integer::intValue).sum();
        if (totalExecutions == 0) {
            return;  // 无执行记录，不调整
        }

        // 计算新配额
        Map<TaskClassifier.TaskType, Integer> newQuotas = new HashMap<>();
        int remainingQuota = 100;

        // 确保每种类型至少有5%的配额
        for (TaskClassifier.TaskType type : TaskClassifier.TaskType.values()) {
            newQuotas.put(type, 5);
            remainingQuota -= 5;
        }

        // 根据执行比例分配剩余配额
        for (Map.Entry<TaskClassifier.TaskType, Integer> entry : executionCounts.entrySet()) {
            double proportion = (double)entry.getValue() / totalExecutions;
            int additionalQuota = (int)(remainingQuota * proportion);
            newQuotas.compute(entry.getKey(), (k, v) -> v + additionalQuota);
        }

        // 确保总和为100%
        int sum = newQuotas.values().stream().mapToInt(Integer::intValue).sum();
        if (sum != 100) {
            // 调整最大配额类型
            TaskClassifier.TaskType maxType = Collections.max(
                    newQuotas.entrySet(),
                    Map.Entry.comparingByValue()
            ).getKey();
            newQuotas.compute(maxType, (k, v) -> v + (100 - sum));
        }

        // 更新配额
        typeQuotas.putAll(newQuotas);

        lastAdjustmentTime = currentTime;
    }

    public Map<TaskClassifier.TaskType, Integer> getTypeQuotas() {
        return new HashMap<>(typeQuotas);
    }

    public Map<TaskClassifier.TaskType, Integer> getTypeUsage() {
        Map<TaskClassifier.TaskType, Integer> usage = new HashMap<>();
        for (Map.Entry<TaskClassifier.TaskType, AtomicInteger> entry : typeUsage.entrySet()) {
            usage.put(entry.getKey(), entry.getValue().get());
        }
        return usage;
    }

    public int getTotalCapacity() {
        return totalCapacity;
    }

    public int getCurrentUsage() {
        return currentUsage.get();
    }
}
```

### 2. 任务调度器

实现智能的任务调度器，管理任务的执行：

```java
public class TaskScheduler {
    // 多级任务队列
    private final MultiLevelTaskQueue taskQueue;
    // 资源分配策略
    private final ResourceAllocationStrategy resourceStrategy;
    // 任务过期管理器
    private final TaskExpirationManager expirationManager;
    // 自适应容量管理器
    private final AdaptiveCapacityManager capacityManager;
    // 执行器服务
    private final ExecutorService executorService;
    // 执行统计
    private final Map<TaskClassifier.TaskType, AtomicInteger> executionCounts = new HashMap<>();
    // 调度器运行标志
    private volatile boolean running = false;
    // 调度线程
    private Thread schedulerThread;

    public TaskScheduler(int threadPoolSize) {
        this.taskQueue = new MultiLevelTaskQueue();
        this.resourceStrategy = new ResourceAllocationStrategy(threadPoolSize);
        this.expirationManager = new TaskExpirationManager();
        this.capacityManager = new AdaptiveCapacityManager(new SystemLoadManager());
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);

        // 初始化执行统计
        for (TaskClassifier.TaskType type : TaskClassifier.TaskType.values()) {
            executionCounts.put(type, new AtomicInteger(0));
        }
    }

    public void start() {
        if (running) {
            return;
        }

        running = true;
        schedulerThread = new Thread(this::schedulerLoop);
        schedulerThread.setDaemon(true);
        schedulerThread.start();
    }

    public void stop() {
        running = false;
        if (schedulerThread != null) {
            schedulerThread.interrupt();
        }
    }

    public void submitTask(ActivationTask task) {
        // 记录任务创建时间
        expirationManager.recordTaskCreation(task.getTaskId());

        // 添加到队列
        taskQueue.addTask(task);
    }

    private void schedulerLoop() {
        while (running) {
            try {
                // 处理过期任务
                List<String> expiredTaskIds = expirationManager.checkExpiredTasks(taskQueue);
                if (!expiredTaskIds.isEmpty()) {
                    expirationManager.processExpiredTasks(taskQueue, expiredTaskIds);
                }

                // 调整队列容量
                Map<TaskClassifier.TaskType, Integer> queueSizes = taskQueue.getQueueSizes();
                capacityManager.adjustCapacities(queueSizes);

                // 调整资源配额
                Map<TaskClassifier.TaskType, Integer> execCounts = new HashMap<>();
                for (Map.Entry<TaskClassifier.TaskType, AtomicInteger> entry : executionCounts.entrySet()) {
                    execCounts.put(entry.getKey(), entry.getValue().get());
                }
                resourceStrategy.adjustQuotas(execCounts);

                // 获取下一个要执行的任务
                ActivationTask nextTask = taskQueue.getNextTask();
                if (nextTask != null) {
                    // 检查资源是否可用
                    TaskClassifier.TaskType taskType = nextTask.getTaskType();
                    if (resourceStrategy.canAllocateResource(taskType)) {
                        // 分配资源
                        resourceStrategy.allocateResource(taskType);

                        // 提交任务执行
                        executeTask(nextTask);
                    } else {
                        // 资源不足，将任务放回队列
                        taskQueue.addTask(nextTask);
                    }
                }

                // 等待一小段时间
                Thread.sleep(10);  // 10毫秒

            } catch (InterruptedException e) {
                // 线程被中断，检查是否应该继续运行
                if (!running) {
                    break;
                }
            } catch (Exception e) {
                // 处理其他异常
                System.err.println("Error in scheduler loop: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private void executeTask(ActivationTask task) {
        // 提交任务到执行器
        executorService.execute(() -> {
            try {
                // 执行任务
                task.run();

                // 更新执行统计
                executionCounts.get(task.getTaskType()).incrementAndGet();

            } catch (Exception e) {
                // 处理执行异常
                System.err.println("Error executing task: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // 释放资源
                resourceStrategy.releaseResource(task.getTaskType());

                // 移除任务记录
                expirationManager.removeTask(task.getTaskId());
            }
        });
    }

    public Map<TaskClassifier.TaskType, Integer> getExecutionStatistics() {
        Map<TaskClassifier.TaskType, Integer> stats = new HashMap<>();
        for (Map.Entry<TaskClassifier.TaskType, AtomicInteger> entry : executionCounts.entrySet()) {
            stats.put(entry.getKey(), entry.getValue().get());
        }
        return stats;
    }

    public Map<TaskClassifier.TaskType, Integer> getResourceQuotas() {
        return resourceStrategy.getTypeQuotas();
    }

    public Map<TaskClassifier.TaskType, Integer> getResourceUsage() {
        return resourceStrategy.getTypeUsage();
    }

    public int getQueueSize() {
        return taskQueue.getTotalSize();
    }

    public void resetStatistics() {
        for (AtomicInteger count : executionCounts.values()) {
            count.set(0);
        }
    }
}
```

## 六、监控与反馈机制

### 1. 系统监控器

实现系统监控器，收集和分析系统运行数据：

```java
public class SystemMonitor {
    // 监控数据收集间隔（毫秒）
    private final long COLLECTION_INTERVAL = 1000;  // 1秒
    // 上次收集时间
    private long lastCollectionTime = System.currentTimeMillis();
    // 任务调度器
    private final TaskScheduler taskScheduler;
    // 系统负载管理器
    private final SystemLoadManager systemLoadManager;
    // 历史数据存储
    private final LinkedList<MonitoringData> historyData = new LinkedList<>();
    // 历史数据最大存储数量
    private final int MAX_HISTORY_SIZE = 60;  // 存储60个数据点，相当于1分钟的数据
    // 监控器运行标志
    private volatile boolean running = false;
    // 监控线程
    private Thread monitorThread;
    // 监控数据监听器
    private final List<MonitoringDataListener> listeners = new ArrayList<>();

    public SystemMonitor(TaskScheduler taskScheduler, SystemLoadManager systemLoadManager) {
        this.taskScheduler = taskScheduler;
        this.systemLoadManager = systemLoadManager;
    }

    public void start() {
        if (running) {
            return;
        }

        running = true;
        monitorThread = new Thread(this::monitorLoop);
        monitorThread.setDaemon(true);
        monitorThread.start();
    }

    public void stop() {
        running = false;
        if (monitorThread != null) {
            monitorThread.interrupt();
        }
    }

    public void addListener(MonitoringDataListener listener) {
        listeners.add(listener);
    }

    public void removeListener(MonitoringDataListener listener) {
        listeners.remove(listener);
    }

    private void monitorLoop() {
        while (running) {
            try {
                // 检查是否到收集时间
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastCollectionTime >= COLLECTION_INTERVAL) {
                    // 收集监控数据
                    MonitoringData data = collectMonitoringData();

                    // 存储历史数据
                    historyData.addLast(data);
                    if (historyData.size() > MAX_HISTORY_SIZE) {
                        historyData.removeFirst();
                    }

                    // 通知监听器
                    notifyListeners(data);

                    lastCollectionTime = currentTime;
                }

                // 等待一小段时间
                Thread.sleep(100);  // 100毫秒

            } catch (InterruptedException e) {
                // 线程被中断，检查是否应该继续运行
                if (!running) {
                    break;
                }
            } catch (Exception e) {
                // 处理其他异常
                System.err.println("Error in monitor loop: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    private MonitoringData collectMonitoringData() {
        // 创建监控数据对象
        MonitoringData data = new MonitoringData();
        data.timestamp = System.currentTimeMillis();

        // 收集系统负载数据
        data.systemLoad = systemLoadManager.getSystemLoad();
        data.cpuUsage = getCpuUsage();
        data.memoryUsage = getMemoryUsage();

        // 收集任务队列数据
        data.queueSize = taskScheduler.getQueueSize();
        data.queueSizeByType = taskScheduler.getQueueSizeByType();

        // 收集资源使用数据
        data.resourceUsage = taskScheduler.getResourceUsage();
        data.resourceQuotas = taskScheduler.getResourceQuotas();

        // 收集执行统计数据
        data.executionStats = taskScheduler.getExecutionStatistics();

        return data;
    }

    private void notifyListeners(MonitoringData data) {
        for (MonitoringDataListener listener : listeners) {
            try {
                listener.onNewMonitoringData(data);
            } catch (Exception e) {
                System.err.println("Error notifying listener: " + e.getMessage());
            }
        }
    }

    private double getCpuUsage() {
        // 获取CPU使用率
        // 实际实现可能需要使用JMX或其他系统监控工具
        try {
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            if (osBean instanceof com.sun.management.OperatingSystemMXBean) {
                com.sun.management.OperatingSystemMXBean sunOsBean =
                        (com.sun.management.OperatingSystemMXBean) osBean;
                return sunOsBean.getProcessCpuLoad();
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return -1.0;  // 无法获取
    }

    private double getMemoryUsage() {
        // 获取内存使用率
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        return (double) (totalMemory - freeMemory) / totalMemory;
    }

    public List<MonitoringData> getHistoryData() {
        return new ArrayList<>(historyData);
    }

    public MonitoringData getLatestData() {
        if (historyData.isEmpty()) {
            return collectMonitoringData();
        }
        return historyData.getLast();
    }

    public static class MonitoringData {
        public long timestamp;
        public double systemLoad;
        public double cpuUsage;
        public double memoryUsage;
        public int queueSize;
        public Map<TaskClassifier.TaskType, Integer> queueSizeByType;
        public Map<TaskClassifier.TaskType, Integer> resourceUsage;
        public Map<TaskClassifier.TaskType, Integer> resourceQuotas;
        public Map<TaskClassifier.TaskType, Integer> executionStats;
    }

    public interface MonitoringDataListener {
        void onNewMonitoringData(MonitoringData data);
    }
}
```

### 2. 性能分析器

实现性能分析器，分析系统性能并提供优化建议：

```java
public class PerformanceAnalyzer implements SystemMonitor.MonitoringDataListener {
    // 系统监控器
    private final SystemMonitor systemMonitor;
    // 任务调度器
    private final TaskScheduler taskScheduler;
    // 分析间隔（毫秒）
    private final long ANALYSIS_INTERVAL = 10000;  // 10秒
    // 上次分析时间
    private long lastAnalysisTime = System.currentTimeMillis();
    // 性能问题监听器
    private final List<PerformanceIssueListener> listeners = new ArrayList<>();

    public PerformanceAnalyzer(SystemMonitor systemMonitor, TaskScheduler taskScheduler) {
        this.systemMonitor = systemMonitor;
        this.taskScheduler = taskScheduler;
        this.systemMonitor.addListener(this);
    }

    public void addListener(PerformanceIssueListener listener) {
        listeners.add(listener);
    }

    public void removeListener(PerformanceIssueListener listener) {
        listeners.remove(listener);
    }

    @Override
    public void onNewMonitoringData(SystemMonitor.MonitoringData data) {
        // 检查是否到分析时间
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastAnalysisTime >= ANALYSIS_INTERVAL) {
            // 执行性能分析
            List<PerformanceIssue> issues = analyzePerformance();

            // 通知监听器
            if (!issues.isEmpty()) {
                notifyListeners(issues);
            }

            lastAnalysisTime = currentTime;
        }
    }

    private List<PerformanceIssue> analyzePerformance() {
        List<PerformanceIssue> issues = new ArrayList<>();
        List<SystemMonitor.MonitoringData> historyData = systemMonitor.getHistoryData();

        if (historyData.isEmpty()) {
            return issues;
        }

        // 获取最新数据
        SystemMonitor.MonitoringData latestData = historyData.get(historyData.size() - 1);

        // 分析系统负载
        if (latestData.systemLoad > 0.9) {
            issues.add(new PerformanceIssue(
                    PerformanceIssueType.HIGH_SYSTEM_LOAD,
                    "System load is very high: " + String.format("%.2f", latestData.systemLoad),
                    "Consider reducing the number of concurrent tasks or increasing system resources."));
        }

        // 分析队列大小
        if (latestData.queueSize > 10000) {
            issues.add(new PerformanceIssue(
                    PerformanceIssueType.LARGE_QUEUE_SIZE,
                    "Task queue is very large: " + latestData.queueSize,
                    "Consider increasing processing capacity or reducing task generation rate."));
        }

        // 分析队列增长趋势
        if (historyData.size() >= 5) {
            SystemMonitor.MonitoringData oldData = historyData.get(historyData.size() - 5);
            int queueGrowth = latestData.queueSize - oldData.queueSize;
            if (queueGrowth > 1000) {
                issues.add(new PerformanceIssue(
                        PerformanceIssueType.QUEUE_GROWTH_TREND,
                        "Task queue is growing rapidly: +" + queueGrowth + " in 5 seconds",
                        "Task generation rate exceeds processing capacity. Consider optimizing task processing or reducing generation rate."));
            }
        }

        // 分析资源使用不平衡
        Map<TaskClassifier.TaskType, Integer> quotas = latestData.resourceQuotas;
        Map<TaskClassifier.TaskType, Integer> usage = latestData.resourceUsage;

        for (Map.Entry<TaskClassifier.TaskType, Integer> entry : quotas.entrySet()) {
            TaskClassifier.TaskType type = entry.getKey();
            int quota = entry.getValue();
            int used = usage.getOrDefault(type, 0);

            // 检查资源使用率
            double utilizationRatio = quota > 0 ? (double)used / quota : 0;

            if (utilizationRatio > 0.95) {
                issues.add(new PerformanceIssue(
                        PerformanceIssueType.RESOURCE_QUOTA_EXHAUSTION,
                        "Resource quota for " + type + " is nearly exhausted: " +
                                String.format("%.2f%%", utilizationRatio * 100),
                        "Consider increasing the quota for this task type or optimizing its processing."));
            } else if (utilizationRatio < 0.1 && quota > 10) {
                issues.add(new PerformanceIssue(
                        PerformanceIssueType.RESOURCE_UNDERUTILIZATION,
                        "Resource quota for " + type + " is underutilized: " +
                                String.format("%.2f%%", utilizationRatio * 100),
                        "Consider reducing the quota for this task type to free resources for other types."));
            }
        }

        // 分析任务类型分布
        Map<TaskClassifier.TaskType, Integer> queueSizes = latestData.queueSizeByType;
        int totalQueueSize = latestData.queueSize;

        if (totalQueueSize > 1000) {
            for (Map.Entry<TaskClassifier.TaskType, Integer> entry : queueSizes.entrySet()) {
                TaskClassifier.TaskType type = entry.getKey();
                int size = entry.getValue();

                // 检查是否有某类型任务占据了大部分队列
                double proportion = totalQueueSize > 0 ? (double)size / totalQueueSize : 0;

                if (proportion > 0.8) {
                    issues.add(new PerformanceIssue(
                            PerformanceIssueType.TASK_TYPE_IMBALANCE,
                            type + " tasks dominate the queue: " +
                                    String.format("%.2f%%", proportion * 100),
                            "Consider optimizing the processing of this task type or adjusting its generation rate."));
                }
            }
        }

        return issues;
    }

    private void notifyListeners(List<PerformanceIssue> issues) {
        for (PerformanceIssueListener listener : listeners) {
            try {
                listener.onPerformanceIssuesDetected(issues);
            } catch (Exception e) {
                System.err.println("Error notifying performance issue listener: " + e.getMessage());
            }
        }
    }

    public enum PerformanceIssueType {
        HIGH_SYSTEM_LOAD,
        LARGE_QUEUE_SIZE,
        QUEUE_GROWTH_TREND,
        RESOURCE_QUOTA_EXHAUSTION,
        RESOURCE_UNDERUTILIZATION,
        TASK_TYPE_IMBALANCE
    }

    public static class PerformanceIssue {
        private final PerformanceIssueType type;
        private final String description;
        private final String recommendation;

        public PerformanceIssue(PerformanceIssueType type, String description, String recommendation) {
            this.type = type;
            this.description = description;
            this.recommendation = recommendation;
        }

        public PerformanceIssueType getType() {
            return type;
        }

        public String getDescription() {
            return description;
        }

        public String getRecommendation() {
            return recommendation;
        }
    }

    public interface PerformanceIssueListener {
        void onPerformanceIssuesDetected(List<PerformanceIssue> issues);
    }
}
```

## 七、总结

本优化方案针对图式激活扩散机制的任务队列管理提出了一系列改进措施：

1. 多级任务队列：实现了支持任务分类和分组的多级队列结构，提高任务管理的灵活性和效率

2. 智能容量控制：实现了自适应的容量控制机制，根据系统负载和队列使用率动态调整容量

3. 任务过期处理：实现了任务过期处理机制，清理长时间未执行的任务，防止队列堆积

4. 资源分配策略：实现了智能的资源分配策略，确保各类任务都能获得合理的资源

5. 任务调度器：实现了智能的任务调度器，管理任务的执行和资源分配

6. 监控与反馈机制：实现了系统监控器和性能分析器，提供实时监控和性能优化建议

这些优化措施将显著提高图式激活扩散机制的效率和可靠性，解决当前实现中的任务堆积和资源竞争问题，确保重要任务能够及时执行，从而提升系统的整体性能和响应能力。

通过实施这些优化措施，图式激活扩散机制将能够更高效、更智能地处理复杂的认知任务，为AGI大脑系统提供更强大的认知能力。
```