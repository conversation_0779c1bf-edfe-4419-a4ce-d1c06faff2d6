## 三、操作与底层API关联

在综合优化版本中，我们认识到操作节点需要与底层API建立关联，而不是为每个操作都创建独立的实体类。同时，我们也认识到像"计算当前位和进位"这样的节点实际上是一套完整的子图式，只是尚未分解到底层API。以下是多位数加法中各操作与底层API的关联方式：

### 1. 初始化操作

初始化操作设置必要的变量和初始状态，关联到变量操作API：

```java
// 操作节点定义
OperationNode initOperation = new OperationNode(
    "10",
    "初始化",
    "initialization",
    Arrays.asList("操作数1", "操作数2")
);

// 底层API关联
initOperation.setApiReference("variable/basic/set_multiple");
```

实际执行时，系统会调用底层的变量操作API：

```java
// 底层API实现（仅少量核心API需要实现为实体类）
public class VariableOperations {
    public static class SetMultiple implements Operation {
        @Override
        public Object execute(ExecutionContext context) {
            // 从上下文获取操作数
            String operand1 = (String) context.getVariable("操作数1");
            String operand2 = (String) context.getVariable("操作数2");

            // 设置初始变量
            context.setVariable("结果", "");
            context.setVariable("进位", 0);
            context.setVariable("当前位索引", Math.min(operand1.length(), operand2.length()) - 1);

            return null;
        }
    }
}
```

### 2. 获取当前位操作

获取当前位操作从操作数中获取当前处理的位，可能关联到字符串操作API和记忆搜索API。与"计算当前位和进位"操作类似，这个操作也可以被视为一个完整的子图式：

```java
// 操作节点定义
OperationNode getCurrentDigit = new OperationNode(
    "11",
    "获取当前位",
    "data_access",
    Arrays.asList("操作数1", "操作数2", "当前位索引")
);

// 底层API关联
getCurrentDigit.setApiReference("string/basic/get_char_at");
// 或者关联到记忆搜索API
getCurrentDigit.setApiReference("memory/search/digit_position");
// 或者关联到子图式
getCurrentDigit.setSchemaReference("string/schemas/get_digit_schema");
```

#### 2.1 作为子图式的实现

当"获取当前位"被视为子图式时，它可以有自己的内部结构：

```java
// 创建子图式
ExecutableSchema getDigitSchema = new ExecutableSchema("get_digit_schema", "获取数字位图式");

// 创建上下文节点
ContextNode getDigitContext = new ContextNode("1", "获取数字位", "local");
getDigitSchema.setContextNode(getDigitContext);

// 创建操作节点
OperationNode searchMemory = new OperationNode("2", "搜索记忆", "memory_access",
        Arrays.asList("操作数1", "操作数2", "当前位索引"));
searchMemory.setApiReference("memory/search/digit_position");

OperationNode checkOperand1Length = new OperationNode("3", "检查操作数1长度", "comparison",
        Arrays.asList("操作数1", "当前位索引"));
checkOperand1Length.setApiReference("string/basic/check_index_in_bounds");

OperationNode getDigit1 = new OperationNode("4", "获取位1", "data_access",
        Arrays.asList("操作数1", "当前位索引"));
getDigit1.setApiReference("string/basic/get_digit_at");

OperationNode setDigit1Zero = new OperationNode("5", "设置位1为0", "data_update");
setDigit1Zero.setApiReference("variable/basic/set_zero");

OperationNode checkOperand2Length = new OperationNode("6", "检查操作数2长度", "comparison",
        Arrays.asList("操作数2", "当前位索引"));
checkOperand2Length.setApiReference("string/basic/check_index_in_bounds");

OperationNode getDigit2 = new OperationNode("7", "获取位2", "data_access",
        Arrays.asList("操作数2", "当前位索引"));
getDigit2.setApiReference("string/basic/get_digit_at");

OperationNode setDigit2Zero = new OperationNode("8", "设置位2为0", "data_update");
setDigit2Zero.setApiReference("variable/basic/set_zero");

OperationNode cacheResult = new OperationNode("9", "缓存结果", "memory_update",
        Arrays.asList("操作数1", "操作数2", "当前位索引", "位1", "位2"));
cacheResult.setApiReference("memory/cache/store_digits");

// 创建控制节点
ControlNode memoryCheckNode = new ControlNode("10", "检查记忆", "conditional");
ControlNode operand1LengthCheckNode = new ControlNode("11", "检查操作数1长度", "conditional");
ControlNode operand2LengthCheckNode = new ControlNode("12", "检查操作数2长度", "conditional");

// 添加节点到图式
// ... (省略添加节点的代码)

// 创建时序边和顺承边
// ... (省略创建边的代码)
```

这个子图式展示了"获取当前位"操作的内部实现，它包含以下步骤：

1. **搜索记忆**：首先尝试从记忆中搜索结果
2. **检查记忆**：检查是否找到记忆中的结果
3. **检查操作数1长度**：检查当前位索引是否在操作数1的长度范围内
4. **获取位1**：如果在范围内，获取操作数1的当前位
5. **设置位1为0**：如果不在范围内，将位1设置为0
6. **检查操作数2长度**：检查当前位索引是否在操作数2的长度范围内
7. **获取位2**：如果在范围内，获取操作数2的当前位
8. **设置位2为0**：如果不在范围内，将位2设置为0
9. **缓存结果**：将结果缓存到记忆中供未来使用

这种实现展示了如何将一个看似简单的操作分解为更细粒度的步骤，每个步骤都关联到底层API。

#### 2.2 执行逻辑

在实际执行中，系统可能首先尝试从记忆中搜索结果，如果找不到才执行计算：

```java
// 执行逻辑（简化版）
public Object executeGetCurrentDigit(ExecutionContext context) {
    String operand1 = (String) context.getVariable("操作数1");
    String operand2 = (String) context.getVariable("操作数2");
    int currentIndex = (int) context.getVariable("当前位索引");

    // 首先尝试从记忆中搜索
    String searchKey = "digit_at_" + operand1 + "_" + operand2 + "_" + currentIndex;
    if (context.hasCachedVariable(searchKey)) {
        // 如果找到缓存结果，直接使用
        Map<String, Object> result = (Map<String, Object>) context.getCachedVariable(searchKey);
        context.setVariable("位1", result.get("digit1"));
        context.setVariable("位2", result.get("digit2"));
        return result;
    }

    // 如果没有缓存，执行计算
    int digit1 = (currentIndex < operand1.length()) ?
                 Character.getNumericValue(operand1.charAt(operand1.length() - 1 - currentIndex)) : 0;
    int digit2 = (currentIndex < operand2.length()) ?
                 Character.getNumericValue(operand2.charAt(operand2.length() - 1 - currentIndex)) : 0;

    context.setVariable("位1", digit1);
    context.setVariable("位2", digit2);

    // 缓存结果供未来使用
    Map<String, Object> result = new HashMap<>();
    result.put("digit1", digit1);
    result.put("digit2", digit2);
    context.cacheVariable(searchKey, result);

    return result;
}
```

### 3. 计算当前位和进位操作

计算当前位和进位操作执行加法计算，关联到数学运算API和记忆搜索API。值得注意的是，这个操作本身可以被视为一个完整的子图式，它可以进一步分解为更基本的操作，直到最终关联到底层API：

```java
// 操作节点定义
OperationNode calculateDigitAndCarry = new OperationNode(
    "12",
    "计算当前位和进位",
    "arithmetic",
    Arrays.asList("位1", "位2", "进位")
);

// 底层API关联
calculateDigitAndCarry.setApiReference("math/basic/add_with_carry");
// 或者关联到记忆搜索API
calculateDigitAndCarry.setApiReference("memory/search/digit_addition");
// 或者关联到子图式
calculateDigitAndCarry.setSchemaReference("math/schemas/digit_addition_schema");
```

#### 3.1 作为子图式的实现

当"计算当前位和进位"被视为子图式时，它可以有自己的内部结构，包含更细粒度的操作节点：

```java
// 创建子图式
ExecutableSchema digitAdditionSchema = new ExecutableSchema("digit_addition_schema", "数字加法图式");

// 创建上下文节点
ContextNode digitAdditionContext = new ContextNode("1", "数字加法", "local");
digitAdditionSchema.setContextNode(digitAdditionContext);

// 创建操作节点
OperationNode searchMemory = new OperationNode("2", "搜索记忆", "memory_access",
        Arrays.asList("位1", "位2", "进位"));
searchMemory.setApiReference("memory/search/basic_addition");

OperationNode performAddition = new OperationNode("3", "执行加法", "arithmetic",
        Arrays.asList("位1", "位2", "进位"));
performAddition.setApiReference("math/basic/add");

OperationNode calculateResult = new OperationNode("4", "计算结果", "arithmetic",
        Arrays.asList("和"));
calculateResult.setApiReference("math/basic/mod_10");

OperationNode calculateNewCarry = new OperationNode("5", "计算新进位", "arithmetic",
        Arrays.asList("和"));
calculateNewCarry.setApiReference("math/basic/divide_10");

OperationNode cacheResult = new OperationNode("6", "缓存结果", "memory_update",
        Arrays.asList("位1", "位2", "进位", "当前位结果", "新进位"));
cacheResult.setApiReference("memory/cache/store_result");

// 创建控制节点
ControlNode memoryCheckNode = new ControlNode("7", "检查记忆", "conditional");

// 添加节点到图式
digitAdditionSchema.addNode(digitAdditionContext);
digitAdditionSchema.addNode(searchMemory);
digitAdditionSchema.addNode(performAddition);
digitAdditionSchema.addNode(calculateResult);
digitAdditionSchema.addNode(calculateNewCarry);
digitAdditionSchema.addNode(cacheResult);
digitAdditionSchema.addNode(memoryCheckNode);

// 创建边
digitAdditionSchema.addEdge(new SequenceEdge("101", digitAdditionContext, searchMemory, "时序首"));
digitAdditionSchema.addEdge(new SequenceEdge("102", digitAdditionContext, memoryCheckNode, "时序"));
digitAdditionSchema.addEdge(new SequenceEdge("103", digitAdditionContext, performAddition, "时序"));
digitAdditionSchema.addEdge(new SequenceEdge("104", digitAdditionContext, calculateResult, "时序"));
digitAdditionSchema.addEdge(new SequenceEdge("105", digitAdditionContext, calculateNewCarry, "时序"));
digitAdditionSchema.addEdge(new SequenceEdge("106", digitAdditionContext, cacheResult, "时序"));

// 创建执行顺序
digitAdditionSchema.addEdge(new SuccessionEdge("201", searchMemory, memoryCheckNode));
digitAdditionSchema.addEdge(new ConditionalEdge("202", memoryCheckNode, performAddition, "false"));
digitAdditionSchema.addEdge(new SuccessionEdge("203", performAddition, calculateResult));
digitAdditionSchema.addEdge(new SuccessionEdge("204", calculateResult, calculateNewCarry));
digitAdditionSchema.addEdge(new SuccessionEdge("205", calculateNewCarry, cacheResult));

// 创建数据流边
digitAdditionSchema.addEdge(new DataFlowEdge("301", digitAdditionContext, searchMemory, "位1"));
digitAdditionSchema.addEdge(new DataFlowEdge("302", digitAdditionContext, searchMemory, "位2"));
digitAdditionSchema.addEdge(new DataFlowEdge("303", digitAdditionContext, searchMemory, "进位"));
digitAdditionSchema.addEdge(new DataFlowEdge("304", searchMemory, memoryCheckNode, "搜索结果"));
digitAdditionSchema.addEdge(new DataFlowEdge("305", digitAdditionContext, performAddition, "位1"));
digitAdditionSchema.addEdge(new DataFlowEdge("306", digitAdditionContext, performAddition, "位2"));
digitAdditionSchema.addEdge(new DataFlowEdge("307", digitAdditionContext, performAddition, "进位"));
digitAdditionSchema.addEdge(new DataFlowEdge("308", performAddition, calculateResult, "和"));
digitAdditionSchema.addEdge(new DataFlowEdge("309", performAddition, calculateNewCarry, "和"));
digitAdditionSchema.addEdge(new DataFlowEdge("310", calculateResult, cacheResult, "当前位结果"));
digitAdditionSchema.addEdge(new DataFlowEdge("311", calculateNewCarry, cacheResult, "新进位"));
digitAdditionSchema.addEdge(new DataFlowEdge("312", cacheResult, digitAdditionContext, "当前位结果"));
digitAdditionSchema.addEdge(new DataFlowEdge("313", cacheResult, digitAdditionContext, "进位"));
```

这个子图式展示了"计算当前位和进位"操作的内部实现，它包含以下步骤：

1. **搜索记忆**：首先尝试从记忆中搜索结果
2. **检查记忆**：检查是否找到记忆中的结果
3. **执行加法**：如果没有找到记忆中的结果，执行基本的加法操作
4. **计算结果**：计算当前位的结果（对10取模）
5. **计算新进位**：计算新的进位（除以10）
6. **缓存结果**：将结果缓存到记忆中供未来使用

这种方式允许系统在不同的抽象层次上操作，既可以将"计算当前位和进位"视为一个原子操作，也可以将其视为一个可以进一步分解的子图式。

#### 3.2 执行逻辑

在实际执行中，系统会首先尝试从记忆中搜索结果（如9+5=14这样的基本加法事实）：

```java
// 执行逻辑（简化版）
public Object executeCalculateDigitAndCarry(ExecutionContext context) {
    int digit1 = (int) context.getVariable("位1");
    int digit2 = (int) context.getVariable("位2");
    int carry = (int) context.getVariable("进位");

    // 首先尝试从记忆中搜索
    String searchKey = "add_" + digit1 + "_" + digit2 + "_" + carry;
    if (context.hasCachedVariable(searchKey)) {
        // 如果找到缓存结果，直接使用
        Map<String, Object> result = (Map<String, Object>) context.getCachedVariable(searchKey);
        context.setVariable("当前位结果", result.get("digit_result"));
        context.setVariable("进位", result.get("new_carry"));
        return result;
    }

    // 如果没有缓存，执行计算
    int sum = digit1 + digit2 + carry;
    int currentDigitResult = sum % 10;
    int newCarry = sum / 10;

    context.setVariable("当前位结果", currentDigitResult);
    context.setVariable("进位", newCarry);

    // 缓存结果供未来使用
    Map<String, Object> result = new HashMap<>();
    result.put("digit_result", currentDigitResult);
    result.put("new_carry", newCarry);
    context.cacheVariable(searchKey, result);

    return result;
}
```

### 4. 更新结果操作

更新结果操作将当前位的计算结果添加到总结果中，关联到字符串操作API：

```java
// 操作节点定义
OperationNode updateResult = new OperationNode(
    "13",
    "更新结果",
    "data_update",
    Arrays.asList("结果", "当前位结果")
);

// 底层API关联
updateResult.setApiReference("string/basic/prepend");
```

### 5. 移动到下一位操作

移动到下一位操作更新当前位索引，关联到数学运算API：

```java
// 操作节点定义
OperationNode moveToNextDigit = new OperationNode(
    "14",
    "移动到下一位",
    "arithmetic",
    Arrays.asList("当前位索引")
);

// 底层API关联
moveToNextDigit.setApiReference("math/basic/decrement");
```

### 6. 添加最终进位操作

添加最终进位操作处理计算完所有位后可能剩余的进位，关联到字符串操作API：

```java
// 操作节点定义
OperationNode addFinalCarry = new OperationNode(
    "15",
    "添加进位",
    "data_update",
    Arrays.asList("结果", "进位")
);

// 底层API关联
addFinalCarry.setApiReference("string/basic/prepend_int_to_string");
```

### 7. 返回结果操作

返回结果操作返回最终计算结果，关联到变量操作API：

```java
// 操作节点定义
OperationNode returnResult = new OperationNode(
    "17",
    "返回结果",
    "return",
    Arrays.asList("结果")
);

// 底层API关联
returnResult.setApiReference("variable/basic/get");
```

## 四、动态可中断执行引擎

综合优化版本的执行引擎采用动态加载、可中断的设计，支持从图数据库动态构建和执行图式，并能够根据场景变化实时调整执行状态：

```java
public class DynamicSchemaExecutionEngine {
    private GraphDatabaseService graphDb;
    private TaskManager taskManager;
    private MotivationSystem motivationSystem;

    public DynamicSchemaExecutionEngine(GraphDatabaseService graphDb) {
        this.graphDb = graphDb;
        this.taskManager = TaskManager.getInstance();
        this.motivationSystem = MotivationSystem.getInstance();
    }

    /**
     * 从图数据库动态加载图式
     * 不再硬编码创建图式，而是根据存储在图数据库中的结构动态构建
     */
    public ExecutableSchema loadSchemaFromGraph(String schemaName) {
        try (Transaction tx = graphDb.beginTx()) {
            // 创建图式对象
            ExecutableSchema schema = new ExecutableSchema(schemaName);

            // 查询上下文节点
            String query = "MATCH (n:场景 {name: '" + schemaName + "'}) RETURN n";
            Result result = tx.execute(query);
            if (result.hasNext()) {
                Map<String, Object> row = result.next();
                Node contextNode = (Node) row.get("n");
                schema.setContextNode(new ContextNode(contextNode));

                // 查询所有相关节点和边
                loadNodesAndEdges(schema, contextNode);
            }

            tx.commit();
            return schema;
        }
    }

    /**
     * 加载图式的所有节点和边
     */
    private void loadNodesAndEdges(ExecutableSchema schema, Node contextNode) {
        // 加载时序节点
        String query = "MATCH (n:场景)-[r:时序]->(m:场景) WHERE id(n) = " + contextNode.getId() + " RETURN m";
        Result result = graphDb.execute(query);
        while (result.hasNext()) {
            Map<String, Object> row = result.next();
            Node node = (Node) row.get("m");
            schema.addNode(createSchemaNode(node));
        }

        // 加载顺承边
        query = "MATCH (n:场景)-[r:顺承]->(m:场景) WHERE id(n) = " + contextNode.getId() + " RETURN r, m";
        result = graphDb.execute(query);
        while (result.hasNext()) {
            Map<String, Object> row = result.next();
            Relationship rel = (Relationship) row.get("r");
            Node target = (Node) row.get("m");
            schema.addEdge(createSchemaEdge(rel, target));
        }

        // 加载其他类型的边...
    }

    /**
     * 动态执行图式，支持中断和恢复
     */
    public Future<ExecutionResult> executeSchema(String schemaName, Map<String, Object> parameters) {
        // 动态加载图式
        ExecutableSchema schema = loadSchemaFromGraph(schemaName);

        // 创建执行上下文
        ExecutionContext context = new ExecutionContext();
        context.setParameters(parameters);

        // 创建执行任务
        SchemaExecutionTask task = new SchemaExecutionTask(schema, context);

        // 计算任务优先级（基于动机系统）
        int priority = calculateTaskPriority(schema, context);
        task.setPriority(priority);

        // 提交到任务管理器执行
        return taskManager.submitTask(task);
    }

    /**
     * 计算任务优先级
     */
    private int calculateTaskPriority(ExecutableSchema schema, ExecutionContext context) {
        // 基于动机系统计算优先级
        List<Motivation> currentMotivations = motivationSystem.getCurrentMotivations();
        int priority = 0;

        // 计算与当前动机的相关性
        for (Motivation motivation : currentMotivations) {
            double relevance = calculateRelevance(schema, motivation);
            priority += motivation.getStrength() * relevance;
        }

        return priority;
    }

    /**
     * 计算图式与动机的相关性
     */
    private double calculateRelevance(ExecutableSchema schema, Motivation motivation) {
        // 实现相关性计算逻辑
        return 0.5; // 简化实现
    }
}

/**
 * 可中断的图式执行任务
 * 支持暂停、恢复和状态保存
 */
public class SchemaExecutionTask implements Task {
    private String id;
    private ExecutableSchema schema;
    private ExecutionContext context;
    private int priority;
    private volatile boolean interrupted = false;
    private AtomicReference<ExecutionState> state = new AtomicReference<>(ExecutionState.PENDING);
    private Node currentNode; // 当前执行节点

    public SchemaExecutionTask(ExecutableSchema schema, ExecutionContext context) {
        this.id = UUID.randomUUID().toString();
        this.schema = schema;
        this.context = context;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    @Override
    public boolean canExecute() {
        // 检查资源和前置条件
        return true; // 简化实现
    }

    @Override
    public ExecutionResult execute() {
        state.set(ExecutionState.RUNNING);

        try {
            // 获取起始节点
            Node startNode;
            if (currentNode != null) {
                // 从上次暂停点继续
                startNode = currentNode;
            } else {
                // 新任务，从时序首开始
                startNode = getFirstSequenceNode(schema.getContextNode());
            }

            // 执行图式
            Object result = executeNode(startNode);

            // 创建执行结果
            return new ExecutionResult(result, context);
        } catch (InterruptedException e) {
            state.set(ExecutionState.INTERRUPTED);
            return new ExecutionResult(null, context, e);
        } catch (Exception e) {
            state.set(ExecutionState.FAILED);
            return new ExecutionResult(null, context, e);
        } finally {
            if (state.get() == ExecutionState.RUNNING) {
                state.set(ExecutionState.COMPLETED);
            }
        }
    }

    /**
     * 执行节点，支持中断和暂停
     */
    private Object executeNode(Node node) throws InterruptedException {
        // 检查是否被中断
        if (interrupted) {
            throw new InterruptedException("Task was interrupted");
        }

        // 检查是否需要暂停
        if (shouldPause(node)) {
            currentNode = node; // 保存当前节点
            state.set(ExecutionState.PAUSED);
            return null;
        }

        // 保存当前执行状态
        currentNode = node;
        context.setCurrentNode(node);

        // 根据节点类型执行不同的操作
        String nodeType = node.getProperty("node_type").toString();

        Object result = null;

        switch (nodeType) {
            case "OperationNode":
                result = executeOperationNode(node);
                break;
            case "ControlNode":
                result = executeControlNode(node);
                break;
            case "ContextNode":
                result = executeContextNode(node);
                break;
            default:
                throw new IllegalArgumentException("Unknown node type: " + nodeType);
        }

        return result;
    }

    /**
     * 执行操作节点
     */
    private Object executeOperationNode(Node node) throws InterruptedException {
        // 获取操作引用
        String apiReference = node.getProperty("api_reference", "").toString();
        String schemaReference = node.getProperty("schema_reference", "").toString();

        Object result = null;

        if (!apiReference.isEmpty()) {
            // 执行底层API
            Operation operation = OperationRegistry.getOperationByPath(apiReference);
            result = operation.execute(context);
        } else if (!schemaReference.isEmpty()) {
            // 执行子图式
            ExecutableSchema subSchema = SchemaRegistry.getSchema(schemaReference);
            SchemaExecutionTask subTask = new SchemaExecutionTask(subSchema, context.createSubContext());
            result = subTask.execute().getResult();
            context.mergeSubContext(subTask.context);
        } else {
            // 根据节点名称查找操作
            Operation operation = OperationRegistry.getOperationByName(node.getProperty("name").toString());
            if (operation != null) {
                result = operation.execute(context);
            }
        }

        // 获取下一个节点
        Node nextNode = getNextNode(node);
        if (nextNode != null) {
            return executeNode(nextNode);
        }

        return result;
    }

    /**
     * 执行控制节点
     */
    private Object executeControlNode(Node node) throws InterruptedException {
        String controlType = node.getProperty("control_type").toString();

        switch (controlType) {
            case "loop":
                return executeLoopNode(node);
            case "conditional":
                return executeConditionalNode(node);
            default:
                throw new IllegalArgumentException("Unknown control type: " + controlType);
        }
    }

    /**
     * 执行循环节点，支持中断
     */
    private Object executeLoopNode(Node loopNode) throws InterruptedException {
        // 获取循环体节点
        List<Node> bodyNodes = getSequenceNodes(loopNode);

        // 循环执行
        boolean continueLoop = true;
        while (continueLoop && !interrupted) {
            // 执行循环体
            for (Node bodyNode : bodyNodes) {
                // 检查是否被中断
                if (interrupted) {
                    throw new InterruptedException("Task was interrupted during loop execution");
                }

                // 执行循环体节点
                executeNode(bodyNode);

                // 检查是否需要暂停
                if (state.get() == ExecutionState.PAUSED) {
                    return null;
                }
            }

            // 检查循环条件
            continueLoop = checkLoopCondition(loopNode);
        }

        // 获取循环后的下一个节点
        Node nextNode = getNextNode(loopNode);
        if (nextNode != null) {
            return executeNode(nextNode);
        }

        return null;
    }

    /**
     * 执行条件节点
     */
    private Object executeConditionalNode(Node conditionalNode) throws InterruptedException {
        // 评估条件
        boolean condition = evaluateCondition(conditionalNode);

        // 获取对应分支
        Node branchNode = condition ?
                          getThenBranchNode(conditionalNode) :
                          getElseBranchNode(conditionalNode);

        if (branchNode != null) {
            return executeNode(branchNode);
        }

        return null;
    }

    /**
     * 执行上下文节点
     */
    private Object executeContextNode(Node contextNode) throws InterruptedException {
        // 获取时序首节点
        Node startNode = getFirstSequenceNode(contextNode);
        if (startNode != null) {
            return executeNode(startNode);
        }
        return null;
    }

    /**
     * 检查是否需要暂停执行
     */
    private boolean shouldPause(Node node) {
        // 实现暂停条件检查逻辑
        // 例如，检查是否需要等待外部输入或资源
        return false; // 简化实现
    }

    @Override
    public void interrupt() {
        interrupted = true;
    }

    /**
     * 恢复执行
     */
    public Future<ExecutionResult> resume() {
        if (state.get() == ExecutionState.PAUSED) {
            return TaskManager.getInstance().submitTask(this);
        }
        throw new IllegalStateException("Cannot resume task in state: " + state.get());
    }

    /**
     * 获取当前执行状态
     */
    public ExecutionState getState() {
        return state.get();
    }

    /**
     * 执行状态枚举
     */
    public enum ExecutionState {
        PENDING,    // 等待执行
        RUNNING,    // 正在执行
        PAUSED,     // 暂停执行
        COMPLETED,  // 执行完成
        FAILED,     // 执行失败
        INTERRUPTED // 执行被中断
    }

    // 辅助方法：获取下一个节点、评估条件等
    private Node getNextNode(Node node) { /* 实现省略 */ return null; }
    private Node getFirstSequenceNode(Node contextNode) { /* 实现省略 */ return null; }
    private List<Node> getSequenceNodes(Node node) { /* 实现省略 */ return Collections.emptyList(); }
    private boolean checkLoopCondition(Node loopNode) { /* 实现省略 */ return false; }
    private boolean evaluateCondition(Node conditionalNode) { /* 实现省略 */ return false; }
    private Node getThenBranchNode(Node conditionalNode) { /* 实现省略 */ return null; }
    private Node getElseBranchNode(Node conditionalNode) { /* 实现省略 */ return null; }
}

/**
 * 基于动机的任务管理器
 * 支持任务的优先级调度、并发执行和资源管理
 */
public class TaskManager {
    private static TaskManager instance;
    private ExecutorService executorService;
    private PriorityBlockingQueue<Task> taskQueue;
    private Map<String, Future<?>> runningTasks;
    private MotivationSystem motivationSystem;
    private ResourceManager resourceManager;

    private TaskManager() {
        executorService = Executors.newCachedThreadPool();
        taskQueue = new PriorityBlockingQueue<>(100, Comparator.comparing(Task::getPriority).reversed());
        runningTasks = new ConcurrentHashMap<>();
        motivationSystem = MotivationSystem.getInstance();
        resourceManager = ResourceManager.getInstance();

        // 启动任务调度线程
        startTaskScheduler();

        // 注册动机变化监听器
        motivationSystem.addMotivationChangeListener(this::onMotivationChanged);
    }

    public static synchronized TaskManager getInstance() {
        if (instance == null) {
            instance = new TaskManager();
        }
        return instance;
    }

    /**
     * 提交任务到任务队列
     */
    public <T> Future<T> submitTask(Task task) {
        taskQueue.add(task);
        return new TaskFuture<>(task.getId());
    }

    /**
     * 任务调度线程
     */
    private void startTaskScheduler() {
        Thread scheduler = new Thread(() -> {
            while (true) {
                try {
                    // 获取优先级最高的任务
                    Task task = taskQueue.take();

                    // 检查是否可以执行（资源、前置条件等）
                    if (task.canExecute() && resourceManager.acquireResources(task)) {
                        // 提交到线程池执行
                        Future<?> future = executorService.submit(() -> {
                            try {
                                ExecutionResult result = task.execute();
                                handleTaskCompletion(task.getId(), result);
                                return result;
                            } catch (Exception e) {
                                handleTaskFailure(task.getId(), e);
                                throw e;
                            } finally {
                                resourceManager.releaseResources(task);
                            }
                        });

                        // 记录运行中的任务
                        runningTasks.put(task.getId(), future);
                    } else {
                        // 如果不能执行，重新放入队列
                        taskQueue.add(task);
                        Thread.sleep(100); // 避免CPU占用过高
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        });

        scheduler.setDaemon(true);
        scheduler.start();
    }

    /**
     * 处理任务完成
     */
    private void handleTaskCompletion(String taskId, ExecutionResult result) {
        runningTasks.remove(taskId);
        // 通知相关组件任务完成
        notifyTaskCompletion(taskId, result);
    }

    /**
     * 处理任务失败
     */
    private void handleTaskFailure(String taskId, Exception e) {
        runningTasks.remove(taskId);
        // 通知相关组件任务失败
        notifyTaskFailure(taskId, e);
    }

    /**
     * 中断任务
     */
    public boolean interruptTask(String taskId) {
        Future<?> future = runningTasks.get(taskId);
        if (future != null) {
            return future.cancel(true);
        }
        return false;
    }

    /**
     * 动机变化处理
     */
    private void onMotivationChanged(MotivationChangeEvent event) {
        // 获取所有任务
        List<Task> tasks = new ArrayList<>(taskQueue);

        // 根据新的动机状态重新计算任务优先级
        for (Task task : tasks) {
            if (task instanceof SchemaExecutionTask) {
                SchemaExecutionTask schemaTask = (SchemaExecutionTask) task;
                int newPriority = calculateTaskPriority(schemaTask, event.getCurrentMotivations());
                schemaTask.setPriority(newPriority);
            }
        }

        // 重新排序任务队列
        reorderTaskQueue();
    }

    /**
     * 计算任务优先级
     */
    private int calculateTaskPriority(SchemaExecutionTask task, List<Motivation> motivations) {
        // 实现任务优先级计算逻辑
        return 0; // 简化实现
    }

    /**
     * 重新排序任务队列
     */
    private void reorderTaskQueue() {
        List<Task> tasks = new ArrayList<>();
        taskQueue.drainTo(tasks);
        taskQueue.addAll(tasks);
    }

    /**
     * 通知任务完成
     */
    private void notifyTaskCompletion(String taskId, ExecutionResult result) {
        // 实现任务完成通知逻辑
    }

    /**
     * 通知任务失败
     */
    private void notifyTaskFailure(String taskId, Exception e) {
        // 实现任务失败通知逻辑
    }

    /**
     * 任务Future实现
     */
    private class TaskFuture<T> implements Future<T> {
        private final String taskId;

        public TaskFuture(String taskId) {
            this.taskId = taskId;
        }

        @Override
        public boolean cancel(boolean mayInterruptIfRunning) {
            return interruptTask(taskId);
        }

        @Override
        public boolean isCancelled() {
            Future<?> future = runningTasks.get(taskId);
            return future != null && future.isCancelled();
        }

        @Override
        public boolean isDone() {
            Future<?> future = runningTasks.get(taskId);
            return future == null || future.isDone();
        }

        @Override
        public T get() throws InterruptedException, ExecutionException {
            Future<?> future = runningTasks.get(taskId);
            if (future != null) {
                return (T) future.get();
            }
            throw new ExecutionException(new IllegalStateException("Task not found: " + taskId));
        }

        @Override
        public T get(long timeout, TimeUnit unit) throws InterruptedException, ExecutionException, TimeoutException {
            Future<?> future = runningTasks.get(taskId);
            if (future != null) {
                return (T) future.get(timeout, unit);
            }
            throw new ExecutionException(new IllegalStateException("Task not found: " + taskId));
        }
    }
}

/**
 * 资源管理器
 * 管理任务执行所需的资源，避免资源冲突
 */
public class ResourceManager {
    private static ResourceManager instance;
    private Map<String, Integer> availableResources;
    private Map<String, Map<String, Integer>> taskResources; // 任务ID -> (资源名称 -> 数量)

    private ResourceManager() {
        availableResources = new ConcurrentHashMap<>();
        taskResources = new ConcurrentHashMap<>();

        // 初始化可用资源
        initializeResources();
    }

    public static synchronized ResourceManager getInstance() {
        if (instance == null) {
            instance = new ResourceManager();
        }
        return instance;
    }

    /**
     * 初始化可用资源
     */
    private void initializeResources() {
        // 设置系统可用资源
        availableResources.put("memory", 1000); // 内存单位
        availableResources.put("cpu", 8);      // CPU核心
        availableResources.put("io", 10);      // IO操作并发数
    }

    /**
     * 获取任务所需资源
     */
    public synchronized boolean acquireResources(Task task) {
        // 获取任务所需资源
        Map<String, Integer> requiredResources = getRequiredResources(task);

        // 检查资源是否足够
        for (Map.Entry<String, Integer> entry : requiredResources.entrySet()) {
            String resourceName = entry.getKey();
            int requiredAmount = entry.getValue();

            Integer availableAmount = availableResources.get(resourceName);
            if (availableAmount == null || availableAmount < requiredAmount) {
                return false; // 资源不足
            }
        }

        // 分配资源
        for (Map.Entry<String, Integer> entry : requiredResources.entrySet()) {
            String resourceName = entry.getKey();
            int requiredAmount = entry.getValue();

            availableResources.put(resourceName, availableResources.get(resourceName) - requiredAmount);
        }

        // 记录任务占用的资源
        taskResources.put(task.getId(), requiredResources);

        return true;
    }

    /**
     * 释放任务所占用的资源
     */
    public synchronized void releaseResources(Task task) {
        Map<String, Integer> resources = taskResources.remove(task.getId());
        if (resources != null) {
            for (Map.Entry<String, Integer> entry : resources.entrySet()) {
                String resourceName = entry.getKey();
                int amount = entry.getValue();

                availableResources.put(resourceName, availableResources.get(resourceName) + amount);
            }
        }
    }

    /**
     * 获取任务所需资源
     */
    private Map<String, Integer> getRequiredResources(Task task) {
        // 实际实现中应该根据任务类型和特性计算所需资源
        Map<String, Integer> resources = new HashMap<>();
        resources.put("memory", 10); // 简化实现
        resources.put("cpu", 1);
        resources.put("io", 1);
        return resources;
    }
}
```

## 五、综合优化特点与比较

### 1. 综合优化特点

#### 1.1 结构与表示优势

- **树状结构优势**：保留时序主题节点统筹整个时序图式的树状结构，便于查询和管理
- **明确的点类型**：引入明确的点类型定义，增强语义表达
- **显式数据流**：显式表示数据流关系，便于理解和优化
- **节点归属明确**：通过树状结构明确节点归属，解决节点复用问题
- **操作与底层API关联**：操作节点通过引用关联到底层API，避免为每个操作创建实体类
- **操作节点作为子图式**：认识到像"计算当前位和进位"这样的操作节点本身就是一套完整的子图式，可以进一步分解为更基本的操作，直到分解到底层API为止

#### 1.2 执行引擎优势

- **动态图式加载**：从图数据库动态加载图式，而非硬编码创建，增强了系统的灵活性和可扩展性
- **可中断执行**：支持任务的中断、暂停和恢复，可以根据场景变化实时调整执行状态
- **状态保存与恢复**：支持执行状态的保存和恢复，便于实现长时间运行的复杂任务
- **并发执行**：支持多任务并行执行，提高系统效率
- **资源管理**：集成资源管理机制，避免资源冲突和竞争

#### 1.3 动机与认知集成优势

- **动机驱动执行**：集成动机管理系统，任务优先级基于动机强度动态调整
- **记忆搜索集成**：操作执行时可以先尝试从记忆中搜索结果，提高效率
- **机算与人算模式融合**：支持精确执行的机算模式和基于动机和上下文的灵活人算模式

#### 1.4 其他优势

- **兼容现有实现**：与当前实现兼容，便于平滑过渡
- **图式间交互便利**：树状结构便于图式间交互

### 2. 三种方案比较

| 特性 | 当前实现 | 纯优化版本 | 综合优化版本 |
|------|---------|-----------|------------|
| 结构类型 | 树状结构 | 链式结构 | 树状+链式结构 |
| 点类型 | 混用场景节点 | 明确区分类型 | 明确区分类型 |
| 数据流表示 | 隐式表示 | 显式表示 | 显式表示 |
| 节点归属 | 明确（树状） | 不明确（链式） | 明确（树状） |
| 节点复用 | 支持且清晰 | 支持但可能混淆 | 支持且清晰 |
| 查询便利性 | 高（树状） | 低（链式） | 高（树状） |
| 操作实现方式 | 实体类 | 实体类 | API关联 |
| 操作细化到底层API | 不支持 | 部分支持 | 完全支持 |
| 记忆搜索集成 | 低 | 中 | 高 |
| 执行引擎类型 | 硬编码单线程 | 硬编码单线程 | 动态可中断多线程 |
| 动态图式加载 | 不支持 | 不支持 | 支持 |
| 执行中断与恢复 | 不支持 | 不支持 | 支持 |
| 动机驱动执行 | 不支持 | 不支持 | 支持 |
| 资源管理 | 不支持 | 不支持 | 支持 |
| 执行效率 | 中等 | 高 | 非常高 |
| 扩展性 | 中等 | 高 | 非常高 |
| 与现有系统兼容性 | 高 | 低 | 高 |

## 六、示例

### 1. 示例输入

```
操作数1 = "123"
操作数2 = "456"
```

### 2. 执行过程

1. **初始化**：
   - 结果 = ""
   - 进位 = 0
   - 当前位索引 = 2

2. **循环处理**（第一次）：
   - 获取当前位：位1 = 3, 位2 = 6
   - 计算当前位和进位：和 = 3 + 6 + 0 = 9, 当前位结果 = 9, 进位 = 0
   - 更新结果：结果 = "9"
   - 移动到下一位：当前位索引 = 1
   - 循环条件：1 >= 0，继续循环

3. **循环处理**（第二次）：
   - 获取当前位：位1 = 2, 位2 = 5
   - 计算当前位和进位：和 = 2 + 5 + 0 = 7, 当前位结果 = 7, 进位 = 0
   - 更新结果：结果 = "79"
   - 移动到下一位：当前位索引 = 0
   - 循环条件：0 >= 0，继续循环

4. **循环处理**（第三次）：
   - 获取当前位：位1 = 1, 位2 = 4
   - 计算当前位和进位：和 = 1 + 4 + 0 = 5, 当前位结果 = 5, 进位 = 0
   - 更新结果：结果 = "579"
   - 移动到下一位：当前位索引 = -1
   - 循环条件：-1 >= 0，循环结束

5. **处理最终进位**：
   - 条件：进位 = 0 > 0？否，执行跳过进位分支

6. **返回结果**：
   - 结果 = "579"

### 3. 示例输出

```
结果 = "579"
```

## 七、总结

综合优化版本的多位数加法可执行图式结合了当前实现的树状结构优势和优化版本的点边类型定义优势，并引入了操作与底层API关联的新机制，提供了一种既实用又先进的解决方案。这种方案具有以下优势：

1. **查询便利性**：保留树状结构，可以通过时序主题快速查询所有相关步骤
2. **节点归属明确**：通过树状结构明确节点的归属关系，解决节点复用问题
3. **语义表达增强**：引入明确的点类型定义，增强语义表达
4. **数据流显式**：显式表示数据流关系，便于理解和优化
5. **操作原子化**：操作节点通过引用关联到底层API，避免为每个操作创建实体类，大幅提高扩展性
6. **记忆搜索集成**：操作执行时可以先尝试从记忆中搜索结果，如个位数字相加的结果，提高执行效率
7. **兼容现有实现**：与当前实现兼容，便于平滑过渡
8. **图式间交互便利**：树状结构便于图式间交互
9. **操作节点作为子图式**：认识到像"计算当前位和进位"这样的操作节点本身就是一套完整的子图式，可以进一步分解为更基本的操作，直到分解到底层API为止

这种综合方案既考虑了理论上的结构清晰性，也兼顾了实际应用中的存储和查询效率，并解决了为每个操作创建实体类的可扩展性问题。通过将操作与底层API关联，系统只需要实现有限数量的底层API，就能支持数以万计的复杂操作。

特别值得注意的是，本文深入探讨了操作节点作为子图式的概念。在传统的实现中，每个操作节点可能被视为原子的、不可分解的单元。而在本文的综合优化方案中，我们认识到像"计算当前位和进位"这样的操作节点实际上是一套完整的子图式，可以进一步分解为更基本的操作，直到分解到底层API为止。这种层次化的方法允许系统在不同的抽象层次上操作，既可以将操作节点视为原子操作，也可以将其视为可以进一步分解的子图式。这种方式与人类的认知过程非常相似，人类可以将复杂的操作视为一个整体，也可以将其分解为更细粒度的步骤。

在实际执行中，系统可以先尝试从记忆中搜索结果，如果找不到才执行计算，这与人类的计算方式非常相似。例如，在计算9+5时，人类通常不会重新计算，而是直接从记忆中提取结果14。这种方式不仅提高了执行效率，也使系统的行为更接近人类认知。

总之，这种综合方案为可执行图式的设计和实现提供了一个平衡的解决方案，兼顾了理论上的结构清晰性、实际应用中的效率和系统的可扩展性。通过将操作节点视为可分解的子图式，我们能够在不同的抽象层次上操作，从而实现更灵活、更强大的可执行图式系统。

在本文中，我们已经将多位数加法的所有操作节点细化到了底层API级别，包括初始化、获取当前位、计算当前位和进位、更新结果、移动到下一位等操作。每个操作都被分解为更细粒度的原子操作，如字符串处理、数据搜索、基本算术运算等。这种细化方式不仅展示了操作节点的内部结构，还明确了每个原子操作与底层API的关联。这种完整的细化过程使得整个可执行图式的执行过程更加透明和可控，同时也为未来的自动化生成和优化提供了基础。
