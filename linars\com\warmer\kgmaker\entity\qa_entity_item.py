"""
QA Entity Item

This module provides the QA Entity Item class for representing entities in the knowledge graph.
"""

class QAEntityItem:
    """QA Entity Item class for representing entities in the knowledge graph."""
    
    def __init__(self, name="", entity_type=0, color="", r=30, node_id=None):
        """
        Initialize a QA Entity Item.
        
        Args:
            name: The name of the entity
            entity_type: The type of the entity
            color: The color of the entity
            r: The radius of the entity
            node_id: The ID of the entity
        """
        self.name = name
        self.entity_type = entity_type
        self.color = color
        self.r = r
        self.node_id = node_id
    
    def to_dict(self):
        """
        Convert the entity to a dictionary.
        
        Returns:
            A dictionary representation of the entity
        """
        return {
            'name': self.name,
            'entity_type': self.entity_type,
            'color': self.color,
            'r': self.r,
            'node_id': self.node_id
        }
    
    @classmethod
    def from_dict(cls, data):
        """
        Create an entity from a dictionary.
        
        Args:
            data: A dictionary containing entity data
            
        Returns:
            A QAEntityItem instance
        """
        return cls(
            name=data.get('name', ''),
            entity_type=data.get('entity_type', 0),
            color=data.get('color', ''),
            r=data.get('r', 30),
            node_id=data.get('node_id')
        )
