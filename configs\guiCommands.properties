###############################################################################
# Copyright (c) 2009, 2011 The University of Memphis.  All rights reserved. 
# This program and the accompanying materials are made available 
# under the terms of the LIDA Software Framework Non-Commercial License v1.0 
# which accompanies this distribution, and is available at
# http://ccrg.cs.memphis.edu/assets/papers/2010/LIDA-framework-non-commercial-v1.0.pdf
###############################################################################
pauseRunningThreads = edu.memphis.ccrg.lida.Framework.gui.commands.PauseRunningThreadsCommand
quitAll = edu.memphis.ccrg.lida.Framework.gui.commands.QuitAllCommand
resetEnvironment = edu.memphis.ccrg.lida.Framework.gui.commands.ResetEnvironmentCommand
resumeRunningThreads = edu.memphis.ccrg.lida.Framework.gui.commands.ResumeRunningThreadsCommand
setTimeScale = edu.memphis.ccrg.lida.Framework.gui.commands.SetTimeScaleCommand
EnableTicksMode = edu.memphis.ccrg.lida.Framework.gui.commands.EnableTicksModeCommand
AddTicks = edu.memphis.ccrg.lida.Framework.gui.commands.AddTicksCommand