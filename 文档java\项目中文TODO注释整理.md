# 项目中文TODO注释整理

本文档整理了项目中所有包含中文TODO注释的代码片段，按照模块和功能分类进行了整理，并列出了相关文档中的对应优化方案。

## 目录
- [搜索机制相关](#搜索机制相关)
- [图激活扩散相关](#图激活扩散相关)
- [自然语言处理相关](#自然语言处理相关)
- [注意力机制相关](#注意力机制相关)
- [动机与目标处理相关](#动机与目标处理相关)
- [变量处理相关](#变量处理相关)
- [任务处理相关](#任务处理相关)
- [其他功能相关](#其他功能相关)

## 搜索机制相关

### SearchMOM.java
```java
// todo 与pam扩散整合，结合实时反馈，做更智能灵活的搜索
@Override
public List<Task> execute(Operation operation, Term[] args, Memory memory, Timable time) {
    List<Task> tasks = new ArrayList<>();
    if (args[0] instanceof CompoundTerm){
        CompoundTerm ctt = (CompoundTerm) args[0];
        Term[] terms = ctt.term;
        // 可能是语句数或词数
        System.out.println("Search:--------- " + Arrays.toString(terms));
        String sname = "";
        sname = getAnswer();
        try {
            Task task = narsese.parseTask(sname + ".");
            tasks.add(task);
        } catch (Parser.InvalidInputException e) {
            throw new RuntimeException(e);
        }
    }

    // todo 可能是各种模态，如果是纯文本，可直接触发脑内语音或语言。感知数据，则要转语言
    return tasks;
}

private static String getAnswer() {
    String sname = "";
    // 苹果+去哪了，输出场景：(*,苹果,$状态)
    // (^搜索匹配,(*,(*,苹果,$状态),<$状态<->去哪了>)）
    // 搜索匹配条件集，直接用cypher语句，查包含苹果和一个其他元素a的场景，并且元素a与【去哪了】相似
    // todo 拓展为与内容无关，适配各种搜索场景和条件集，组装查询语句
    String cypher = "MATCH (c{name:'苹果'})-[r:arg0]->(a)<-[r1:arg1]-(b)-[r2:相似]->(d{name:'去哪了'}) return a";

    // 实现代码...
}
```

**对应优化方案**：

在《搜索机制作为可执行图式工具》文档中提出了以下优化方案：

1. **与激活扩散的集成**：
   ```java
   public class ActivationGuidedSearch {
       // 基于激活状态构建搜索查询
       public static String buildActivationGuidedQuery(Memory memory, String searchTarget) {
           // 获取当前激活的节点
           List<Node> activatedNodes = getHighlyActivatedNodes(memory);
           // 构建基于激活节点的查询
           // ...
       }
   }
   ```

2. **动态查询构建机制**：
   ```java
   public class DynamicQueryBuilder {
       // 基础查询模板
       private static final Map<String, String> QUERY_TEMPLATES = new HashMap<>();

       // 构建查询参数
       public static Map<String, Object> buildQueryParams(Term[] terms) {
           // 分析terms，提取查询参数
           // ...
       }

       // 根据参数选择并填充查询模板
       public static String buildQuery(Map<String, Object> params) {
           // ...
       }
   }
   ```
```

### SearchSB.java
```java
// 如果没有结果，可能是没有经验记忆，可能是距离太长，可能判断错误、查找错误，
// 只要不是需要凭空归纳，都是可达的，三段论都是一种搜索。其他错误都是经验问题，试错即可。
// todo 从以上找若干中间点，再约搜若干次，找不到就从中间高置信点推理，
//  根据点类型、缺失类型、目标类型，适用不同推理方式。后天图程？
// 推理搜索明确分开，搜索=结构推理。反过来，各种推理都是搜索，只是搜索的方案不同。
// 集中推理，针对一类、一组，进行连续推理。nars类型驱动，pam也是类型驱动。

// todo 可能是各种模态，如果是纯文本，可直接触发脑内语音或语言。感知数据，则要转语言
```

**对应优化方案**：

在《搜索机制作为可执行图式工具》文档中提出了以下优化方案：

1. **三段论推理与图式搜索的双向转换**：
   ```java
   public class SyllogismGraphMapper {
       // 将三段论转换为图搜索查询
       public static GraphQuery syllogismToGraphQuery(Syllogism syllogism) {
           GraphQuery query = new GraphQuery();

           // 根据三段论类型构建不同的图查询
           switch (syllogism.getType()) {
               case DEDUCTION: // 演绎: M->P, S->M => S->P
                   query.addStartNode(syllogism.getS())
                        .addIntermediateNode(syllogism.getM())
                        .addEndNode(syllogism.getP())
                        .setRelationType(RelationType.TRANSITIVE);
                   break;
               // 其他类型...
           }
           return query;
       }
   }
   ```

2. **启发式搜索**：
   ```java
   public class HeuristicSearch {
       // 执行启发式搜索
       public static List<Node> executeHeuristicSearch(Node start, Node goal, Memory memory) {
           // 优先队列，按启发函数值排序
           PriorityQueue<SearchNode> frontier = new PriorityQueue<>(
               Comparator.comparingDouble(SearchNode::getEstimatedCost)
           );

           // 启发函数：估计从当前节点到目标节点的距离
           private static double heuristic(Node current, Node goal) {
               // 基于节点属性的启发函数
               double semanticDistance = calculateSemanticDistance(current, goal);
               double activationFactor = 1.0 - (current.getActivation() * 0.5);
               return semanticDistance * activationFactor;
           }
       }
   }
   ```

### SearchSOS.java
```java
// todo 与pam扩散整合，结合实时反馈，做更智能灵活的搜索

// todo 可能是各种模态，如果是纯文本，可直接触发脑内语音或语言。感知数据，则要转语言
```

**对应优化方案**：

在《搜索机制作为可执行图式工具》文档中提出了以下优化方案：

1. **搜索与激活扩散的集成**：
   ```java
   public class SearchResultActivator {
       // 激活搜索结果
       public static void activateSearchResults(List<Node> searchResults, Memory memory, double initialActivation) {
           // 获取PAM节点结构
           PamNodeStructure pns = memory.getPam().getNodeStructure();

           // 为每个搜索结果创建激活任务
           for (Node result : searchResults) {
               // 创建激活任务
               ActivationTask task = new ActivationTask(
                   result,
                   null,
                   initialActivation,
                   80,  // 搜索结果的优先级较高
                   memory.getTaskSpawner()
               );

               // 提交任务
               memory.getTaskSpawner().addTask(task);
           }
       }
   }
   ```

2. **双向搜索机制**：
   ```java
   public class BidirectionalSearch {
       // 执行双向搜索
       public static List<Node> executeBidirectionalSearch(Node source, Node target, Memory memory) {
           // 从源节点开始的前向搜索
           Set<Node> forwardFrontier = new HashSet<>();
           Map<Node, Node> forwardParents = new HashMap<>();
           forwardFrontier.add(source);
           forwardParents.put(source, null);

           // 从目标节点开始的后向搜索
           Set<Node> backwardFrontier = new HashSet<>();
           Map<Node, Node> backwardParents = new HashMap<>();
           backwardFrontier.add(target);
           backwardParents.put(target, null);

           // 双向搜索实现...
       }
   }
   ```
```

### SearchMOS.java
```java
// todo 拓展为与内容无关，适配各种搜索场景和条件集，组装查询语句
```

**对应优化方案**：

在《搜索机制作为可执行图式工具》文档中提出了以下优化方案：

1. **基于语义的查询构建**：
   ```java
   public class SemanticQueryBuilder {
       // 根据自然语言描述构建查询
       public static String buildQueryFromNL(String naturalLanguage, Memory memory) {
           // 分析自然语言，提取关键实体和关系
           Map<String, Object> semanticElements = analyzeNaturalLanguage(naturalLanguage);

           // 构建查询
           StringBuilder queryBuilder = new StringBuilder("MATCH ");

           // 添加实体节点和关系约束
           // ...

           return queryBuilder.toString();
       }
   }
   ```

2. **查询优化与缓存**：
   ```java
   public class QueryOptimizer {
       // 查询缓存
       private static final Map<String, String> QUERY_CACHE = new ConcurrentHashMap<>();
       private static final int MAX_CACHE_SIZE = 1000;

       // 优化查询
       public static String optimizeQuery(String query) {
           // 检查缓存
           if (QUERY_CACHE.containsKey(query)) {
               return QUERY_CACHE.get(query);
           }

           // 执行查询优化
           String optimizedQuery = query;

           // 添加索引提示和结果数量限制
           // ...

           // 缓存优化后的查询
           QUERY_CACHE.put(query, optimizedQuery);

           return optimizedQuery;
       }
   }
   ```

## 图激活扩散相关

### PamImpl0.java
```java
// 递归遍历ct.term，todo 还要判断位置等
termNames = ct.getTermNames(termNames);
```

**对应优化方案**：

在《图式激活扩散优化-第一部分》文档中提出了以下优化方案：

1. **动态深度阈值**：
   ```java
   private int calculateOptimalDepth(Node node, String linkType, String from) {
       // 基础深度
       int baseDepth = 4;

       // 根据节点类型调整
       if (isHighPriorityNode(node)) {
           baseDepth += 2;  // 高优先级节点允许更深的激活
       }

       // 根据连接类型调整
       switch (linkType) {
           case "相似":
           case "对等":
               baseDepth -= 1;  // 语义关系限制较浅的激活
               break;
           case "顺承":
           case "时序":
               baseDepth += 1;  // 时序关系允许更深的激活
               break;
           // 其他类型...
       }

       // 确保深度在合理范围内
       return Math.max(2, Math.min(baseDepth, 10));
   }
   ```

2. **激活衰减机制**：
   ```java
   private double calculateActivationDecay(double activation, int depth, String linkType) {
       // 基础衰减率
       double baseDecayRate = 0.7;

       // 根据连接类型调整衰减率
       switch (linkType) {
           case "相似":
           case "对等":
               baseDecayRate = 0.6;  // 语义关系衰减更快
               break;
           case "顺承":
           case "时序":
               baseDecayRate = 0.8;  // 时序关系衰减更慢
               break;
       }

       // 应用深度指数衰减
       return activation * Math.pow(baseDecayRate, depth);
   }
   ```

### 自传体场景触发
```java
// todo 目前默认自传体场景，后续要根据场景类型来触发
if (entry.getValue() > 1 && AgentStarter.selfscenemap.containsKey(tnname)) {
    snodes = NeoUtil.getSceneSon(tnname);
    StringBuilder sb = new StringBuilder();
    // 拼接场景，中间要加逗号，前后不用
    for (int i = 0; i < snodes.size(); i++) {
        sb.append(snodes.get(i).getTNname());
        if (i < snodes.size() - 1) {
            sb.append(",");
        }
    }
    // 实现代码...
}
```

**对应优化方案**：

在《图式激活扩散优化-第一部分》文档中提出了以下优化方案：

1. **选择性激活传播**：
   ```java
   private List<Link> selectLinksForPropagation(Set<Link> allLinks, Node sourceNode, String from) {
       // 为所有连接计算传播优先级
       List<ScoredLink> scoredLinks = new ArrayList<>();
       for (Link link : allLinks) {
           double score = calculateLinkScore(link, sourceNode, from);
           scoredLinks.add(new ScoredLink(link, score));
       }

       // 按分数排序
       Collections.sort(scoredLinks, (a, b) -> Double.compare(b.score, a.score));

       // 选择前N个或超过阈值的连接
       int maxLinks = calculateMaxLinks(sourceNode, from);
       double minScore = calculateMinScore(sourceNode, from);

       List<Link> selectedLinks = new ArrayList<>();
       for (int i = 0; i < Math.min(maxLinks, scoredLinks.size()); i++) {
           ScoredLink scoredLink = scoredLinks.get(i);
           if (scoredLink.score >= minScore) {
               selectedLinks.add(scoredLink.link);
           }
       }

       return selectedLinks;
   }
   ```

2. **激活配额管理**：
   ```java
   private class ActivationQuotaManager {
       private final Map<String, Integer> nodeTypeQuotas = new HashMap<>();
       private final Map<String, Integer> fromTypeQuotas = new HashMap<>();
       private final Map<Node, Integer> nodeUsage = new ConcurrentHashMap<>();

       public ActivationQuotaManager() {
           // 设置不同节点类型的配额
           nodeTypeQuotas.put("概念", 20);
           nodeTypeQuotas.put("场景", 30);  // 场景节点有更高的配额
           nodeTypeQuotas.put("动作", 15);

           // 设置不同来源的配额
           fromTypeQuotas.put("listen", 40);
           fromTypeQuotas.put("see", 40);
           fromTypeQuotas.put("pam", 20);
           fromTypeQuotas.put("varwantplan", 50);
       }

       // 获取剩余配额
       public int getRemainingQuota(Node node, String from) {
           // 实现代码...
       }
   }
   ```

## 自然语言处理相关

### LangGSMergeTask.java
```java
// 逐词从节点属性拿到词性，通过词性找到对应的构式，存buffer
// 点投票模式，逐步动态在buffer里组装框架，而不是通过一条边查出所有关系
//		pos = NeoUtil.getNode((String) mm.getProperty("pos"));
//todo 有多种词性的情况，方向等问题
//		links = NeoUtil.getLinks(pos);
```

**对应优化方案**：

在《自然语言编译执行优化方案》文档中提出了以下优化方案：

1. **统一中间表示设计**：
   ```python
   class SyntaxTree:
       def __init__(self):
           self.root = None  # 根节点
           self.nodes = {}   # 节点集合
           self.edges = {}   # 边集合
           self.properties = {}  # 树属性

   class SemanticRepresentation:
       def __init__(self):
           self.intent = None  # 意图
           self.frames = []    # 语义框架集合
           self.entities = {}  # 实体集合
           self.relations = [] # 关系集合
           self.context = {}   # 上下文信息
   ```

2. **改进词法分析**：
   ```python
   def enhance_pos_tagging(tokens):
       # 上下文敏感的词性标注
       for i in range(len(tokens)):
           # 获取当前词的上下文
           context = get_token_context(tokens, i, window_size=2)

           # 处理多词性情况
           if has_multiple_pos(tokens[i]):
               # 基于上下文选择最可能的词性
               tokens[i].pos = disambiguate_pos(tokens[i], context)
   ```

### GrammarAnalyzTask.java
```java
// todo 注意线程周期问题，每个词都一个线程+一个周期=真并行，需要改pam=pam集中分发改各线程并行集中调用
// 这里是常驻线程运行与词语同等数量的周期，外周期=线程池周期调用，内周期=线程内部循环。无论内外，都要等待
// 当前线程繁忙时，线程池invoke方法会等待上一个周期的任务执行完成后，再执行当前周期的任务，然后返回结果
// join()的作用是："等待该线程终止"，在子线程调用，也就是孙线程执行完，才继续执行子线程
```

**对应优化方案**：

在《自然语言编译执行优化方案》文档中提出了以下优化方案：

1. **并行处理优化**：
   ```java
   public class ParallelProcessingManager {
       private final ExecutorService executorService;
       private final int maxThreads;
       private final BlockingQueue<Task> taskQueue;

       public ParallelProcessingManager(int maxThreads) {
           this.maxThreads = maxThreads;
           this.executorService = Executors.newFixedThreadPool(maxThreads);
           this.taskQueue = new LinkedBlockingQueue<>();
       }

       // 批量提交任务
       public <T> List<Future<T>> submitTasks(List<Callable<T>> tasks) {
           try {
               return executorService.invokeAll(tasks);
           } catch (InterruptedException e) {
               Thread.currentThread().interrupt();
               throw new RuntimeException("Task submission interrupted", e);
           }
       }

       // 任务分组处理
       public <T> List<T> processInBatches(List<Callable<T>> tasks, int batchSize) {
           List<T> results = new ArrayList<>();
           for (int i = 0; i < tasks.size(); i += batchSize) {
               int end = Math.min(i + batchSize, tasks.size());
               List<Callable<T>> batch = tasks.subList(i, end);
               try {
                   List<Future<T>> futures = executorService.invokeAll(batch);
                   for (Future<T> future : futures) {
                       results.add(future.get());
                   }
               } catch (Exception e) {
                   throw new RuntimeException("Batch processing failed", e);
               }
           }
           return results;
       }
   }
   ```

2. **任务调度优化**：
   ```java
   public class TaskScheduler {
       private final PriorityBlockingQueue<PrioritizedTask> taskQueue;
       private final ThreadPoolExecutor executor;

       public TaskScheduler(int corePoolSize, int maxPoolSize) {
           taskQueue = new PriorityBlockingQueue<>();
           executor = new ThreadPoolExecutor(
               corePoolSize, maxPoolSize, 60, TimeUnit.SECONDS,
               new LinkedBlockingQueue<>(),
               new ThreadPoolExecutor.CallerRunsPolicy());
       }

       // 添加任务并设置优先级
       public <T> Future<T> submit(Callable<T> task, int priority) {
           PrioritizedTask<T> prioritizedTask = new PrioritizedTask<>(task, priority);
           return executor.submit(prioritizedTask);
       }

       // 优先级任务包装类
       private class PrioritizedTask<T> implements Callable<T>, Comparable<PrioritizedTask> {
           private final Callable<T> task;
           private final int priority;

           public PrioritizedTask(Callable<T> task, int priority) {
               this.task = task;
               this.priority = priority;
           }

           @Override
           public T call() throws Exception {
               return task.call();
           }

           @Override
           public int compareTo(PrioritizedTask other) {
               return Integer.compare(other.priority, this.priority); // 高优先级先执行
           }
       }
   }
   ```

### SemanticAnalyzTask0.java
```java
// todo 20250421，基本确定短语结构形式，暂时也不依赖词性，词性可以蕴含在nl里，narsese自身可处理

// 语法搜pos，不同层次+不同时空，如果整合为一个线程，太拥挤，还要两个分别循环。语义复杂很多
// 语法线程会搜到带属性变量语义嵌套，语义会兼顾单字扩散（可能分开），语义也会搜到带概念变量嵌套
// 三种模式，#加法=概念属性变量+语义框架嵌套，#形容词=概念+元素，**adj=跨模变量+接地属性，**VP=语法纯结构+属性+嵌套
// 形容词需要理解，没有直接接地，除了颜色形状，副词有运动属性。##xy。跨模变量=是动物都可有，vp等纯语法结构，以具体模式存在
// 可以直接关联语义结构与vp等，但嵌套方式要同构，可缺省同构，如只需知道vp，无需完全匹配完整。缺省同构与完全同构分开
// 整体嵌套语法结构也保存，用进废退，要求低于一定复杂度。语义同理，太复杂语义经验也没意义，要分解精简，抽取抽象模式
// 整体义法结构关联作用=加速分析，特别是常见结构和内涵，同结构新内涵，同内涵新结构。义法结构杂糅类似xy，范围过大
// 义法结构杂糅无意义，义法自身体系内变量才有意义。其他跨模变量+跨模结构，自传体本身包含多模态，内涵本身杂糅，不是内涵和结构杂糅
// 结构本身杂糅，时空结构应该也不能杂糅，时间内部关系，空间内部关系

// todo 整合到pam
//						pam.getListener().receivePercept(link, ModuleName.UnderstandGraph);
```

**对应优化方案**：

在《自然语言编译执行优化方案》文档中提出了以下优化方案：

1. **语义分析优化**：
   ```python
   class SemanticAnalyzer:
       def __init__(self, knowledge_base):
           self.knowledge_base = knowledge_base
           self.frame_repository = FrameRepository()
           self.context_manager = ContextManager()

       def analyze(self, syntax_tree):
           # 创建语义表示
           semantic_rep = SemanticRepresentation()

           # 提取意图
           semantic_rep.intent = self.extract_intent(syntax_tree)

           # 提取实体和关系
           self.extract_entities_and_relations(syntax_tree, semantic_rep)

           # 匹配语义框架
           self.match_semantic_frames(syntax_tree, semantic_rep)

           # 整合上下文信息
           self.context_manager.enrich_with_context(semantic_rep)

           return semantic_rep
   ```

2. **短语结构分析**：
   ```python
   class PhraseStructureAnalyzer:
       def __init__(self):
           self.grammar_rules = self.load_grammar_rules()
           self.phrase_patterns = self.load_phrase_patterns()

       def analyze(self, tokens):
           # 构建基本短语结构
           phrases = self.identify_basic_phrases(tokens)

           # 处理嵌套短语
           tree = self.build_phrase_hierarchy(phrases)

           # 处理特殊结构
           self.handle_special_structures(tree)

           return tree

       def identify_basic_phrases(self, tokens):
           phrases = []
           i = 0
           while i < len(tokens):
               # 匹配最长的可能短语
               matched_phrase = None
               for pattern in self.phrase_patterns:
                   if self.matches_pattern(tokens[i:], pattern):
                       matched_phrase = self.create_phrase(tokens[i:i+len(pattern)], pattern.type)
                       phrases.append(matched_phrase)
                       i += len(pattern)
                       break

               # 如果没有匹配到短语，则将单词作为原子短语
               if matched_phrase is None:
                   phrases.append(self.create_atomic_phrase(tokens[i]))
                   i += 1

           return phrases
   ```

### VisionAnalyzTask.java
```java
// todo 优先在预测集内扩散激活，甚至调起注意力动机
actsence(link,null);
```

### LanGenImpl.java
```java
// todo 内部语言重理解，重新输入pam走激活过程，主要是知道自己说了啥和正确与否
System.out.println("新词-----------" + name);
```

## 注意力机制相关

### NeighborhoodAttentionCodelet.java
```java
public void bindContent(String label) {
    // todo 自上而下 自下而上 注意 图数据
    Node linkable = AgentStarter.pam.getNode(label);
    if (linkable == null) {
        linkable = nodeStructure.getNeoNode(label);
    }

    if (linkable != null) {
        soughtContent.addDefaultNode(linkable);
//            System.out.println("soughtContent: " + linkable.getName());
    }else{
        logger.log(Level.WARNING, "could not find node with label: {0} in global initializer", label);
    }
}

@Override
public boolean bufferContainsSoughtContent(WorkspaceBuffer buffer) {
    NodeStructure model = (NodeStructure) buffer.getBufferContent(null);
    // todo soughtContent是自上而下，自下而上靠激活度=信息刺激
    for (Linkable ln : soughtContent.getLinkables()) {
        if (!model.containsLinkable(ln)) {
            return false;
        }
    }
    logger.log(Level.FINEST, "Attn codelet {1} found sought content",
            new Object[]{TaskManager.getCurrentTick(), this});
    return true;
}
```

### AttentionCodeletModule.java
```java
@Override
public void receivePreafference(NodeStructure addSet, NodeStructure deleteSet) {
    // TODO Receive results from Action Selection and create AttentionCodelets
    // We need to figure out how to create coalitions and detect that something was
    // "deleted"
}
```

## 动机与目标处理相关

### ProcessGoal.java
```java
// todo 重复目标，判断是否已被修订
if (oldGoalT != null && !goal.isRevisied && revisible(goal, oldGoal, nar.narParameters)) {
    // 实现代码...
}

// todo 一般只执行一个满足操作，不能所有都执行
// 动机竞争所在，一个结果，对应多个备选动作
// 根据激励度和紧急度的权衡，判断执行时机
// todo 构建与执行分开，这里是顺推完成后的反推，从本能到中间动作
// 案例：听到问题--回答问题--满足，听到问题+不开心--不想回答--满足

// todo 直接查图谱
if (AgentStarter.actmap.containsKey(tt.toString()) || tt.toString().equals("回应")){
    isAction = true;
    break;
}

// todo 单词项单前提的状态或操作对等词（如【坐】关联【^坐】）
// 也要经过下面的前提结论匹配？若非具身操作，不能执行。直接提升为目标后，由目标自己处理
```

### Memory.java
```java
// todo 因为常驻动机线程直接处理，不调用mem的循环，所以不触及。是否分内外待定，看情绪情感等方面
memory.internalExperienceBuffer.putIn(t);

//todo 遍历term的所有termlinks，假如termlink的"cpstr"属性存在且不为空，则对termlink进行maketask
memory.globalBuffer.putIn(t);
```

### WorkspaceImpl.java
```java
// 都是单项，且有变量单项，没意义？实例化太多，至少两个前提，才能推理，特别是动机？如：$命令--》好状态。
// 这个可以，至少有一个限定：（回应，$问题）--》好状态。命令本身也是限定？
if (num == 0 && term.hasVar()) return;

// 内部时间
final Stamp stamp = new Stamp(nar.time() , null, memory.newStampSerial(),
        nar.narParameters.DURATION);
// 转nars的task，进入数值计算和推理
final Sentence sentence = new Sentence(term, punctuation,
        new TruthValue((float) 1.0, 0.9, nar.narParameters), stamp);
// todo 任务预算，要根据动机激活激励等实时算，在bag里排序
final BudgetValue budget = new BudgetValue(0.8f, 0.5f, 1, nar.narParameters);
Task task = new Task(sentence, budget, Task.EnumType.INPUT);
```

## 变量处理相关

### Sentence.java
```java
//  Variable name normalization 变量名称规范化
// TODO move this to Concept method, like cloneNormalized() 移动到Concept方法，如cloneNormalized()
//        term = getTVar(_content, normalize, newTerm);
term = _content;
```

### LearnTask.java
```java
// todo 方法名等也适用变量句，后续再优化
// 识别到关注的标志词，如果标志后信息在原始输入队列中，可开始构建
// 标志词形式是（*，教你（*，26，加，8）），需要将标志词后信息拼接为：教你26加8，然后判断
String attentStr = node.getStrs();
if (inputQueueStr.contains(attentStr)){
    System.out.println("conceptNodes: " + node);
    // 截取标志词后信息，作为方法名，需实时理解哪些是方法名，哪些是其他，过滤掉其他
    // todo 识别哪些是参数，构建参数表，带符变量式描述，直接构建即可
    String methodName = "";
    CompoundTerm compoundTerm = null;
    try {
        compoundTerm = (CompoundTerm) narsese.parseTerm(node.getTNname());
    } catch (Parser.InvalidInputException e) {
        throw new RuntimeException(e);
    }
    Term[] terms = compoundTerm.getTerms();
    methodName = terms[1].toString();
}
```

## 任务处理相关

### GlobalAnticipation.java
```java
Task t=new Task(s2,new BudgetValue(AgentStarter.nar.narParameters.DEFAULT_JUDGMENT_PRIORITY,AgentStarter.nar.narParameters.DEFAULT_JUDGMENT_DURABILITY,s2.truth, AgentStarter.nar.narParameters), Task.EnumType.INPUT);
derivetasks.add(t);
} //todo use derived task with revision instead
```

## 其他功能相关

### TermNodeImpl.java
```java
public class TermNodeImpl extends ActivatibleImpl implements Node {
    public static final Logger logger = Logger.getLogger(TermNodeImpl.class
            .getCanonicalName());
    // todo 信息来源 如听觉 视觉
    public int nodeId;
    public ExtendedId extendedId;
    public String label ="word0";
    public String factoryName;
    public String toStringName;

    public String TNname;

    private String bcastid = "...";
}
```

### CueBackgroundTask.java
```java
// Current impl. of episodic memory only processes Nodes.
// TODO add links when episodic memory supports them
```

### CompoundTerm.java
```java
/**
 * list of (direct) term
 */
// TODO make final again
public final Term[] term;

/**
 * syntactic complexity of the compound, the sum of those of its term plus 1
 * 化合物的句法复杂度，该术语的总和加1
 */
// TODO make final again
public short complexity;
```

### ListenDetector.java
```java
// 对主人尊敬+爱戴--》维护+祝福+顺从
// 逐字听到=循环即是逐字，能不能理解=意象都是一样的，理解=信息齐全=概念+搭配+来龙去脉
// 意象与理解分离，忠实展现意象+尽量完善属性，知道自己理解=能联想+能预测+能描述属性
// 每个字词都附属于特定说话者，字词被识别出来，说话者也识别出来了，并行且同时，可再提前？
// 针对听者，在这阶段应该还没被识别，需要更深入的理解
// 连成一句话 = 已有场景即时激活，无场景 = 尝试创建新场景 = 借助类似场景
if(message != null && !"".equals(message)){
//			excite("listen", 0.82, "listen");
//			PamNode msg = new PamNodeImpl();
//			msg.setName(message);
//			pam.receiveExcitation(msg, 0.85, "listen");

    System.out.println("听到---------------" + message);
    excite(message, 0.95, "listen");
    excite("(*," + message + ",听到)", 0.95, "listen");

    //todo 后期构建自传体系，把听到的信息存储在自传体系中，以便后期的提高感知激活
//			excite("柯东", 0.93, "listen");

    // 意象本体+意象内容+意象属性=声音本身+声音内容+声音来源

    message = "";
//			excite("说", 0.93, "listen");
}
```

### PAMemoryImpl.java
```java
/**
 * Default constructor.
 */
public PAMemoryImpl() {
    super();
//		addInternalLinkCategory(DEFAULT_LINK_CATEGORY);
//		addInternalLinkCategory(LATERAL_LINK_CATEGORY); //TODO add back in for a release
//		addInternalLinkCategory(PARENT_LINK_CATEGORY);
//		addInternalLinkCategory(FEATURE_LINK_CATEGORY);
}
```
